module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},87485,(e,t,r)=>{},64019,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>k,"routeModule",()=>j,"serverHooks",()=>A,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>N],64019);var t=e.i(47909),r=e.i(74017),n=e.i(96250),a=e.i(59756),o=e.i(61916),i=e.i(69741),s=e.i(16795),l=e.i(87718),u=e.i(95169),p=e.i(47587),d=e.i(66012),c=e.i(70101),x=e.i(26937),f=e.i(10372),m=e.i(93695);e.i(52474);var h=e.i(220);e.s(["GET",()=>y,"PATCH",()=>b,"runtime",()=>w],44801);var g=e.i(89171),R=e.i(15270),v=e.i(65800);let w="nodejs";async function y(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,v.verifyToken)(t):null;if(!r)return g.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role)return g.NextResponse.json({error:"Forbidden"},{status:403});let n=await R.prisma.organization.findUnique({where:{id:r.organizationId},select:{settings:!0}}),a=n?.settings?.emailConfig||{};return g.NextResponse.json({emailConfig:a})}async function b(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,v.verifyToken)(t):null;if(!r)return g.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role)return g.NextResponse.json({error:"Forbidden"},{status:403});let n=await e.json().catch(()=>null);if(!n||"object"!=typeof n)return g.NextResponse.json({error:"Invalid body"},{status:400});let a=function(e,t){let r={};for(let n of t)r[n]=e[n];return r}(n,["webhookSecret","routingMap","defaultProjectId","defaultDepartmentId","rateLimitPerHour","smtp"]);if(a.routingMap&&"object"!=typeof a.routingMap)return g.NextResponse.json({error:"routingMap must be an object"},{status:400});if(a.smtp&&"object"!=typeof a.smtp)return g.NextResponse.json({error:"smtp must be an object"},{status:400});let o=await R.prisma.organization.findUnique({where:{id:r.organizationId},select:{settings:!0}}),i=function(e,t){let r=e&&"object"==typeof e?{...e}:{},n={...r.emailConfig||{}};for(let e of Object.keys(t))null!==t[e]&&void 0!==t[e]&&(n[e]=t[e]);return r.emailConfig=n,r}(o?.settings,a);return await R.prisma.organization.update({where:{id:r.organizationId},data:{settings:i}}),g.NextResponse.json({ok:!0})}var E=e.i(44801);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/email/config/route",pathname:"/api/email/config",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/email/config/route.ts",nextConfigOutput:"export",userland:E}),{workAsyncStorage:C,workUnitAsyncStorage:N,serverHooks:A}=j;function k(){return(0,n.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:N})}async function q(e,t,n){var g;let R="/api/email/config/route";R=R.replace(/\/index$/,"")||"/";let v=await j.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:b,isDraftMode:E,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:A,revalidateOnlyGenerated:k,resolvedPathname:q}=v,T=(0,i.normalizeAppPath)(R),P=!!(C.dynamicRoutes[T]||C.routes[q]);if(P&&!E){let e=!!C.routes[q],t=C.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let I=null;!P||j.isDev||E||(I="/index"===(I=q)?"/":I);let O=!0===j.isDev||!P,M=P&&!O,U=e.method||"GET",_=(0,o.getTracer)(),H=_.getActiveScopeSpan(),S={params:y,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=b.experimental)?void 0:g.cacheLife,isRevalidate:M,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>j.onRequestError(e,t,n,N)},sharedContext:{buildId:w}},D=new s.NodeNextRequest(e),F=new s.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>j.handle(z,S).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=_.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${U} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),s=async o=>{var s,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&A&&k&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await i(o);e.fetchMetrics=S.renderOpts.fetchMetrics;let l=S.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let u=S.renderOpts.collectedTags;if(!P)return await (0,d.sendResponse)(D,F,s,S.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(s.headers);u&&(t[f.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==S.renderOpts.collectedRevalidate&&!(S.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&S.renderOpts.collectedRevalidate,n=void 0===S.renderOpts.collectedExpire||S.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:S.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:A})},N),t}},m=await j.handleResponse({req:e,nextConfig:b,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:k,responseGenerator:u,waitUntil:n.waitUntil});if(!P)return null;if((null==m||null==(s=m.value)?void 0:s.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),E&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&P||g.delete(f.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,d.sendResponse)(D,F,new Response(m.value.body,{headers:g,status:m.value.status||200})),null};H?await s(H):await _.withPropagatedContext(e.headers,()=>_.trace(u.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},s))}catch(t){if(H||t instanceof m.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:A})}),P)throw t;return await (0,d.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__0ecae6cc._.js.map