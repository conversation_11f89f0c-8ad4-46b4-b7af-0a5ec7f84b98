{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/edge-wrapper_0b0266f1.js", "server/edge/chunks/[root-of-the-server]__db54bf8a._.js", "server/edge/chunks/turbopack-edge-wrapper_5b67d8fb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/WeCare/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "dgOlDWdH7N7b8aVOV6O_X", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GGrCW1bnFvPVoYugdOQkarZqhAXTm03FdCZPrNbwLLY=", "__NEXT_PREVIEW_MODE_ID": "6038c76f307d5b1aadeb2359014476f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a7611a2c068d192d17f71f433490fb026c2ef91bdba5bb74c46018489fcb0f30", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46f49dbf793482a9c000d06901472a990d2542683a1777c9f3d900eb3c9f967c"}}}, "instrumentation": null, "functions": {}}