module.exports=[96556,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DynamicServerError:function(){return o},isDynamicServerError:function(){return i}});let n="DYNAMIC_SERVER_USAGE";class o extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},60312,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{StaticGenBailoutError:function(){return o},isStaticGenBailoutError:function(){return i}});let n="NEXT_STATIC_GEN_BAILOUT";class o extends Error{constructor(...e){super(...e),this.code=n}}function i(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===n}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},13091,(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===o}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{isHangingPromiseRejectionError:function(){return n},makeDevtoolsIOAwarePromise:function(){return u},makeHangingPromise:function(){return c}});let o="HANGING_PROMISE_REJECTION";class i extends Error{constructor(e,t){super(`During prerendering, ${t} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${t} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${e}".`),this.route=e,this.expression=t,this.digest=o}}let a=new WeakMap;function c(e,t,r){if(e.aborted)return Promise.reject(new i(t,r));{let n=new Promise((n,o)=>{let c=o.bind(null,new i(t,r)),s=a.get(e);if(s)s.push(c);else{let t=[c];a.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return n.catch(s),n}}function s(){}function u(e){return new Promise(t=>{setTimeout(()=>{t(e)},0)})}},17491,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{METADATA_BOUNDARY_NAME:function(){return n},OUTLET_BOUNDARY_NAME:function(){return i},ROOT_LAYOUT_BOUNDARY_NAME:function(){return a},VIEWPORT_BOUNDARY_NAME:function(){return o}});let n="__next_metadata_boundary__",o="__next_viewport_boundary__",i="__next_outlet_boundary__",a="__next_root_layout_boundary__"},61933,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{atLeastOneTask:function(){return i},scheduleImmediate:function(){return o},scheduleOnNextTick:function(){return n},waitAtLeastOneReactRenderTask:function(){return a}});let n=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},o=e=>{setImmediate(e)};function i(){return new Promise(e=>o(e))}function a(){return new Promise(e=>setImmediate(e))}},49640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{BailoutToCSRError:function(){return o},isBailoutToCSRError:function(){return i}});let n="BAILOUT_TO_CLIENT_SIDE_RENDERING";class o extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=n}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}},50640,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"InvariantError",{enumerable:!0,get:function(){return n}});class n extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},60384,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{Postpone:function(){return S},PreludeState:function(){return H},abortAndThrowOnSynchronousRequestDataAccess:function(){return v},abortOnSynchronousPlatformIOAccess:function(){return R},accessedDynamicData:function(){return I},annotateDynamicAccess:function(){return C},consumeDynamicAccess:function(){return $},createDynamicTrackingState:function(){return m},createDynamicValidationState:function(){return y},createHangingInputAbortSignal:function(){return U},createRenderInBrowserAbortSignal:function(){return B},delayUntilRuntimeStage:function(){return J},formatDynamicAPIAccesses:function(){return M},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return k},isPrerenderInterruptedError:function(){return N},logDisallowedDynamicError:function(){return F},markCurrentScopeAsDynamic:function(){return g},postponeWithTracking:function(){return A},throwIfDisallowedDynamic:function(){return z},throwToInterruptStaticGeneration:function(){return b},trackAllowedDynamicAccess:function(){return q},trackDynamicDataInDynamicRender:function(){return E},trackSynchronousPlatformIOAccessInDev:function(){return D},trackSynchronousRequestDataAccessInDev:function(){return w},useDynamicRouteParams:function(){return W},warnOnSyncDynamicError:function(){return O}});let n=function(e){return e&&e.__esModule?e:{default:e}}(e.r(717)),o=e.r(96556),i=e.r(60312),a=e.r(32319),c=e.r(56704),s=e.r(13091),u=e.r(17491),l=e.r(61933),d=e.r(49640),f=e.r(50640),p="function"==typeof n.default.unstable_postpone;function m(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicErrorWithStack:null}}function y(){return{hasSuspenseAboveBody:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasAllowedDynamic:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function g(e,t,r){if(t)switch(t.type){case"cache":case"unstable-cache":case"private-cache":return}if(!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t)switch(t.type){case"prerender-ppr":return A(e.route,r,t.dynamicTracking);case"prerender-legacy":t.revalidate=0;let n=Object.defineProperty(new o.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}function b(e,t,r){let n=Object.defineProperty(new o.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function E(e){switch(e.type){case"cache":case"unstable-cache":case"private-cache":return}}function _(e,t,r){let n=x(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let o=r.dynamicTracking;o&&o.dynamicAccesses.push({stack:o.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function R(e,t,r,n){let o=n.dynamicTracking;_(e,t,n),o&&null===o.syncDynamicErrorWithStack&&(o.syncDynamicErrorWithStack=r)}function D(e){e.prerenderPhase=!1}function v(e,t,r,n){if(!1===n.controller.signal.aborted){_(e,t,n);let o=n.dynamicTracking;o&&null===o.syncDynamicErrorWithStack&&(o.syncDynamicErrorWithStack=r)}throw x(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function O(e){e.syncDynamicErrorWithStack&&console.error(e.syncDynamicErrorWithStack)}let w=D;function S({reason:e,route:t}){let r=a.workUnitAsyncStorage.getStore();A(t,e,r&&"prerender-ppr"===r.type?r.dynamicTracking:null)}function A(e,t,r){(function(){if(!p)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(T(e,t))}function T(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function k(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&P(e.message)}function P(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===P(T("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let j="NEXT_PRERENDER_INTERRUPTED";function x(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=j,t}function N(e){return"object"==typeof e&&null!==e&&e.digest===j&&"name"in e&&"message"in e&&e instanceof Error}function I(e){return e.length>0}function $(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function M(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function B(){let e=new AbortController;return e.abort(Object.defineProperty(new d.BailoutToCSRError("Render in Browser"),"__NEXT_ERROR_CODE",{value:"E721",enumerable:!1,configurable:!0})),e.signal}function U(e){switch(e.type){case"prerender":case"prerender-runtime":let t=new AbortController;if(e.cacheSignal)e.cacheSignal.inputReady().then(()=>{t.abort()});else{let r=(0,a.getRuntimeStagePromise)(e);r?r.then(()=>(0,l.scheduleOnNextTick)(()=>t.abort())):(0,l.scheduleOnNextTick)(()=>t.abort())}return t.signal;case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"private-cache":case"unstable-cache":return}}function C(e,t){let r=t.dynamicTracking;r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function W(e){let t=c.workAsyncStorage.getStore(),r=a.workUnitAsyncStorage.getStore();if(t&&r)switch(r.type){case"prerender-client":case"prerender":{let o=r.fallbackRouteParams;o&&o.size>0&&n.default.use((0,s.makeHangingPromise)(r.renderSignal,t.route,e));break}case"prerender-ppr":{let n=r.fallbackRouteParams;if(n&&n.size>0)return A(t.route,e,r.dynamicTracking);break}case"prerender-runtime":throw Object.defineProperty(new f.InvariantError(`\`${e}\` was called during a runtime prerender. Next.js should be preventing ${e} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E771",enumerable:!1,configurable:!0});case"cache":case"private-cache":throw Object.defineProperty(new f.InvariantError(`\`${e}\` was called inside a cache scope. Next.js should be preventing ${e} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E745",enumerable:!1,configurable:!0})}}let G=/\n\s+at Suspense \(<anonymous>\)/,L=RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at ${u.ROOT_LAYOUT_BOUNDARY_NAME} \\([^\\n]*\\)`),X=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),Y=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),V=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function q(e,t,r,n){if(!V.test(t)){if(X.test(t)){r.hasDynamicMetadata=!0;return}if(Y.test(t)){r.hasDynamicViewport=!0;return}if(L.test(t)){r.hasAllowedDynamic=!0,r.hasSuspenseAboveBody=!0;return}else if(G.test(t)){r.hasAllowedDynamic=!0;return}else{if(n.syncDynamicErrorWithStack)return void r.dynamicErrors.push(n.syncDynamicErrorWithStack);let o=function(e,t){let r=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.stack=r.name+": "+e+t,r}(`Route "${e.route}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);return void r.dynamicErrors.push(o)}}}var H=function(e){return e[e.Full=0]="Full",e[e.Empty=1]="Empty",e[e.Errored=2]="Errored",e}({});function F(e,t){console.error(t),e.dev||(e.hasReadableErrorStacks?console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \`next dev\`, then open "${e.route}" in your browser to investigate the error.`):console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:
  - Start the app in development mode by running \`next dev\`, then open "${e.route}" in your browser to investigate the error.
  - Rerun the production build with \`next build --debug-prerender\` to generate better stack traces.`))}function z(e,t,r,n){if(0!==t){if(r.hasSuspenseAboveBody)return;if(n.syncDynamicErrorWithStack)throw F(e,n.syncDynamicErrorWithStack),new i.StaticGenBailoutError;let o=r.dynamicErrors;if(o.length>0){for(let t=0;t<o.length;t++)F(e,o[t]);throw new i.StaticGenBailoutError}if(r.hasDynamicViewport)throw console.error(`Route "${e.route}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`),new i.StaticGenBailoutError;if(1===t)throw console.error(`Route "${e.route}" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`),new i.StaticGenBailoutError}else if(!1===r.hasAllowedDynamic&&r.hasDynamicMetadata)throw console.error(`Route "${e.route}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`),new i.StaticGenBailoutError}function J(e,t){return e.runtimeStagePromise?e.runtimeStagePromise.then(()=>t):t}}];

//# sourceMappingURL=node_modules_next_dist_3bd4d890._.js.map