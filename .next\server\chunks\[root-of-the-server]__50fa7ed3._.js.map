{"version": 3, "sources": ["turbopack:///[project]/lib/prisma.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/src/app/api/users/[id]/route.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as { prisma?: PrismaClient }\n\nexport const prisma =\n  globalForPrisma.prisma ?? new PrismaClient({\n    log: ['warn', 'error'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"export\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/users/[id]/route\",\n        pathname: \"/api/users/[id]\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/users/[id]/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/users/[id]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { verifyToken } from '@/lib/auth-new'\n\nexport const runtime = 'nodejs'\n\nexport async function PATCH(req: NextRequest, context: { params: Promise<{ id: string }> }) {\n  const token = req.cookies.get('auth-token')?.value\n  const admin = token ? verifyToken(token) : null\n  if (!admin) return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n  if (admin.role !== 'ADMIN') return NextResponse.json({ error: 'Forbidden' }, { status: 403 })\n\n  const body = await req.json()\n  const { role, isActive, projectIds, departmentIds, fullName, permissions } = body || {}\n\n  // Verify user belongs to same org\n  const { params } = context\n  const { id } = await params\n  const target = await prisma.user.findUnique({ where: { id } })\n  if (!target || target.organizationId !== admin.organizationId) return NextResponse.json({ error: 'Not found' }, { status: 404 })\n\n  // Capture \"before\" state for audit\n  const beforeProjects = await prisma.userProject.findMany({ where: { userId: target.id }, select: { projectId: true } })\n  const beforeDepartments = await prisma.userDepartment.findMany({ where: { userId: target.id }, select: { departmentId: true } })\n  const beforePerms = await prisma.userPermission.findMany({ where: { userId: target.id }, select: { permission: true } })\n  const beforeProjectIds = beforeProjects.map(p => p.projectId)\n  const beforeDepartmentIds = beforeDepartments.map(d => d.departmentId)\n  const beforePermissionKeys = beforePerms.map(p => p.permission)\n\n  // Update simple fields\n  const updated = await prisma.user.update({\n    where: { id },\n    data: {\n      ...(fullName !== undefined ? { fullName } : {}),\n      ...(typeof isActive === 'boolean' ? { isActive } : {}),\n      ...(role && ['ADMIN', 'MANAGER', 'AGENT', 'CUSTOMER'].includes(role) ? { role } : {}),\n    },\n  })\n\n  // Replace project memberships if provided\n  if (Array.isArray(projectIds)) {\n    await prisma.userProject.deleteMany({ where: { userId: updated.id } })\n    const projects = await prisma.project.findMany({ where: { id: { in: projectIds }, organizationId: admin.organizationId }, select: { id: true } })\n    for (const p of projects) {\n      await prisma.userProject.create({ data: { userId: updated.id, projectId: p.id } })\n    }\n  }\n\n  // Replace department memberships if provided\n  if (Array.isArray(departmentIds)) {\n    await prisma.userDepartment.deleteMany({ where: { userId: updated.id } })\n    const departments = await prisma.department.findMany({ where: { id: { in: departmentIds }, organizationId: admin.organizationId }, select: { id: true } })\n    for (const d of departments) {\n      await prisma.userDepartment.create({ data: { userId: updated.id, departmentId: d.id } })\n    }\n  }\n\n  // Replace permissions if provided (array of PermissionKey)\n  if (Array.isArray(permissions)) {\n    await prisma.userPermission.deleteMany({ where: { userId: updated.id } })\n    const unique = Array.from(new Set(permissions.filter((p: any) => typeof p === 'string')))\n    for (const key of unique) {\n      await prisma.userPermission.create({\n        data: { userId: updated.id, organizationId: admin.organizationId, permission: key as any },\n      })\n    }\n  }\n\n  // Audit logs for changes\n  const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || null\n  const logs: any[] = []\n  if (role && role !== target.role && ['ADMIN','MANAGER','AGENT','CUSTOMER'].includes(role)) {\n    logs.push({ action: 'USER_ROLE_CHANGE', before: { role: target.role }, after: { role } })\n  }\n  if (typeof isActive === 'boolean' && isActive !== target.isActive) {\n    logs.push({ action: 'USER_ACTIVATION', before: { isActive: target.isActive }, after: { isActive } })\n  }\n  if (Array.isArray(projectIds)) {\n    const afterProjects = await prisma.userProject.findMany({ where: { userId: updated.id }, select: { projectId: true } })\n    const afterProjectIds = afterProjects.map(p => p.projectId)\n    logs.push({ action: 'USER_PROJECT_SET', before: { projectIds: beforeProjectIds }, after: { projectIds: afterProjectIds } })\n  }\n  if (Array.isArray(departmentIds)) {\n    const afterDepartments = await prisma.userDepartment.findMany({ where: { userId: updated.id }, select: { departmentId: true } })\n    const afterDepartmentIds = afterDepartments.map(d => d.departmentId)\n    logs.push({ action: 'USER_DEPARTMENT_SET', before: { departmentIds: beforeDepartmentIds }, after: { departmentIds: afterDepartmentIds } })\n  }\n  if (Array.isArray(permissions)) {\n    const afterPerms = await prisma.userPermission.findMany({ where: { userId: updated.id }, select: { permission: true } })\n    const afterPermissionKeys = afterPerms.map(p => p.permission)\n    logs.push({ action: 'USER_PERMISSION_SET', before: { permissions: beforePermissionKeys }, after: { permissions: afterPermissionKeys } })\n  }\n\n  for (const entry of logs) {\n    await prisma.userAuditLog.create({\n      data: {\n        organizationId: admin.organizationId,\n        actorId: admin.id,\n        targetUserId: updated.id,\n        action: entry.action,\n        before: entry.before,\n        after: entry.after,\n        ip: ip || undefined,\n      },\n    })\n  }\n\n  return NextResponse.json({ user: { id: updated.id, email: updated.email, fullName: updated.fullName, role: updated.role, isActive: updated.isActive } })\n}\n\n"], "names": [], "mappings": "wqDAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIO,IAAM,EACX,AAHsB,WAGN,MAAM,EAAI,IAAI,EAAA,YAAY,CAAC,CACzC,IAAK,CAAC,OAAQ,QAAQ,AACxB,2LCPF,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,gDCfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,IAAM,EAAU,SAEhB,eAAe,EAAM,CAAgB,CAAE,CAA4C,EACxF,IAAM,EAAQ,EAAI,OAAO,CAAC,GAAG,CAAC,eAAe,MACvC,EAAQ,EAAQ,CAAA,EAAA,EAAA,WAAW,AAAX,EAAY,GAAS,KAC3C,GAAI,CAAC,EAAO,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,cAAe,EAAG,CAAE,OAAQ,GAAI,GAC9E,GAAmB,UAAf,EAAM,IAAI,CAAc,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,WAAY,EAAG,CAAE,OAAQ,GAAI,GAG3F,GAAM,MAAE,CAAI,UAAE,CAAQ,YAAE,CAAU,eAAE,CAAa,UAAE,CAAQ,aAAE,CAAW,CAAE,CAAG,AADhE,MAAM,EAAI,IAAI,IAC0D,CAAC,EAGhF,QAAE,CAAM,CAAE,CAAG,EACb,IAAE,CAAE,CAAE,CAAG,MAAM,EACf,EAAS,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAE,MAAO,IAAE,CAAG,CAAE,GAC5D,GAAI,CAAC,GAAU,EAAO,cAAc,GAAK,EAAM,cAAc,CAAE,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,WAAY,EAAG,CAAE,OAAQ,GAAI,GAG9H,IAAM,EAAiB,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAO,EAAE,AAAC,EAAG,OAAQ,CAAE,WAAW,CAAK,CAAE,GAC/G,EAAoB,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAO,EAAE,AAAC,EAAG,OAAQ,CAAE,cAAc,CAAK,CAAE,GACxH,EAAc,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAO,EAAG,AAAD,EAAI,OAAQ,CAAE,YAAY,CAAK,CAAE,GAChH,EAAmB,EAAe,GAAG,CAAC,GAAK,EAAE,SAAS,EACtD,EAAsB,EAAkB,GAAG,CAAC,GAAK,EAAE,YAAY,EAC/D,EAAuB,EAAY,GAAG,CAAC,GAAK,EAAE,UAAU,EAGxD,EAAU,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CACvC,MAAO,IAAE,CAAG,EACZ,KAAM,CACJ,GAAI,AAAa,WAAY,UAAE,CAAS,EAAI,CAAC,CAAC,CAC9C,GAAwB,WAApB,OAAO,EAAyB,UAAE,CAAS,EAAI,CAAC,CAAC,CACrD,GAAI,GAAQ,CAAC,QAAS,UAAW,QAAS,WAAW,CAAC,QAAQ,CAAC,GAAQ,CAAE,MAAK,EAAI,CAAC,CACrF,AADsF,CAExF,GAGA,GAAI,MAAM,OAAO,CAAC,GAGhB,IAAK,IAAM,EAHkB,GAC7B,AAEgB,MAFV,EAAA,CAEoB,KAFd,CAAC,WAAW,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,CAAE,GACnD,MAAM,EAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,GAAI,CAAE,GAAI,CAAW,EAAG,eAAgB,EAAM,cAAc,AAAC,EAAG,OAAQ,CAAE,IAAI,CAAK,CAAE,IAE7I,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE,KAAM,CAAE,OAAQ,EAAQ,EAAE,CAAE,UAAW,EAAE,EAAE,AAAC,CAAE,GAKpF,GAAI,MAAM,OAAO,CAAC,GAGhB,IAAK,IAAM,KAAK,AAHgB,AAChC,MAAM,EAAA,IAEuB,EAFjB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,CAAE,GACnD,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,GAAI,CAAE,GAAI,CAAc,EAAG,eAAgB,EAAM,cAAc,AAAC,EAAG,OAAQ,CAAE,IAAI,CAAK,CAAE,IAEtJ,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAE,KAAM,CAAE,OAAQ,EAAQ,EAAE,CAAE,aAAc,EAAE,EAAG,AAAD,CAAG,GAK1F,GAAI,MAAM,OAAO,CAAC,GAGhB,IAAK,IAAM,GAHmB,EAC9B,EAEkB,IAFZ,EAAA,CAEoB,KAFd,CAAC,cAAc,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,CAAE,GACxD,MAAM,IAAI,CAAC,IAAI,IAAI,EAAY,MAAM,CAAC,AAAC,GAAwB,UAAb,OAAO,MAEtE,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CACjC,KAAM,CAAE,OAAQ,EAAQ,EAAE,CAAE,eAAgB,EAAM,cAAc,CAAE,WAAY,CAAW,CAC3F,GAKJ,IAAM,EAAK,EAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,IAAI,CAAC,EAAE,EAAI,KAC1D,EAAc,EAAE,CAOtB,GANI,GAAQ,IAAS,EAAO,IAAI,EAAI,CAAC,QAAQ,UAAU,QAAQ,WAAW,CAAC,QAAQ,CAAC,IAClF,EAAK,CADoF,GAChF,CAAC,CAAE,OAAQ,mBAAoB,OAAQ,CAAE,KAAM,EAAO,IAAI,AAAC,EAAG,MAAO,MAAE,CAAK,CAAE,GAEjE,WAApB,OAAO,GAA0B,IAAa,EAAO,QAAQ,EAAE,AACjE,EAAK,IAAI,CAAC,CAAE,OAAQ,kBAAmB,OAAQ,CAAE,SAAU,EAAO,QAAQ,AAAC,EAAG,MAAO,UAAE,CAAS,CAAE,GAEhG,MAAM,OAAO,CAAC,GAAa,CAE7B,IAAM,EAAkB,CADF,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,EAAG,OAAQ,CAAE,WAAW,CAAK,CAAE,EAAA,EAC/E,GAAG,CAAC,GAAK,EAAE,SAAS,EAC1D,EAAK,IAAI,CAAC,CAAE,OAAQ,mBAAoB,OAAQ,CAAE,WAAY,CAAiB,EAAG,MAAO,CAAE,WAAY,CAAgB,CAAE,EAC3H,CACA,GAAI,MAAM,OAAO,CAAC,GAAgB,CAEhC,IAAM,EAAqB,CADF,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,EAAG,OAAQ,CAAE,cAAc,CAAK,CAAE,EAAA,EAClF,GAAG,CAAC,GAAK,EAAE,YAAY,EACnE,EAAK,IAAI,CAAC,CAAE,OAAQ,sBAAuB,OAAQ,CAAE,cAAe,CAAoB,EAAG,MAAO,CAAE,cAAe,CAAmB,CAAE,EAC1I,CACA,GAAI,MAAM,OAAO,CAAC,GAAc,CAE9B,IAAM,EAAsB,CADT,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAQ,EAAE,AAAC,EAAG,OAAQ,CAAE,YAAY,CAAK,CAAE,EAAA,EAC/E,GAAG,CAAC,GAAK,EAAE,UAAU,EAC5D,EAAK,IAAI,CAAC,CAAE,OAAQ,sBAAuB,OAAQ,CAAE,YAAa,CAAqB,EAAG,MAAO,CAAE,YAAa,CAAoB,CAAE,EACxI,CAEA,IAAK,IAAM,KAAS,EAClB,GADwB,GAClB,EAAA,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAC/B,KAAM,CACJ,eAAgB,EAAM,cAAc,CACpC,QAAS,EAAM,EAAE,CACjB,aAAc,EAAQ,EAAE,CACxB,OAAQ,EAAM,MAAM,CACpB,OAAQ,EAAM,MAAM,CACpB,MAAO,EAAM,KAAK,CAClB,GAAI,QAAM,CACZ,CACF,GAGF,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,KAAM,CAAE,GAAI,EAAQ,EAAE,CAAE,MAAO,EAAQ,KAAK,CAAE,SAAU,EAAQ,QAAQ,CAAE,KAAM,EAAQ,IAAI,CAAE,SAAU,EAAQ,QAAQ,AAAC,CAAE,EACxJ,CD5FA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,wBACN,SAAU,kBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,4CAClB,iBAZqB,SAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,CAAE,aAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,wBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,CACtD,UACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,sBAAE,CAAoB,CAAE,yBAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAgB,AAAhB,EAAiB,GACvC,GAAQ,CAAQ,GAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAIhE,AAHqC,CAIrC,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAS,AAAT,IACT,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,CACZ,SACA,oBACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,WAAY,EAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAyB,AAAzB,EAA0B,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,AACvC,EAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,CAAG,OAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,CACV,oBACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAmB,AAAnB,EAAoB,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,EAIrC,GAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [1]}