module.exports=[18311,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(87924),c=a.i(72131);function d({initial:a}){let[d,e]=(0,c.useState)(a||{}),[f,g]=(0,c.useState)([]),[h,i]=(0,c.useState)([]),[j,k]=(0,c.useState)(!1),[l,m]=(0,c.useState)(null),[n,o]=(0,c.useState)([]),[p,q]=(0,c.useState)({});function r(a,b){e(c=>({...c,[a]:b}))}async function s(a){a.preventDefault(),k(!0),m(null);try{let a={};for(let b of n){let c=(b.toEmail||"").trim().toLowerCase();c&&b.departmentId&&(a[c]=b.departmentId)}let b={...d,routingMap:a};if(!(await fetch("/api/email/config",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)})).ok)throw Error("Save failed");m("Saved successfully")}catch(a){m(a?.message||"Failed to save")}finally{k(!1)}}return(0,c.useEffect)(()=>{!async function(){try{let[a,b]=await Promise.all([fetch("/api/projects").then(a=>a.json()).catch(()=>({projects:[]})),fetch("/api/departments").then(a=>a.json()).catch(()=>({departments:[]}))]);g(a.projects||[]),i(b.departments||[])}catch{}}()},[]),(0,c.useEffect)(()=>{let a=Object.entries(d.routingMap||{}).map(([a,b])=>({toEmail:a,departmentId:String(b||"")}));0===a.length&&a.push({toEmail:"",departmentId:""}),o(a)},[d.routingMap]),(0,b.jsxs)("form",{onSubmit:s,className:"space-y-6",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,b.jsx)("h2",{className:"font-semibold",children:"Webhook"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"Webhook secret"}),(0,b.jsx)("input",{type:"text",value:d.webhookSecret||"",onChange:a=>r("webhookSecret",a.target.value),className:"mt-1 w-full border rounded px-3 py-2",placeholder:"e.g., random-long-secret"}),(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Header: x-email-webhook-secret must match."})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"Rate limit (per hour per sender)"}),(0,b.jsx)("input",{type:"number",value:d.rateLimitPerHour??20,onChange:a=>r("rateLimitPerHour",Number(a.target.value)),className:"mt-1 w-full border rounded px-3 py-2"})]})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,b.jsx)("h2",{className:"font-semibold",children:"Routing"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"Default Project"}),(0,b.jsxs)("select",{value:d.defaultProjectId||"",onChange:a=>r("defaultProjectId",a.target.value),className:"mt-1 w-full border rounded px-3 py-2",children:[(0,b.jsx)("option",{value:"",children:"— None —"}),f.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"Default Department"}),(0,b.jsxs)("select",{value:d.defaultDepartmentId||"",onChange:a=>r("defaultDepartmentId",a.target.value),className:"mt-1 w-full border rounded px-3 py-2",children:[(0,b.jsx)("option",{value:"",children:"— None —"}),h.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,b.jsxs)("div",{className:"md:col-span-2",children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600 mb-2",children:"Address book (Recipient → Department)"}),(0,b.jsxs)("div",{className:"space-y-3",children:[n.map((a,c)=>(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-12 gap-2 items-center",children:[(0,b.jsx)("div",{className:"md:col-span-5",children:(0,b.jsx)("input",{placeholder:"<EMAIL>",className:"w-full border rounded px-3 py-2",value:a.toEmail,onChange:a=>o(b=>b.map((b,d)=>d===c?{...b,toEmail:a.target.value}:b))})}),(0,b.jsx)("div",{className:"md:col-span-4",children:(0,b.jsxs)("select",{className:"w-full border rounded px-3 py-2",value:a.departmentId,onChange:a=>o(b=>b.map((b,d)=>d===c?{...b,departmentId:a.target.value}:b)),children:[(0,b.jsx)("option",{value:"",children:"— Select department —"}),h.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})}),(0,b.jsxs)("div",{className:"md:col-span-4 flex flex-wrap gap-2",children:[(0,b.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:async()=>{let b=(a.toEmail||"").trim();if(!b)return void q(a=>({...a,[c]:"Enter a recipient email first"}));q(a=>({...a,[c]:"Testing routing..."}));try{let a=await fetch("/api/email/config/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({to:b})}),d=await a.json();if(!a.ok)throw Error(d?.error||"Failed");let e=d?.resolved;q(a=>({...a,[c]:`Department: ${e?.departmentName||e?.departmentId||"n/a"} • Project: ${e?.projectName||e?.projectId||"n/a"}`}))}catch(a){q(b=>({...b,[c]:a?.message||"Test failed"}))}},children:"Test routing"}),(0,b.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:async()=>{let b=(a.toEmail||"").trim();if(!b)return void q(a=>({...a,[c]:"Enter a recipient email first"}));q(a=>({...a,[c]:"Sending test email..."}));try{let a=await fetch("/api/email/config/test/send-ack",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({to:b})}),d=await a.json();if(!a.ok)throw Error(d?.error||"Failed");q(a=>({...a,[c]:`Test email sent ✓ (messageId: ${d?.messageId||"n/a"})`}))}catch(a){q(b=>({...b,[c]:a?.message||"Send failed"}))}},children:"Send test email"}),(0,b.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:()=>o(a=>a.filter((a,b)=>b!==c)),children:"Remove"})]}),p[c]&&(0,b.jsx)("div",{className:"md:col-span-12 text-xs text-gray-600",children:p[c]})]},c)),(0,b.jsx)("button",{type:"button",className:"inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-9 px-3 py-1",onClick:()=>o(a=>[...a,{toEmail:"",departmentId:""}]),children:"+ Add address"})]})]})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,b.jsx)("h2",{className:"font-semibold",children:"Acknowledgement Email (SMTP)"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"SMTP Host"}),(0,b.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.host||"",onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},host:a.target.value}}))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"SMTP Port"}),(0,b.jsx)("input",{type:"number",className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.port??587,onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},port:Number(a.target.value)}}))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"User"}),(0,b.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.user||"",onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},user:a.target.value}}))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"Password"}),(0,b.jsx)("input",{type:"password",className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.pass||"",onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},pass:a.target.value}}))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"From Email"}),(0,b.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.fromEmail||"",onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},fromEmail:a.target.value}}))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm text-gray-600",children:"From Name"}),(0,b.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:d.smtp?.fromName||"",onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},fromName:a.target.value}}))})]}),(0,b.jsx)("div",{children:(0,b.jsxs)("label",{className:"inline-flex items-center text-sm text-gray-600 mt-6",children:[(0,b.jsx)("input",{type:"checkbox",className:"mr-2",checked:!!d.smtp?.secure,onChange:a=>e(b=>({...b,smtp:{...b.smtp||{},secure:a.target.checked}}))}),"Use TLS (secure)"]})})]})]}),(0,b.jsxs)("div",{className:"flex items-center gap-3",children:[(0,b.jsx)("button",{type:"submit",disabled:j,className:"inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2",children:j?"Saving…":"Save configuration"}),l&&(0,b.jsx)("span",{className:"text-sm text-gray-600",children:l})]})]})}}];

//# sourceMappingURL=src_app_dashboard_email-config_EmailConfigForm_tsx_a85d0316._.js.map