module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},75762,(e,t,r)=>{},83518,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>q,"routeModule",()=>N,"serverHooks",()=>A,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>b],83518);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),d=e.i(95169),p=e.i(47587),u=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),h=e.i(93695);e.i(52474);var R=e.i(220);e.s(["GET",()=>w,"POST",()=>E,"runtime",()=>y],59661);var v=e.i(89171),g=e.i(15270),f=e.i(65800);let y="nodejs";async function w(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,f.verifyToken)(t):null;if(!r)return v.NextResponse.json({error:"Unauthorized"},{status:401});try{let{searchParams:t}=new URL(e.url);if("1"===t.get("my")&&"ADMIN"!==r.role){let e=await g.prisma.userDepartment.findMany({where:{userId:r.id,department:{organizationId:r.organizationId}},select:{department:{select:{id:!0,name:!0,description:!0}}},orderBy:{department:{name:"asc"}}});return v.NextResponse.json({departments:e.map(e=>e.department)})}let a=await g.prisma.department.findMany({where:{organizationId:r.organizationId},select:{id:!0,name:!0,description:!0},orderBy:{name:"asc"}});return v.NextResponse.json({departments:a})}catch(e){return v.NextResponse.json({error:e?.message||"Failed to load departments"},{status:400})}}async function E(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,f.verifyToken)(t):null;if(!r)return v.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role)return v.NextResponse.json({error:"Forbidden"},{status:403});try{let{name:t,description:a}=await e.json();if(!t||"string"!=typeof t)return v.NextResponse.json({error:"Name is required"},{status:400});let n=await g.prisma.department.create({data:{name:t,description:a||null,organizationId:r.organizationId},select:{id:!0,name:!0,description:!0}});return v.NextResponse.json({department:n},{status:201})}catch(e){return v.NextResponse.json({error:e?.message||"Failed to create department"},{status:400})}}var C=e.i(59661);let N=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/departments/route",pathname:"/api/departments",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/departments/route.ts",nextConfigOutput:"export",userland:C}),{workAsyncStorage:j,workUnitAsyncStorage:b,serverHooks:A}=N;function q(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:b})}async function k(e,t,a){var v;let g="/api/departments/route";g=g.replace(/\/index$/,"")||"/";let f=await N.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:y,params:w,nextConfig:E,isDraftMode:C,prerenderManifest:j,routerServerContext:b,isOnDemandRevalidate:A,revalidateOnlyGenerated:q,resolvedPathname:k}=f,T=(0,o.normalizeAppPath)(g),P=!!(j.dynamicRoutes[T]||j.routes[k]);if(P&&!C){let e=!!j.routes[k],t=j.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let I=null;!P||N.isDev||C||(I="/index"===(I=k)?"/":I);let O=!0===N.isDev||!P,_=P&&!O,S=e.method||"GET",U=(0,s.getTracer)(),M=U.getActiveScopeSpan(),H={params:w,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>N.onRequestError(e,t,a,b)},sharedContext:{buildId:y}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>N.handle(z,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${S} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async s=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&A&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=H.renderOpts.fetchMetrics;let l=H.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=H.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(D,F,i,H.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await N.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:A})},b),t}},h=await N.handleResponse({req:e,nextConfig:E,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:a.waitUntil});if(!P)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,u.sendResponse)(D,F,new Response(h.value.body,{headers:v,status:h.value.status||200})),null};M?await i(M):await U.withPropagatedContext(e.headers,()=>U.trace(d.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(M||t instanceof h.NoFallbackError||await N.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:A})}),P)throw t;return await (0,u.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__f7626fd7._.js.map