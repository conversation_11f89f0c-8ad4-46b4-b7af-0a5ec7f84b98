(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["chunks/turbopack-edge-wrapper_87689a19.js",{otherChunks:["chunks/edge-wrapper_0b0266f1.js","chunks/[root-of-the-server]__db54bf8a._.js"],runtimeModuleIds:[32442]}]),(()=>{let e;if(!Array.isArray(globalThis.TURBOPACK))return;let t=new WeakMap;function r(e,t){this.m=e,this.e=t}let n=r.prototype,o=Object.prototype.hasOwnProperty,u="undefined"!=typeof Symbol&&Symbol.toStringTag;function i(e,t,r){o.call(e,t)||Object.defineProperty(e,t,r)}function l(e,t){let r=e[t];return r||(r=a(t),e[t]=r),r}function a(e){return{exports:{},error:void 0,id:e,namespaceObject:void 0}}function s(e,t){i(e,"__esModule",{value:!0}),u&&i(e,u,{value:"Module"});let r=0;for(;r<t.length;){let n=t[r++],o=t[r++];"function"==typeof t[r]?i(e,n,{get:o,set:t[r++],enumerable:!0}):i(e,n,{get:o,enumerable:!0})}Object.seal(e)}n.s=function(e,t){let r,n;null!=t?n=(r=l(this.c,t)).exports:(r=this.m,n=this.e),r.namespaceObject=n,s(n,e)},n.j=function(e,r){var n,u;let i,a,s;null!=r?a=(i=l(this.c,r)).exports:(i=this.m,a=this.e);let c=(n=i,u=a,(s=t.get(n))||(t.set(n,s=[]),n.exports=n.namespaceObject=new Proxy(u,{get(e,t){if(o.call(e,t)||"default"===t||"__esModule"===t)return Reflect.get(e,t);for(let e of s){let r=Reflect.get(e,t);if(void 0!==r)return r}},ownKeys(e){let t=Reflect.ownKeys(e);for(let e of s)for(let r of Reflect.ownKeys(e))"default"===r||t.includes(r)||t.push(r);return t}})),s);"object"==typeof e&&null!==e&&c.push(e)},n.v=function(e,t){(null!=t?l(this.c,t):this.m).exports=e},n.n=function(e,t){let r;(r=null!=t?l(this.c,t):this.m).exports=r.namespaceObject=e};let c=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,f=[null,c({}),c([]),c(c)];function d(e,t,r){let n=[],o=-1;for(let t=e;("object"==typeof t||"function"==typeof t)&&!f.includes(t);t=c(t))for(let r of Object.getOwnPropertyNames(t))n.push(r,function(e,t){return()=>e[t]}(e,r)),-1===o&&"default"===r&&(o=n.length-1);return r&&o>=0||(o>=0?n[o]=()=>e:n.push("default",()=>e)),s(t,n),t}function h(e){return"function"==typeof e?function(...t){return e.apply(this,t)}:Object.create(null)}function p(e){return"string"==typeof e?e:e.path}function m(){let e,t;return{promise:new Promise((r,n)=>{t=n,e=r}),resolve:e,reject:t}}n.i=function(e){let t=x(e,this.m);if(t.namespaceObject)return t.namespaceObject;let r=t.exports;return t.namespaceObject=d(r,h(r),r&&r.__esModule)},n.A=function(e){return this.r(e)(this.i.bind(this))},n.t="function"==typeof require?require:function(){throw Error("Unexpected use of runtime require")},n.r=function(e){return x(e,this.m).exports},n.f=function(e){function t(t){if(o.call(e,t))return e[t].module();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r}return t.keys=()=>Object.keys(e),t.resolve=t=>{if(o.call(e,t))return e[t].id();let r=Error(`Cannot find module '${t}'`);throw r.code="MODULE_NOT_FOUND",r},t.import=async e=>await t(e),t};let b=Symbol("turbopack queues"),y=Symbol("turbopack exports"),O=Symbol("turbopack error");function g(e){e&&1!==e.status&&(e.status=1,e.forEach(e=>e.queueCount--),e.forEach(e=>e.queueCount--?e.queueCount++:e()))}n.a=function(e,t){let r=this.m,n=t?Object.assign([],{status:-1}):void 0,o=new Set,{resolve:u,reject:i,promise:l}=m(),a=Object.assign(l,{[y]:r.exports,[b]:e=>{n&&e(n),o.forEach(e),a.catch(()=>{})}}),s={get:()=>a,set(e){e!==a&&(a[y]=e)}};Object.defineProperty(r,"exports",s),Object.defineProperty(r,"namespaceObject",s),e(function(e){let t=e.map(e=>{if(null!==e&&"object"==typeof e){if(b in e)return e;if(null!=e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then){let t=Object.assign([],{status:0}),r={[y]:{},[b]:e=>e(t)};return e.then(e=>{r[y]=e,g(t)},e=>{r[O]=e,g(t)}),r}}return{[y]:e,[b]:()=>{}}}),r=()=>t.map(e=>{if(e[O])throw e[O];return e[y]}),{promise:u,resolve:i}=m(),l=Object.assign(()=>i(r),{queueCount:0});function a(e){e!==n&&!o.has(e)&&(o.add(e),e&&0===e.status&&(l.queueCount++,e.push(l)))}return t.map(e=>e[b](a)),l.queueCount?u:r()},function(e){e?i(a[O]=e):u(a[y]),g(n)}),n&&-1===n.status&&(n.status=0)};let w=function(e){let t=new URL(e,"x:/"),r={};for(let e in t)r[e]=t[e];for(let t in r.href=e,r.pathname=e.replace(/[?#].*/,""),r.origin=r.protocol="",r.toString=r.toJSON=(...t)=>e,r)Object.defineProperty(this,t,{enumerable:!0,configurable:!0,value:r[t]})};function _(e,t){throw Error(`Invariant: ${t(e)}`)}w.prototype=URL.prototype,n.U=w,n.z=function(e){throw Error("dynamic usage of require is not supported")},n.g=globalThis;let j=r.prototype;var C=function(e){return e[e.Runtime=0]="Runtime",e[e.Parent=1]="Parent",e[e.Update=2]="Update",e}(C||{});let k=new Map;n.M=k;let R=new Map,U=new Map;async function P(e,t,r){let n;if("string"==typeof r)return M(e,t,$(r));let o=r.included||[],u=o.map(e=>!!k.has(e)||R.get(e));if(u.length>0&&u.every(e=>e))return void await Promise.all(u);let i=r.moduleChunks||[],l=i.map(e=>U.get(e)).filter(e=>e);if(l.length>0){if(l.length===i.length)return void await Promise.all(l);let r=new Set;for(let e of i)U.has(e)||r.add(e);for(let n of r){let r=M(e,t,$(n));U.set(n,r),l.push(r)}n=Promise.all(l)}else{for(let o of(n=M(e,t,$(r.path)),i))U.has(o)||U.set(o,n)}for(let e of o)R.has(e)||R.set(e,n);await n}j.l=function(e){return P(1,this.m.id,e)};let v=Promise.resolve(void 0),T=new WeakMap;function M(t,r,n){let o=e.loadChunkCached(t,n),u=T.get(o);if(void 0===u){let e=T.set.bind(T,o,v);u=o.then(e).catch(e=>{let o;switch(t){case 0:o=`as a runtime dependency of chunk ${r}`;break;case 1:o=`from module ${r}`;break;case 2:o="from an HMR update";break;default:_(t,e=>`Unknown source type: ${e}`)}throw Error(`Failed to load chunk ${n} ${o}${e?`: ${e}`:""}`,e?{cause:e}:void 0)}),T.set(o,u)}return u}function $(e){return`${e.split("/").map(e=>encodeURIComponent(e)).join("/")}`}j.L=function(e){return M(1,this.m.id,e)},j.R=function(e){let t=this.r(e);return t?.default??t},j.P=function(e){return`/ROOT/${e??""}`},j.b=function(e){let t=new Blob([`self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};
self.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(e.reverse().map($),null,2)};
importScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`],{type:"text/javascript"});return URL.createObjectURL(t)};let A=/\.js(?:\?[^#]*)?(?:#.*)?$/;n.w=function(t,r,n){return e.loadWebAssembly(1,this.m.id,t,r,n)},n.u=function(t,r){return e.loadWebAssemblyModule(1,this.m.id,t,r)};let E={};n.c=E;let x=(e,t)=>{let r=E[e];if(r){if(r.error)throw r.error;return r}return K(e,C.Parent,t.id)};function K(e,t,n){let o=k.get(e);"function"!=typeof o&&function(e,t,r){let n;switch(t){case 0:n=`as a runtime entry of chunk ${r}`;break;case 1:n=`because it was required from module ${r}`;break;case 2:n="because of an HMR update";break;default:_(t,e=>`Unknown source type: ${e}`)}throw Error(`Module ${e} was instantiated ${n}, but the module factory is not available. It might have been deleted in an HMR update.`)}(e,t,n);let u=a(e),i=u.exports;E[e]=u;let l=new r(u,i);try{o(l,u,i)}catch(e){throw u.error=e,e}return u.namespaceObject&&u.exports!==u.namespaceObject&&d(u.exports,u.namespaceObject),u}function S(t){let r,n=function(e){if("string"==typeof e)return e;let t=decodeURIComponent(("undefined"!=typeof TURBOPACK_NEXT_CHUNK_URLS?TURBOPACK_NEXT_CHUNK_URLS.pop():e.getAttribute("src")).replace(/[?#].*$/,""));return t.startsWith("")?t.slice(0):t}(t[0]);return 2===t.length?r=t[1]:(r=void 0,!function(e,t,r,n){let o=1;for(;o<e.length;){let t=e[o],n=o+1;for(;n<e.length&&"function"!=typeof e[n];)n++;if(n===e.length)throw Error("malformed chunk format, expected a factory function");if(!r.has(t)){let u=e[n];for(Object.defineProperty(u,"name",{value:"__TURBOPACK__module__evaluation__"});o<n;o++)t=e[o],r.set(t,u)}o=n+1}}(t,0,k)),e.registerChunk(n,r)}function N(e,t,r=!1){let n;try{n=t()}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return!r||n.__esModule?n:d(n,h(n),!0)}n.y=async function(e){let t;try{t=await import(e)}catch(t){throw Error(`Failed to load external module ${e}: ${t}`)}return t&&t.__esModule&&t.default&&"default"in t.default?d(t.default,h(t),!0):t},N.resolve=(e,t)=>require.resolve(e,t),n.x=N,(()=>{e={registerChunk(e,o){t.add(e),function(e){let t=r.get(e);if(null!=t){for(let r of t)r.requiredChunks.delete(e),0===r.requiredChunks.size&&n(r.runtimeModuleIds,r.chunkPath);r.delete(e)}}(e),null!=o&&(0===o.otherChunks.length?n(o.runtimeModuleIds,e):function(e,o,u){let i=new Set,l={runtimeModuleIds:u,chunkPath:e,requiredChunks:i};for(let e of o){let n=p(e);if(t.has(n))continue;i.add(n);let o=r.get(n);null==o&&(o=new Set,r.set(n,o)),o.add(l)}0===l.requiredChunks.size&&n(l.runtimeModuleIds,l.chunkPath)}(e,o.otherChunks.filter(e=>{var t;return t=p(e),A.test(t)}),o.runtimeModuleIds))},loadChunkCached(e,t){throw Error("chunk loading is not supported")},async loadWebAssembly(e,t,r,n,u){let i=await o(r,n);return await WebAssembly.instantiate(i,u)},loadWebAssemblyModule:async(e,t,r,n)=>o(r,n)};let t=new Set,r=new Map;function n(e,t){for(let r of e)!function(e,t){let r=E[t];if(r){if(r.error)throw r.error;return}K(t,C.Runtime,e)}(t,r)}async function o(e,t){let r;try{r=t()}catch(e){}if(!r)throw Error(`dynamically loading WebAssembly is not supported in this runtime as global was not injected for chunk '${e}'`);return r}})();let q=globalThis.TURBOPACK;globalThis.TURBOPACK={push:S},q.forEach(S)})();

//# sourceMappingURL=edge-wrapper_87689a19.js.map