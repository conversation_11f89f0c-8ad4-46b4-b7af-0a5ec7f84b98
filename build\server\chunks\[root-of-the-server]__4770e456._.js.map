{"version": 3, "sources": ["turbopack:///[project]/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as { prisma?: PrismaClient }\n\nexport const prisma =\n  globalForPrisma.prisma ?? new PrismaClient({\n    log: ['warn', 'error'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n\n"], "names": [], "mappings": "wHAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIO,IAAM,EAFW,AAGtB,WAAgB,MAAM,EAAI,IAAI,EAAA,YAAY,CAAC,CACzC,IAAK,CAAC,OAAQ,QAAQ,AACxB"}