module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},44071,(e,t,r)=>{},31367,e=>{"use strict";e.s(["handler",()=>j,"patchFetch",()=>q,"routeModule",()=>C,"serverHooks",()=>N,"workAsyncStorage",()=>A,"workUnitAsyncStorage",()=>b],31367);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["GET",()=>y,"runtime",()=>w],19313);var v=e.i(89171),g=e.i(15270),f=e.i(65800);let w="nodejs";async function y(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,f.verifyToken)(t):null;if(!r)return v.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role)return v.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:a}=new URL(e.url),n=a.get("targetUserId"),s=Math.min(parseInt(a.get("limit")||"50",10)||50,200),i={organizationId:r.organizationId};n&&(i.targetUserId=n);let o=await g.prisma.userAuditLog.findMany({where:i,orderBy:{createdAt:"desc"},take:s,select:{id:!0,action:!0,before:!0,after:!0,ip:!0,createdAt:!0,actor:{select:{id:!0,fullName:!0,email:!0}},targetUser:{select:{id:!0,fullName:!0,email:!0}}}});return v.NextResponse.json({logs:o})}var E=e.i(19313);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/audit/users/route",pathname:"/api/audit/users",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/audit/users/route.ts",nextConfigOutput:"export",userland:E}),{workAsyncStorage:A,workUnitAsyncStorage:b,serverHooks:N}=C;function q(){return(0,a.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:b})}async function j(e,t,a){var v;let g="/api/audit/users/route";g=g.replace(/\/index$/,"")||"/";let f=await C.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:A,prerenderManifest:b,routerServerContext:N,isOnDemandRevalidate:q,revalidateOnlyGenerated:j,resolvedPathname:k}=f,T=(0,i.normalizeAppPath)(g),P=!!(b.dynamicRoutes[T]||b.routes[k]);if(P&&!A){let e=!!b.routes[k],t=b.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!P||C.isDev||A||(O="/index"===(O=k)?"/":O);let U=!0===C.isDev||!P,I=P&&!U,_=e.method||"GET",S=(0,s.getTracer)(),H=S.getActiveScopeSpan(),M={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:U,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:I,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>C.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>C.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=S.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${_} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),o=async s=>{var o,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);u&&(t[h.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:q})},N),t}},m=await C.handleResponse({req:e,nextConfig:E,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:j,responseGenerator:u,waitUntil:a.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(D,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};H?await o(H):await S.withPropagatedContext(e.headers,()=>S.trace(u.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:q})}),P)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__abcaa8f1._.js.map