module.exports=[5522,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},40695,a=>{"use strict";a.s(["Button",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,variant:c="default",size:e="default",...f},g)=>(0,b.jsx)("button",{className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[c],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[e],a),ref:g,...f}));e.displayName="Button"},3130,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},63384,a=>{"use strict";a.s(["default",()=>i]);var b=a.i(87924),c=a.i(72131),d=a.i(50944),e=a.i(3130),f=a.i(5522),g=a.i(40695);let h=["Technical Support","Billing","Account","Feature Request","Data Management","Other"];function i(){let a=(0,d.useRouter)(),[i,j]=(0,c.useState)(""),[k,l]=(0,c.useState)(""),[m,n]=(0,c.useState)("MEDIUM"),[o,p]=(0,c.useState)("Technical Support"),[q,r]=(0,c.useState)([]),[s,t]=(0,c.useState)(""),[u,v]=(0,c.useState)([]),[w,x]=(0,c.useState)(""),[y,z]=(0,c.useState)(!1),[A,B]=(0,c.useState)(""),C=(0,c.useRef)(null);(0,c.useEffect)(()=>{(async()=>{try{let[a,b]=await Promise.all([fetch("/api/projects"),fetch("/api/departments?my=1")]),c=await a.json(),d=await b.json();a.ok?(r(c.projects||[]),(c.projects||[]).length>0&&t(c.projects[0].id)):B(c.error||"Failed to load projects"),b.ok?(v(d.departments||[]),(d.departments||[]).length>0&&x(d.departments[0].id)):A||B(d.error||"Failed to load departments")}catch{B("Failed to load projects/departments")}})()},[]);let D=async b=>{b.preventDefault(),z(!0),B("");try{if(!s){B("Please select a project"),z(!1);return}if(!w){B("Please select a department"),z(!1);return}let b=new FormData;b.append("title",i),b.append("description",k),b.append("priority",m),b.append("category",o),b.append("projectId",s),b.append("departmentId",w);let c=C.current?.files;if(c){let a=Math.min(c.length,3);for(let d=0;d<a;d++)b.append("images",c[d])}let d=await fetch("/api/tickets",{method:"POST",body:b}),e=await d.json();if(!d.ok)return void B(e.error||"Failed to create ticket");a.push("/dashboard/tickets"),a.refresh()}catch(a){B("Unexpected error")}finally{z(!1)}};return(0,b.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Ticket"}),(0,b.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Submit a new support request."})]}),(0,b.jsxs)(e.Card,{children:[(0,b.jsxs)(e.CardHeader,{children:[(0,b.jsx)(e.CardTitle,{children:"Ticket Details"}),(0,b.jsx)(e.CardDescription,{children:"Provide details to help us resolve your issue."})]}),(0,b.jsx)(e.CardContent,{children:(0,b.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[A&&(0,b.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-3 rounded",children:A}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Project"}),(0,b.jsx)("select",{value:s,onChange:a=>t(a.target.value),className:"mt-1 w-full border rounded p-2",required:!0,children:q.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))}),0===q.length&&(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"No projects available. Ask an admin to assign you to a project."})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Department"}),(0,b.jsx)("select",{value:w,onChange:a=>x(a.target.value),className:"mt-1 w-full border rounded p-2",required:!0,children:u.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))}),0===u.length&&(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"No departments configured. Ask an admin to add departments."})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Title"}),(0,b.jsx)(f.Input,{value:i,onChange:a=>j(a.target.value),required:!0,className:"mt-1"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,b.jsx)("textarea",{value:k,onChange:a=>l(a.target.value),required:!0,className:"mt-1 w-full border rounded p-2 h-32"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Priority"}),(0,b.jsxs)("select",{value:m,onChange:a=>n(a.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,b.jsx)("option",{children:"LOW"}),(0,b.jsx)("option",{children:"MEDIUM"}),(0,b.jsx)("option",{children:"HIGH"}),(0,b.jsx)("option",{children:"URGENT"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),(0,b.jsx)("select",{value:o,onChange:a=>p(a.target.value),className:"mt-1 w-full border rounded p-2",children:h.map(a=>(0,b.jsx)("option",{value:a,children:a},a))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Images (up to 3)"}),(0,b.jsx)("input",{ref:C,type:"file",accept:"image/*",multiple:!0,className:"mt-1 w-full border rounded p-2",name:"images",onChange:a=>{let b=a.currentTarget.files;b&&b.length>3&&(a.currentTarget.value="",alert("Please select up to 3 images"))}}),(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PNG, JPG, or GIF. Max 3 images."})]}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(g.Button,{type:"submit",disabled:y||0===q.length,children:y?"Creating...":"Create Ticket"}),(0,b.jsx)(g.Button,{type:"button",variant:"outline",onClick:()=>a.back(),children:"Cancel"})]})]})})]})]})}}];

//# sourceMappingURL=_2fad37c2._.js.map