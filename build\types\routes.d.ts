// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/auth/login" | "/auth/signup" | "/dashboard" | "/dashboard/departments" | "/dashboard/email-config" | "/dashboard/reports" | "/dashboard/tickets" | "/dashboard/tickets/[id]" | "/dashboard/tickets/new" | "/dashboard/users"
type AppRouteHandlerRoutes = "/api/audit/users" | "/api/auth/login" | "/api/auth/logout" | "/api/departments" | "/api/departments/[id]" | "/api/email/config" | "/api/email/config/test" | "/api/email/config/test/send-ack" | "/api/email/inbound" | "/api/me" | "/api/projects" | "/api/tickets" | "/api/tickets/[id]" | "/api/tickets/[id]/messages" | "/api/users" | "/api/users/[id]"
type PageRoutes = never
type LayoutRoutes = "/" | "/dashboard"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/audit/users": {}
  "/api/auth/login": {}
  "/api/auth/logout": {}
  "/api/departments": {}
  "/api/departments/[id]": { "id": string; }
  "/api/email/config": {}
  "/api/email/config/test": {}
  "/api/email/config/test/send-ack": {}
  "/api/email/inbound": {}
  "/api/me": {}
  "/api/projects": {}
  "/api/tickets": {}
  "/api/tickets/[id]": { "id": string; }
  "/api/tickets/[id]/messages": { "id": string; }
  "/api/users": {}
  "/api/users/[id]": { "id": string; }
  "/auth/login": {}
  "/auth/signup": {}
  "/dashboard": {}
  "/dashboard/departments": {}
  "/dashboard/email-config": {}
  "/dashboard/reports": {}
  "/dashboard/tickets": {}
  "/dashboard/tickets/[id]": { "id": string; }
  "/dashboard/tickets/new": {}
  "/dashboard/users": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
  "/dashboard": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
