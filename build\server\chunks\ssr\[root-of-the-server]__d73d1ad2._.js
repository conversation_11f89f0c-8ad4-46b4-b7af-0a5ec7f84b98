module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(36984);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},15843,a=>{a.n(a.i(18357))},2316,a=>{"use strict";a.s(["getUserPermissions",()=>d,"hasPermission",()=>e]);var b=a.i(31191);let c=["DASHBOARD_VIEW","TICKETS_VIEW","TICKETS_UPDATE","TICKETS_ASSIGN","REPORTS_VIEW","DEPARTMENTS_MANAGE","PROJECTS_MANAGE","AUDIT_VIEW"];async function d(a){return new Set("ADMIN"===a.role?c:(await b.prisma.userPermission.findMany({where:{userId:a.id,organizationId:a.organizationId},select:{permission:!0}})).map(a=>a.permission))}function e(a,b){return a.has(b)}},72542,(a,b,c)=>{},8851,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(7997),c=a.i(5246),d=a.i(31191),e=a.i(14929),f=a.i(2316);function g(a){return a.toISOString().slice(0,10)}async function h({searchParams:a}){let h=await (0,c.cookies)(),i=h.get("auth-token")?.value,j=i?(0,e.verifyToken)(i):null;if(!j)return(0,b.jsx)("div",{className:"p-6",children:"Unauthorized"});if("ADMIN"!==j.role){let a=await (0,f.getUserPermissions)({id:j.id,role:j.role,organizationId:j.organizationId});if(!(0,f.hasPermission)(a,"TICKETS_VIEW"))return(0,b.jsx)("div",{className:"p-6",children:"Forbidden"})}let k=(await d.prisma.userProject.findMany({where:{userId:j.id},select:{projectId:!0}})).map(a=>a.projectId),l=(await d.prisma.userDepartment.findMany({where:{userId:j.id},select:{departmentId:!0}})).map(a=>a.departmentId),m="ADMIN"===j.role?{organizationId:j.organizationId}:{organizationId:j.organizationId,projectId:{in:k.length?k:["__none__"]},departmentId:{in:l.length?l:["__none__"]}},n="string"==typeof a?.status?a.status.trim():"",o="string"==typeof a?.projectId?a.projectId.trim():"",p="string"==typeof a?.departmentId?a.departmentId.trim():"",q="string"==typeof a?.from?a.from.trim():"",r="string"==typeof a?.to?a.to.trim():"",s={...m,...n?{status:n}:{},...o?{projectId:o}:{},...p?{departmentId:p}:{},...q?{createdAt:{gte:new Date(q)}}:{},...r?{createdAt:{...q?{gte:new Date(q)}:{},lte:new Date(r)}}:{}},[t,u]=await Promise.all(["ADMIN"===j.role?d.prisma.project.findMany({where:{organizationId:j.organizationId},select:{id:!0,name:!0}}):d.prisma.project.findMany({where:{id:{in:k.length?k:["__none__"]}},select:{id:!0,name:!0}}),"ADMIN"===j.role?d.prisma.department.findMany({where:{organizationId:j.organizationId},select:{id:!0,name:!0}}):d.prisma.department.findMany({where:{id:{in:l.length?l:["__none__"]}},select:{id:!0,name:!0}})]),[v,w,x,y,z]=await Promise.all([d.prisma.ticket.groupBy({by:["status"],where:s,_count:{_all:!0}}),d.prisma.ticket.groupBy({by:["departmentId"],where:s,_count:{_all:!0}}),d.prisma.ticket.groupBy({by:["projectId"],where:s,_count:{_all:!0}}),Promise.all([d.prisma.ticket.count({where:s}),d.prisma.ticket.count({where:{...s,status:"OPEN"}}),d.prisma.ticket.count({where:{...s,status:"IN_PROGRESS"}}),d.prisma.ticket.count({where:{...s,status:"RESOLVED"}}),d.prisma.ticket.count({where:{...s,status:"CLOSED"}}),d.prisma.ticket.count({where:{...s,status:"CANCELLED"}})]),d.prisma.ticket.findMany({where:{...s,...q||r?{}:{createdAt:{gte:new Date(Date.now()-25056e5)}}},select:{id:!0,createdAt:!0},orderBy:{createdAt:"asc"}})]),[A,B,C,D,E,F]=y,G=new Map(u.map(a=>[a.id,a.name])),H=new Map(t.map(a=>[a.id,a.name])),I=new Map;for(let a of z){let b=g(a.createdAt);I.set(b,(I.get(b)||0)+1)}let J=new Date,K=new Date(q||new Date(Date.now()-11232e5)),L=[];for(let a=new Date(K);a<=J;a=new Date(a.getTime()+864e5)){let b=g(a);L.push({day:b,count:I.get(b)||0})}return(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,b.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Reports"}),(0,b.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Organization analytics (scoped to your access)"})]})})}),(0,b.jsx)("form",{method:"GET",className:"bg-white rounded-lg shadow p-4",children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-3",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-xs text-gray-600",children:"Status"}),(0,b.jsxs)("select",{name:"status",defaultValue:n,className:"mt-1 w-full border rounded px-3 py-2",children:[(0,b.jsx)("option",{value:"",children:"All"}),["OPEN","IN_PROGRESS","WAITING_FOR_CUSTOMER","WAITING_FOR_AGENT","RESOLVED","CLOSED","CANCELLED"].map(a=>(0,b.jsx)("option",{value:a,children:a.replaceAll("_"," ")},a))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-xs text-gray-600",children:"Project"}),(0,b.jsxs)("select",{name:"projectId",defaultValue:o,className:"mt-1 w-full border rounded px-3 py-2",children:[(0,b.jsx)("option",{value:"",children:"All"}),t.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-xs text-gray-600",children:"Department"}),(0,b.jsxs)("select",{name:"departmentId",defaultValue:p,className:"mt-1 w-full border rounded px-3 py-2",children:[(0,b.jsx)("option",{value:"",children:"All"}),u.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-xs text-gray-600",children:"From"}),(0,b.jsx)("input",{type:"date",name:"from",defaultValue:q,className:"mt-1 w-full border rounded px-3 py-2"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-xs text-gray-600",children:"To"}),(0,b.jsx)("input",{type:"date",name:"to",defaultValue:r,className:"mt-1 w-full border rounded px-3 py-2"})]}),(0,b.jsxs)("div",{className:"flex items-end gap-2",children:[(0,b.jsx)("button",{type:"submit",className:"inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2",children:"Apply"}),(0,b.jsx)("a",{href:"/dashboard/reports",className:"inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-10 px-4 py-2",children:"Reset"})]})]})}),(0,b.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-6 gap-4",children:[{label:"Open",value:B},{label:"WIP",value:C},{label:"Resolved",value:D},{label:"Closed",value:E},{label:"Discarded",value:F},{label:"Total",value:A}].map(a=>(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("div",{className:"text-sm text-gray-500",children:a.label}),(0,b.jsx)("div",{className:"text-2xl font-bold",children:a.value})]},a.label))}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Tickets by Status"})}),(0,b.jsxs)("ul",{className:"divide-y",children:[v.sort((a,b)=>a.status.localeCompare(b.status)).map(a=>(0,b.jsxs)("li",{className:"p-3 flex items-center justify-between",children:[(0,b.jsx)("div",{className:"text-sm",children:a.status.replaceAll("_"," ")}),(0,b.jsx)("div",{className:"text-sm font-semibold",children:a._count._all})]},a.status)),0===v.length&&(0,b.jsx)("li",{className:"p-4 text-sm text-gray-500",children:"No data."})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Tickets by Department"})}),(0,b.jsxs)("ul",{className:"divide-y",children:[w.sort((a,b)=>(G.get(a.departmentId||"")||"").localeCompare(G.get(b.departmentId||"")||"")).map(a=>(0,b.jsxs)("li",{className:"p-3 flex items-center justify-between",children:[(0,b.jsx)("div",{className:"text-sm",children:G.get(a.departmentId||"")||"Unassigned"}),(0,b.jsx)("div",{className:"text-sm font-semibold",children:a._count._all})]},a.departmentId||"none")),0===w.length&&(0,b.jsx)("li",{className:"p-4 text-sm text-gray-500",children:"No data."})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Tickets by Project"})}),(0,b.jsxs)("ul",{className:"divide-y",children:[x.sort((a,b)=>(H.get(a.projectId||"")||"").localeCompare(H.get(b.projectId||"")||"")).map(a=>(0,b.jsxs)("li",{className:"p-3 flex items-center justify-between",children:[(0,b.jsx)("div",{className:"text-sm",children:H.get(a.projectId||"")||"Unassigned"}),(0,b.jsx)("div",{className:"text-sm font-semibold",children:a._count._all})]},a.projectId||"none")),0===x.length&&(0,b.jsx)("li",{className:"p-4 text-sm text-gray-500",children:"No data."})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Tickets Created Over Time"})}),(0,b.jsx)("div",{className:"p-4",children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700",children:[L.slice(-14).map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 rounded px-3 py-2",children:[(0,b.jsx)("span",{children:a.day}),(0,b.jsx)("span",{className:"font-semibold",children:a.count})]},a.day)),0===L.length&&(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"No tickets in this period."})]})})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d73d1ad2._.js.map