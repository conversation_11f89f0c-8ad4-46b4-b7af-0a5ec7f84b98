module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},45003,(e,t,r)=>{},75468,e=>{"use strict";e.s(["handler",()=>P,"patchFetch",()=>T,"routeModule",()=>j,"serverHooks",()=>b,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>C],75468);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),h=e.i(93695);e.i(52474);var f=e.i(220);e.s(["GET",()=>E,"POST",()=>I,"runtime",()=>y],52427);var R=e.i(89171),g=e.i(15270),v=e.i(65800),w=e.i(49632);let y="nodejs";async function E(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,v.verifyToken)(t):null;if(!r)return R.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),n=a.get("role"),s=a.get("projectId"),i=a.get("departmentId"),o={organizationId:r.organizationId};n&&(o.role=n),s&&(o.userProjects={some:{projectId:s}}),i&&(o.userDepartments={some:{departmentId:i}});let l=await g.prisma.user.findMany({where:o,select:{id:!0,email:!0,fullName:!0,role:!0,isActive:!0,userProjects:{select:{project:{select:{id:!0,name:!0}}}},userDepartments:{select:{department:{select:{id:!0,name:!0}}}}},orderBy:{createdAt:"desc"},take:200}),d=l.map(e=>e.id),u=(d.length?await g.prisma.userPermission.findMany({where:{userId:{in:d},organizationId:r.organizationId},select:{userId:!0,permission:!0}}):[]).reduce((e,t)=>(e[t.userId]||=[],e[t.userId].push({permission:t.permission}),e),{});return l=l.map(e=>({...e,userPermissions:u[e.id]||[]})),R.NextResponse.json({users:l})}async function I(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,v.verifyToken)(t):null;if(!r)return R.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role)return R.NextResponse.json({error:"Forbidden"},{status:403});let{email:a,fullName:n,password:s,role:i="CUSTOMER",projectIds:o=[],departmentIds:l=[]}=await e.json()||{};if(!a||!s)return R.NextResponse.json({error:"Email and password are required"},{status:400});if(!["ADMIN","MANAGER","AGENT","CUSTOMER"].includes(i))return R.NextResponse.json({error:"Invalid role"},{status:400});try{let t=await w.default.hash(s,12),d=await g.prisma.user.create({data:{email:a,password:t,fullName:n||null,role:i,organizationId:r.organizationId}});if(Array.isArray(o)&&o.length>0)for(let e of(await g.prisma.project.findMany({where:{id:{in:o},organizationId:r.organizationId},select:{id:!0}})))await g.prisma.userProject.upsert({where:{userId_projectId:{userId:d.id,projectId:e.id}},create:{userId:d.id,projectId:e.id},update:{}});if(Array.isArray(l)&&l.length>0)for(let e of(await g.prisma.department.findMany({where:{id:{in:l},organizationId:r.organizationId},select:{id:!0}})))await g.prisma.userDepartment.upsert({where:{userId_departmentId:{userId:d.id,departmentId:e.id}},create:{userId:d.id,departmentId:e.id},update:{}});let u=e.headers.get("x-forwarded-for")?.split(",")[0]||null;return await g.prisma.userAuditLog.create({data:{organizationId:r.organizationId,actorId:r.id,targetUserId:d.id,action:"USER_CREATE",after:{email:a,fullName:n||null,role:i,isActive:!0,projectIds:o,departmentIds:l},ip:u||void 0}}),R.NextResponse.json({user:{id:d.id,email:d.email,fullName:d.fullName,role:d.role}},{status:201})}catch(e){return R.NextResponse.json({error:e?.message||"Failed to create user"},{status:400})}}var A=e.i(52427);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/users/route.ts",nextConfigOutput:"export",userland:A}),{workAsyncStorage:N,workUnitAsyncStorage:C,serverHooks:b}=j;function T(){return(0,a.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:C})}async function P(e,t,a){var R;let g="/api/users/route";g=g.replace(/\/index$/,"")||"/";let v=await j.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:I,prerenderManifest:A,routerServerContext:N,isOnDemandRevalidate:C,revalidateOnlyGenerated:b,resolvedPathname:T}=v,P=(0,i.normalizeAppPath)(g),k=!!(A.dynamicRoutes[P]||A.routes[T]);if(k&&!I){let e=!!A.routes[T],t=A.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let q=null;!k||j.isDev||I||(q="/index"===(q=T)?"/":q);let O=!0===j.isDev||!k,M=k&&!O,U=e.method||"GET",_=(0,s.getTracer)(),S=_.getActiveScopeSpan(),D={params:y,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:M,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>j.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},H=new o.NodeNextRequest(e),z=new o.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let i=async r=>j.handle(F,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&C&&b&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=D.renderOpts.fetchMetrics;let l=D.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=D.renderOpts.collectedTags;if(!k)return await (0,p.sendResponse)(H,z,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,a=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:C})},N),t}},h=await j.handleResponse({req:e,nextConfig:E,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:b,responseGenerator:d,waitUntil:a.waitUntil});if(!k)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),I&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&k||R.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(H,z,new Response(h.value.body,{headers:R,status:h.value.status||200})),null};S?await o(S):await _.withPropagatedContext(e.headers,()=>_.trace(d.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(S||t instanceof h.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:M,isOnDemandRevalidate:C})}),k)throw t;return await (0,p.sendResponse)(H,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__7f7ae7fd._.js.map