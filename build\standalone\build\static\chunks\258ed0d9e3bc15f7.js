(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,28640,e=>{"use strict";e.s(["default",()=>a]);var t=e.i(43476),s=e.i(71645);function a(e){var a,l,r,n,d,i,o,c,m;let{initial:p}=e,[u,x]=(0,s.useState)(p||{}),[h,g]=(0,s.useState)([]),[v,j]=(0,s.useState)([]),[b,y]=(0,s.useState)(!1),[f,N]=(0,s.useState)(null),[w,k]=(0,s.useState)([]),[S,C]=(0,s.useState)({});function E(e,t){x(s=>({...s,[e]:t}))}async function T(e){e.preventDefault(),y(!0),N(null);try{let e={};for(let t of w){let s=(t.toEmail||"").trim().toLowerCase();s&&t.departmentId&&(e[s]=t.departmentId)}let t={...u,routingMap:e};if(!(await fetch("/api/email/config",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw Error("Save failed");N("Saved successfully")}catch(e){N((null==e?void 0:e.message)||"Failed to save")}finally{y(!1)}}return(0,s.useEffect)(()=>{!async function(){try{let[e,t]=await Promise.all([fetch("/api/projects").then(e=>e.json()).catch(()=>({projects:[]})),fetch("/api/departments").then(e=>e.json()).catch(()=>({departments:[]}))]);g(e.projects||[]),j(t.departments||[])}catch(e){}}()},[]),(0,s.useEffect)(()=>{let e=Object.entries(u.routingMap||{}).map(e=>{let[t,s]=e;return{toEmail:t,departmentId:String(s||"")}});0===e.length&&e.push({toEmail:"",departmentId:""}),k(e)},[u.routingMap]),(0,t.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,t.jsx)("h2",{className:"font-semibold",children:"Webhook"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"Webhook secret"}),(0,t.jsx)("input",{type:"text",value:u.webhookSecret||"",onChange:e=>E("webhookSecret",e.target.value),className:"mt-1 w-full border rounded px-3 py-2",placeholder:"e.g., random-long-secret"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Header: x-email-webhook-secret must match."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"Rate limit (per hour per sender)"}),(0,t.jsx)("input",{type:"number",value:null!=(c=u.rateLimitPerHour)?c:20,onChange:e=>E("rateLimitPerHour",Number(e.target.value)),className:"mt-1 w-full border rounded px-3 py-2"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,t.jsx)("h2",{className:"font-semibold",children:"Routing"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"Default Project"}),(0,t.jsxs)("select",{value:u.defaultProjectId||"",onChange:e=>E("defaultProjectId",e.target.value),className:"mt-1 w-full border rounded px-3 py-2",children:[(0,t.jsx)("option",{value:"",children:"— None —"}),h.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"Default Department"}),(0,t.jsxs)("select",{value:u.defaultDepartmentId||"",onChange:e=>E("defaultDepartmentId",e.target.value),className:"mt-1 w-full border rounded px-3 py-2",children:[(0,t.jsx)("option",{value:"",children:"— None —"}),v.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600 mb-2",children:"Address book (Recipient → Department)"}),(0,t.jsxs)("div",{className:"space-y-3",children:[w.map((e,s)=>(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-12 gap-2 items-center",children:[(0,t.jsx)("div",{className:"md:col-span-5",children:(0,t.jsx)("input",{placeholder:"<EMAIL>",className:"w-full border rounded px-3 py-2",value:e.toEmail,onChange:e=>k(t=>t.map((t,a)=>a===s?{...t,toEmail:e.target.value}:t))})}),(0,t.jsx)("div",{className:"md:col-span-4",children:(0,t.jsxs)("select",{className:"w-full border rounded px-3 py-2",value:e.departmentId,onChange:e=>k(t=>t.map((t,a)=>a===s?{...t,departmentId:e.target.value}:t)),children:[(0,t.jsx)("option",{value:"",children:"— Select department —"}),v.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})}),(0,t.jsxs)("div",{className:"md:col-span-4 flex flex-wrap gap-2",children:[(0,t.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:async()=>{let t=(e.toEmail||"").trim();if(!t)return void C(e=>({...e,[s]:"Enter a recipient email first"}));C(e=>({...e,[s]:"Testing routing..."}));try{let e=await fetch("/api/email/config/test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({to:t})}),a=await e.json();if(!e.ok)throw Error((null==a?void 0:a.error)||"Failed");let l=null==a?void 0:a.resolved;C(e=>({...e,[s]:"Department: ".concat((null==l?void 0:l.departmentName)||(null==l?void 0:l.departmentId)||"n/a"," • Project: ").concat((null==l?void 0:l.projectName)||(null==l?void 0:l.projectId)||"n/a")}))}catch(e){C(t=>({...t,[s]:(null==e?void 0:e.message)||"Test failed"}))}},children:"Test routing"}),(0,t.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:async()=>{let t=(e.toEmail||"").trim();if(!t)return void C(e=>({...e,[s]:"Enter a recipient email first"}));C(e=>({...e,[s]:"Sending test email..."}));try{let e=await fetch("/api/email/config/test/send-ack",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({to:t})}),a=await e.json();if(!e.ok)throw Error((null==a?void 0:a.error)||"Failed");C(e=>({...e,[s]:"Test email sent ✓ (messageId: ".concat((null==a?void 0:a.messageId)||"n/a",")")}))}catch(e){C(t=>({...t,[s]:(null==e?void 0:e.message)||"Send failed"}))}},children:"Send test email"}),(0,t.jsx)("button",{type:"button",className:"border rounded px-3 py-2 text-sm",onClick:()=>k(e=>e.filter((e,t)=>t!==s)),children:"Remove"})]}),S[s]&&(0,t.jsx)("div",{className:"md:col-span-12 text-xs text-gray-600",children:S[s]})]},s)),(0,t.jsx)("button",{type:"button",className:"inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-9 px-3 py-1",onClick:()=>k(e=>[...e,{toEmail:"",departmentId:""}]),children:"+ Add address"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-4 space-y-4",children:[(0,t.jsx)("h2",{className:"font-semibold",children:"Acknowledgement Email (SMTP)"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"SMTP Host"}),(0,t.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:(null==(a=u.smtp)?void 0:a.host)||"",onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},host:e.target.value}}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"SMTP Port"}),(0,t.jsx)("input",{type:"number",className:"mt-1 w-full border rounded px-3 py-2",value:null!=(m=null==(l=u.smtp)?void 0:l.port)?m:587,onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},port:Number(e.target.value)}}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"User"}),(0,t.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:(null==(r=u.smtp)?void 0:r.user)||"",onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},user:e.target.value}}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"Password"}),(0,t.jsx)("input",{type:"password",className:"mt-1 w-full border rounded px-3 py-2",value:(null==(n=u.smtp)?void 0:n.pass)||"",onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},pass:e.target.value}}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"From Email"}),(0,t.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:(null==(d=u.smtp)?void 0:d.fromEmail)||"",onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},fromEmail:e.target.value}}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm text-gray-600",children:"From Name"}),(0,t.jsx)("input",{className:"mt-1 w-full border rounded px-3 py-2",value:(null==(i=u.smtp)?void 0:i.fromName)||"",onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},fromName:e.target.value}}))})]}),(0,t.jsx)("div",{children:(0,t.jsxs)("label",{className:"inline-flex items-center text-sm text-gray-600 mt-6",children:[(0,t.jsx)("input",{type:"checkbox",className:"mr-2",checked:!!(null==(o=u.smtp)?void 0:o.secure),onChange:e=>x(t=>({...t,smtp:{...t.smtp||{},secure:e.target.checked}}))}),"Use TLS (secure)"]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("button",{type:"submit",disabled:b,className:"inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2",children:b?"Saving…":"Save configuration"}),f&&(0,t.jsx)("span",{className:"text-sm text-gray-600",children:f})]})]})}}]);