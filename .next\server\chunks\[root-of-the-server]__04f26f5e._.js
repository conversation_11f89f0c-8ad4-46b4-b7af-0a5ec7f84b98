module.exports=[14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},24868,(e,t,r)=>{t.exports=e.x("fs/promises",()=>require("fs/promises"))},87683,e=>{"use strict";e.s(["getUserPermissions",()=>a,"hasPermission",()=>i]);var t=e.i(15270);let r=["DASHBOARD_VIEW","TICKETS_VIEW","TICKETS_UPDATE","TICKETS_ASSIGN","REPORTS_VIEW","DEPARTMENTS_MANAGE","PROJECTS_MANAGE","AUDIT_VIEW"];async function a(e){return new Set("ADMIN"===e.role?r:(await t.prisma.userPermission.findMany({where:{userId:e.id,organizationId:e.organizationId},select:{permission:!0}})).map(e=>e.permission))}function i(e,t){return e.has(t)}},14151,(e,t,r)=>{},96199,e=>{"use strict";e.s(["handler",()=>b,"patchFetch",()=>k,"routeModule",()=>A,"serverHooks",()=>S,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>M],96199);var t=e.i(47909),r=e.i(74017),a=e.i(96250),i=e.i(59756),n=e.i(61916),s=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),p=e.i(47587),u=e.i(66012),c=e.i(70101),m=e.i(26937),x=e.i(10372),f=e.i(93695);e.i(52474);var g=e.i(220);e.s(["GET",()=>T,"POST",()=>N,"runtime",()=>y],75311);var h=e.i(89171),I=e.i(15270),R=e.i(65800),v=e.i(24868),w=e.i(14747),E=e.i(87683);let y="nodejs";async function T(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,R.verifyToken)(t):null;if(!r)return h.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==r.role){let e=await (0,E.getUserPermissions)({id:r.id,role:r.role,organizationId:r.organizationId});if(!(0,E.hasPermission)(e,"TICKETS_VIEW"))return h.NextResponse.json({error:"Forbidden: missing permission TICKETS_VIEW"},{status:403})}let a=(await I.prisma.userProject.findMany({where:{userId:r.id},select:{projectId:!0}})).map(e=>e.projectId),i=(await I.prisma.userDepartment.findMany({where:{userId:r.id},select:{departmentId:!0}})).map(e=>e.departmentId);if("ADMIN"!==r.role&&(0===a.length||0===i.length))return h.NextResponse.json({tickets:[]});let n=await I.prisma.ticket.findMany({where:{organizationId:r.organizationId,..."ADMIN"===r.role?{}:{projectId:{in:a},departmentId:{in:i}}},include:{createdBy:{select:{fullName:!0,email:!0}},assignedTo:{select:{fullName:!0,email:!0}}},orderBy:{createdAt:"desc"},take:50});return h.NextResponse.json({tickets:n})}async function N(e){let t,r,a,i=e.cookies.get("auth-token")?.value,n=i?(0,R.verifyToken)(i):null;if(!n)return h.NextResponse.json({error:"Unauthorized"},{status:401});let s=e.headers.get("content-type")||"",o="",l="",d="MEDIUM",p=[];if(s.includes("multipart/form-data")){let i=await e.formData();o=String(i.get("title")||""),l=String(i.get("description")||"");{let e=String(i.get("priority")||"MEDIUM").toUpperCase();d=["LOW","MEDIUM","HIGH","URGENT"].includes(e)?e:"MEDIUM"}t=i.get("category")?String(i.get("category")):void 0,r=i.get("projectId")?String(i.get("projectId")):void 0,a=i.get("departmentId")?String(i.get("departmentId")):void 0,p=i.getAll("images").filter(e=>"function"==typeof e?.arrayBuffer)}else{let i=await e.json();o=i?.title||"",l=i?.description||"";{let e=String(i?.priority||"MEDIUM").toUpperCase();d=["LOW","MEDIUM","HIGH","URGENT"].includes(e)?e:"MEDIUM"}t=i?.category,r=i?.projectId,a=i?.departmentId}if(!o)return h.NextResponse.json({error:"Title is required"},{status:400});if(!r)return h.NextResponse.json({error:"Project is required"},{status:400});if(!a)return h.NextResponse.json({error:"Department is required"},{status:400});if(!await I.prisma.userProject.findUnique({where:{userId_projectId:{userId:n.id,projectId:r}}}))return h.NextResponse.json({error:"Forbidden: not a member of the selected project"},{status:403});if(!await I.prisma.department.findFirst({where:{id:a,organizationId:n.organizationId},select:{id:!0}}))return h.NextResponse.json({error:"Invalid department"},{status:400});if(!await I.prisma.userDepartment.findUnique({where:{userId_departmentId:{userId:n.id,departmentId:a}}})&&"ADMIN"!==n.role)return h.NextResponse.json({error:"Forbidden: not a member of the selected department"},{status:403});let u=new Date().toISOString().replace(/[-:TZ.]/g,"").slice(0,14),c=Math.floor(1e4*Math.random()).toString().padStart(4,"0"),m=`TKT-${u}-${c}`,x=await I.prisma.ticket.create({data:{ticketNumber:m,title:o,description:l,priority:d,category:t,organizationId:n.organizationId,createdById:n.id,projectId:r,departmentId:a}});if(p&&p.length){let e=w.default.join(process.cwd(),"public","uploads");for(let t of(await (0,v.mkdir)(e,{recursive:!0}),p.slice(0,3))){let r=t.type||"application/octet-stream";if(!r.startsWith("image/"))continue;let a=await t.arrayBuffer(),i=Buffer.from(a),n=t.name||"image",s=n.replace(/[^a-zA-Z0-9._-]/g,"_"),o=`${Date.now()}-${Math.random().toString(36).slice(2,8)}-${s}`,l=w.default.join(e,o);await (0,v.writeFile)(l,i);let d=`/uploads/${o}`;await I.prisma.attachment.create({data:{filename:o,originalName:n,mimeType:r,size:i.length,url:d,ticketId:x.id}})}}return await I.prisma.ticketMessage.create({data:{content:`[AUDIT] Ticket created by ${n.fullName||n.email}. Priority=${d}${t?`, Category=${t}`:""}`,isInternal:!0,ticketId:x.id,authorId:n.id}}),h.NextResponse.json({ticket:x},{status:201})}var j=e.i(75311);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/tickets/route",pathname:"/api/tickets",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/tickets/route.ts",nextConfigOutput:"export",userland:j}),{workAsyncStorage:C,workUnitAsyncStorage:M,serverHooks:S}=A;function k(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:M})}async function b(e,t,a){var h;let I="/api/tickets/route";I=I.replace(/\/index$/,"")||"/";let R=await A.prepare(e,t,{srcPage:I,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:E,isDraftMode:y,prerenderManifest:T,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:C,resolvedPathname:M}=R,S=(0,s.normalizeAppPath)(I),k=!!(T.dynamicRoutes[S]||T.routes[M]);if(k&&!y){let e=!!T.routes[M],t=T.dynamicRoutes[S];if(t&&!1===t.fallback&&!e)throw new f.NoFallbackError}let b=null;!k||A.isDev||y||(b="/index"===(b=M)?"/":b);let P=!0===A.isDev||!k,D=k&&!P,U=e.method||"GET",q=(0,n.getTracer)(),_=q.getActiveScopeSpan(),O={params:w,prerenderManifest:T,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(h=E.experimental)?void 0:h.cacheLife,isRevalidate:D,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>A.onRequestError(e,t,a,N)},sharedContext:{buildId:v}},H=new o.NodeNextRequest(e),$=new o.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let s=async r=>A.handle(z,O).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=q.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=a.get("next.route");if(i){let e=`${U} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async n=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&j&&C&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await s(n);e.fetchMetrics=O.renderOpts.fetchMetrics;let l=O.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=O.renderOpts.collectedTags;if(!k)return await (0,u.sendResponse)(H,$,o,O.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[x.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==O.renderOpts.collectedRevalidate&&!(O.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&O.renderOpts.collectedRevalidate,a=void 0===O.renderOpts.collectedExpire||O.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:O.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:j})},N),t}},f=await A.handleResponse({req:e,nextConfig:E,cacheKey:b,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:T,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:C,responseGenerator:d,waitUntil:a.waitUntil});if(!k)return null;if((null==f||null==(o=f.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==f||null==(l=f.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":f.isMiss?"MISS":f.isStale?"STALE":"HIT"),y&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let h=(0,c.fromNodeOutgoingHttpHeaders)(f.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&k||h.delete(x.NEXT_CACHE_TAGS_HEADER),!f.cacheControl||t.getHeader("Cache-Control")||h.get("Cache-Control")||h.set("Cache-Control",(0,m.getCacheControlHeader)(f.cacheControl)),await (0,u.sendResponse)(H,$,new Response(f.value.body,{headers:h,status:f.value.status||200})),null};_?await o(_):await q.withPropagatedContext(e.headers,()=>q.trace(d.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(_||t instanceof f.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:S,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:j})}),k)throw t;return await (0,u.sendResponse)(H,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__04f26f5e._.js.map