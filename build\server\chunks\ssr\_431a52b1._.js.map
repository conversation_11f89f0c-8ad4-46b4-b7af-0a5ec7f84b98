{"version": 3, "sources": ["turbopack:///[project]/components/ui/input.tsx", "turbopack:///[project]/components/ui/button.tsx", "turbopack:///[project]/src/app/dashboard/users/CreateUserForm.tsx", "turbopack:///[project]/src/app/dashboard/users/UserAdminList.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      default: \"bg-blue-600 text-white hover:bg-blue-700\",\n      destructive: \"bg-red-600 text-white hover:bg-red-700\",\n      outline: \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n      ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n      link: \"text-blue-600 underline-offset-4 hover:underline\",\n    }\n    \n    const sizes = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n", "\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\n\ntype Project = { id: string; name: string }\ntype Department = { id: string; name: string }\n\ntype Props = {}\n\nexport default function CreateUserForm(_: Props) {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [departments, setDepartments] = useState<Department[]>([])\n  const [email, setEmail] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [password, setPassword] = useState('')\n  const [role, setRole] = useState<'ADMIN'|'AGENT'|'CUSTOMER'>('CUSTOMER')\n  const [selectedProjects, setSelectedProjects] = useState<string[]>([])\n  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n\n  useEffect(() => {\n    (async () => {\n      try {\n        const [prjRes, deptRes] = await Promise.all([\n          fetch('/api/projects?all=1'),\n          fetch('/api/departments'),\n        ])\n        const prjData = await prjRes.json()\n        const deptData = await deptRes.json()\n        if (prjRes.ok) {\n          setProjects(prjData.projects || [])\n        } else {\n          setError(prjData.error || 'Failed to load projects')\n        }\n        if (deptRes.ok) {\n          setDepartments(deptData.departments || [])\n        } else if (!error) {\n          setError(deptData.error || 'Failed to load departments')\n        }\n      } catch {\n        setError('Failed to load projects/departments')\n      }\n    })()\n  }, [])\n\n  const toggleProject = (id: string) => {\n    setSelectedProjects(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])\n  }\n  const toggleDepartment = (id: string) => {\n    setSelectedDepartments(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])\n  }\n\n  const submit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n    setSuccess('')\n    try {\n      const res = await fetch('/api/users', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email, fullName, password, role, projectIds: selectedProjects, departmentIds: selectedDepartments }),\n      })\n      const data = await res.json()\n      if (!res.ok) {\n        setError(data.error || 'Failed to create user')\n        return\n      }\n      setSuccess('User created')\n      setEmail(''); setFullName(''); setPassword(''); setRole('CUSTOMER'); setSelectedProjects([]); setSelectedDepartments([])\n    } catch {\n      setError('Unexpected error')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={submit} className=\"space-y-4\">\n      {error && <div className=\"bg-red-50 text-red-700 border border-red-200 p-3 rounded\">{error}</div>}\n      {success && <div className=\"bg-green-50 text-green-700 border border-green-200 p-3 rounded\">{success}</div>}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n        <Input type=\"email\" value={email} onChange={e=>setEmail(e.target.value)} required className=\"mt-1\" />\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n        <Input value={fullName} onChange={e=>setFullName(e.target.value)} className=\"mt-1\" />\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n        <Input type=\"password\" value={password} onChange={e=>setPassword(e.target.value)} required className=\"mt-1\" />\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Role</label>\n        <select value={role} onChange={e=>setRole(e.target.value as any)} className=\"mt-1 w-full border rounded p-2\">\n          <option value=\"CUSTOMER\">Customer</option>\n          <option value=\"AGENT\">Agent</option>\n          <option value=\"MANAGER\">Manager</option>\n          <option value=\"ADMIN\">Admin</option>\n        </select>\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Assign Projects</label>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded\">\n          {projects.map(p => (\n            <label key={p.id} className=\"flex items-center gap-2 text-sm\">\n              <input type=\"checkbox\" checked={selectedProjects.includes(p.id)} onChange={() => toggleProject(p.id)} />\n              <span>{p.name}</span>\n            </label>\n          ))}\n          {projects.length === 0 && <div className=\"text-xs text-gray-500\">No projects found</div>}\n        </div>\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Assign Departments</label>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded\">\n          {departments.map(d => (\n            <label key={d.id} className=\"flex items-center gap-2 text-sm\">\n              <input type=\"checkbox\" checked={selectedDepartments.includes(d.id)} onChange={() => toggleDepartment(d.id)} />\n              <span>{d.name}</span>\n            </label>\n          ))}\n          {departments.length === 0 && <div className=\"text-xs text-gray-500\">No departments found</div>}\n        </div>\n      </div>\n      <div className=\"flex gap-2\">\n        <Button type=\"submit\" disabled={loading}>{loading ? 'Creating...' : 'Create User'}</Button>\n      </div>\n    </form>\n  )\n}\n\n", "\"use client\"\n\nimport { useEffect, useMemo, useState } from 'react'\nimport { Button } from '@/components/ui/button'\n\ntype PermissionKey =\n  | 'DASHBOARD_VIEW'\n  | 'TICKETS_VIEW'\n  | 'TICKETS_UPDATE'\n  | 'TICKETS_ASSIGN'\n  | 'REPORTS_VIEW'\n  | 'DEPARTMENTS_MANAGE'\n  | 'PROJECTS_MANAGE'\n  | 'AUDIT_VIEW'\n\nconst ALL_PERMISSIONS: { key: PermissionKey; label: string }[] = [\n  { key: 'DASHBOARD_VIEW', label: 'Dashboard View' },\n  { key: 'TICKETS_VIEW', label: 'Tickets: View' },\n  { key: 'TICKETS_UPDATE', label: 'Tickets: Update' },\n  { key: 'TICKETS_ASSIGN', label: 'Tickets: Assign' },\n  { key: 'REPORTS_VIEW', label: 'Reports: View' },\n  { key: 'DEPARTMENTS_MANAGE', label: 'Departments: Manage' },\n  { key: 'PROJECTS_MANA<PERSON>', label: 'Projects: Manage' },\n  { key: 'AUDIT_VIEW', label: 'Audit: View' },\n]\n\ntype User = {\n  id: string\n  email: string\n  fullName: string | null\n  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'\n  isActive: boolean\n  userProjects: { project: { id: string, name: string } }[]\n  userDepartments: { department: { id: string, name: string } }[]\n  userPermissions: { permission: PermissionKey }[]\n}\n\ntype Project = { id: string; name: string }\ntype Department = { id: string; name: string }\n\nexport default function UserAdminList() {\n  const [users, setUsers] = useState<User[]>([])\n  const [projects, setProjects] = useState<Project[]>([])\n  const [departments, setDepartments] = useState<Department[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [savingId, setSavingId] = useState<string | null>(null)\n\n  const load = async () => {\n    setLoading(true)\n    setError('')\n    try {\n      const [uRes, pRes, dRes] = await Promise.all([\n        fetch('/api/users'),\n        fetch('/api/projects?all=1'),\n        fetch('/api/departments'),\n      ])\n      const [uData, pData, dData] = await Promise.all([uRes.json(), pRes.json(), dRes.json()])\n      if (!uRes.ok) throw new Error(uData.error || 'Failed to load users')\n      if (!pRes.ok) throw new Error(pData.error || 'Failed to load projects')\n      if (!dRes.ok) throw new Error(dData.error || 'Failed to load departments')\n      setUsers(uData.users || [])\n      setProjects(pData.projects || [])\n      setDepartments(dData.departments || [])\n    } catch (e: any) {\n      setError(e?.message || 'Failed to load')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => { load() }, [])\n\n  const updateUser = async (u: User, updates: Partial<User> & { projectIds?: string[], departmentIds?: string[], permissions?: PermissionKey[] }) => {\n    setSavingId(u.id)\n    setError('')\n    try {\n      const res = await fetch(`/api/users/${u.id}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updates),\n      })\n      const data = await res.json()\n      if (!res.ok) throw new Error(data.error || 'Failed to update user')\n      await load()\n    } catch (e: any) {\n      setError(e?.message || 'Failed to update user')\n    } finally {\n      setSavingId(null)\n    }\n  }\n\n  const projectIds = useMemo(() => new Set(projects.map(p => p.id)), [projects])\n  const departmentIds = useMemo(() => new Set(departments.map(d => d.id)), [departments])\n\n  if (loading) return <div className=\"p-4\">Loading users...</div>\n  if (error) return <div className=\"p-4 text-red-600\">{error}</div>\n\n  return (\n    <div className=\"bg-white rounded-lg shadow\">\n      <div className=\"p-4 border-b flex items-center justify-between\">\n        <h2 className=\"font-semibold\">Manage Users</h2>\n        <Button variant=\"outline\" onClick={load}>Refresh</Button>\n      </div>\n      <div className=\"p-4 overflow-x-auto\">\n        <table className=\"min-w-full text-sm\">\n          <thead>\n            <tr className=\"text-left text-gray-500\">\n              <th className=\"p-2\">Name</th>\n              <th className=\"p-2\">Email</th>\n              <th className=\"p-2\">Role</th>\n              <th className=\"p-2\">Projects</th>\n              <th className=\"p-2\">Departments</th>\n              <th className=\"p-2\">Permissions</th>\n              <th className=\"p-2\">Active</th>\n              <th className=\"p-2 text-right\">Actions</th>\n            </tr>\n          </thead>\n          <tbody>\n            {users.map(u => {\n              const selProjectIds = new Set(u.userProjects.map(up => up.project.id).filter(id => projectIds.has(id)))\n              const selDeptIds = new Set(u.userDepartments.map(ud => ud.department.id).filter(id => departmentIds.has(id)))\n              return (\n                <tr key={u.id} className=\"border-t\">\n                  <td className=\"p-2\">{u.fullName || '—'}</td>\n                  <td className=\"p-2\">{u.email}</td>\n                  <td className=\"p-2\">\n                    <select value={u.role} onChange={e => updateUser(u, { role: e.target.value as any })} className=\"border rounded p-1\">\n                      <option value=\"CUSTOMER\">Customer</option>\n                      <option value=\"AGENT\">Agent</option>\n                      <option value=\"MANAGER\">Manager</option>\n                      <option value=\"ADMIN\">Admin</option>\n                    </select>\n                  </td>\n                  <td className=\"p-2\">\n                    <div className=\"max-w-xs grid grid-cols-1 gap-1\">\n                      {projects.map(p => (\n                        <label key={p.id} className=\"flex items-center gap-2\">\n                          <input type=\"checkbox\" checked={selProjectIds.has(p.id)} onChange={()=>{\n                            const updated = new Set(selProjectIds)\n                            if (updated.has(p.id)) updated.delete(p.id); else updated.add(p.id)\n                            updateUser(u, { projectIds: Array.from(updated) })\n                          }} />\n                          <span className=\"truncate\">{p.name}</span>\n                        </label>\n                      ))}\n                    </div>\n                  </td>\n                  <td className=\"p-2\">\n                    <div className=\"max-w-xs grid grid-cols-1 gap-1\">\n                      {departments.map(d => (\n                        <label key={d.id} className=\"flex items-center gap-2\">\n                          <input type=\"checkbox\" checked={selDeptIds.has(d.id)} onChange={()=>{\n                            const updated = new Set(selDeptIds)\n                            if (updated.has(d.id)) updated.delete(d.id); else updated.add(d.id)\n                            updateUser(u, { departmentIds: Array.from(updated) })\n                          }} />\n                          <span className=\"truncate\">{d.name}</span>\n                        </label>\n                      ))}\n                    </div>\n                  </td>\n                  <td className=\"p-2\">\n                    <div className=\"max-w-xs grid grid-cols-1 gap-1\">\n                      {ALL_PERMISSIONS.map(p => {\n                        const sel = new Set((u.userPermissions || []).map(up => up.permission))\n                        const checked = sel.has(p.key)\n                        return (\n                          <label key={p.key} className=\"flex items-center gap-2\">\n                            <input type=\"checkbox\" checked={checked} onChange={()=>{\n                              if (checked) sel.delete(p.key); else sel.add(p.key)\n                              updateUser(u, { permissions: Array.from(sel) })\n                            }} />\n                            <span className=\"truncate\">{p.label}</span>\n                          </label>\n                        )\n                      })}\n                    </div>\n                  </td>\n                  <td className=\"p-2\">\n                    <input type=\"checkbox\" checked={u.isActive} onChange={()=> updateUser(u, { isActive: !u.isActive })} />\n                  </td>\n                  <td className=\"p-2 text-right\">\n                    <Button size=\"sm\" disabled={savingId===u.id} variant=\"outline\" onClick={()=>updateUser(u, { fullName: u.fullName || '' })}>\n                      {savingId===u.id ? 'Saving...' : 'Save'}\n                    </Button>\n                  </td>\n                </tr>\n              )\n            })}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": "4EAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAKA,IAAM,EAAQ,EAAA,UAAgB,CAC5B,CAAC,WAAE,CAAS,CAAE,MAAI,CAAE,GAAG,EAAO,CAAE,IAE5B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAM,EACN,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,kUACA,GAEF,IAAK,EACJ,GAAG,CAAK,IAKjB,EAAM,WAAW,CAAG,uECrBpB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAQA,IAAM,EAAS,EAAA,UAAgB,CAC7B,CAAC,WAAE,CAAS,SAAE,EAAU,SAAS,MAAE,EAAO,SAAS,CAAE,GAAG,EAAO,CAAE,IAoB7D,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EApBG,AAqBd,yRAnBW,AAoBX,CAnBJ,QAAS,2CACT,YAAa,yCACb,QAAS,uEACT,UAAW,8CACX,MAAO,wCACP,KAAM,kDACR,CAac,CAAC,EAAQ,CAXT,AAYR,CAXJ,QAAS,iBACT,GAAI,sBACJ,GAAI,uBACJ,KAAM,WACR,CAOW,CAAC,EAAK,CACX,GAEF,IAAK,EACJ,GAAG,CAAK,IAKjB,EAAO,WAAW,CAAG,yECzCrB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAOe,SAAS,EAAe,CAAQ,EAC7C,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EACzD,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACnC,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,IACnC,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAA6B,YACvD,CAAC,EAAkB,EAAoB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAmB,EAAE,EAC/D,CAAC,EAAqB,EAAuB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAW,EAAE,EACrE,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAEvC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,CAAC,UACC,GAAI,CACF,GAAM,CAAC,EAAQ,EAAQ,CAAG,MAAM,QAAQ,GAAG,CAAC,CAC1C,MAAM,uBACN,MAAM,oBACP,EACK,EAAU,MAAM,EAAO,IAAI,GAC3B,EAAW,MAAM,EAAQ,IAAI,GAC/B,EAAO,EAAE,CACX,CADa,CACD,EAAQ,QAAQ,EAAI,EAAE,EAElC,EAAS,EAAQ,KAAK,EAAI,2BAExB,EAAQ,EAAE,CACZ,CADc,CACC,EAAS,WAAW,EAAI,EAAE,EAChC,AAAC,GACV,EAAS,EADQ,AACC,KAAK,EAAI,6BAE/B,CAAE,KAAM,CACN,EAAS,sCACX,EACF,CAAC,EACH,EAAG,EAAE,EASL,IAAM,EAAS,MAAO,IACpB,EAAE,cAAc,GAChB,GAAW,GACX,EAAS,IACT,EAAW,IACX,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,aAAc,CACpC,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,OAAE,WAAO,WAAU,OAAU,EAAM,WAAY,EAAkB,cAAe,CAAoB,EAC3H,GACM,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,YACX,EAAS,EAAK,KAAK,EAAI,yBAGzB,EAAW,gBACX,EAAS,IAAK,EAAY,IAAK,EAAY,IAAK,EAAQ,YAAa,EAAoB,EAAE,EAAG,EAAuB,EAAE,CACzH,CAAE,KAAM,CACN,EAAS,mBACX,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAQ,UAAU,sBAC/B,GAAS,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEAA4D,IACpF,GAAW,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EAAkE,IAC7F,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,UAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,KAAK,QAAQ,MAAO,EAAO,SAAU,GAAG,EAAS,EAAE,MAAM,CAAC,KAAK,EAAG,QAAQ,CAAA,CAAA,EAAC,UAAU,YAE9F,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,cAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,MAAO,EAAU,SAAU,GAAG,EAAY,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,YAE9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,aAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,KAAK,WAAW,MAAO,EAAU,SAAU,GAAG,EAAY,EAAE,MAAM,CAAC,KAAK,EAAG,QAAQ,CAAA,CAAA,EAAC,UAAU,YAEvG,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,SAC3D,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,MAAO,EAAM,SAAU,GAAG,EAAQ,EAAE,MAAM,CAAC,KAAK,EAAU,UAAU,2CAC1E,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,oBAAW,aACzB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,UACtB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,YACxB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,gBAG1B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,wDAA+C,oBAChE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qEACZ,EAAS,GAAG,CAAC,GACZ,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAiB,UAAU,4CAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAiB,QAAQ,CAAC,EAAE,EAAE,EAAG,SAAU,IAAM,cA9DtE,EA8DoF,EAAE,EAAE,MA7D7G,EAAoB,GAAQ,EAAK,QAAQ,CAAC,GAAM,EAAK,MAAM,CAAC,GAAK,IAAM,GAAM,IAAI,EAAM,EAAG,KA8DhF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAE,IAAI,KAFH,EAAE,EAAE,GAKG,IAApB,EAAS,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,4BAGrE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,wDAA+C,uBAChE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qEACZ,EAAY,GAAG,CAAC,GACf,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAiB,UAAU,4CAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAoB,QAAQ,CAAC,EAAE,EAAE,EAAG,SAAU,IAAM,cAvEtE,EAuEuF,EAAE,EAAE,MAtEnH,EAAuB,GAAQ,EAAK,QAAQ,CAAC,GAAM,EAAK,MAAM,CAAC,GAAK,IAAM,GAAM,IAAI,EAAM,EAAG,KAuEnF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAE,IAAI,KAFH,EAAE,EAAE,GAKM,IAAvB,EAAY,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,+BAGxE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,SAAS,SAAU,WAAU,EAAU,cAAgB,oBAI5E,kECrIA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAYA,IAAM,EAA2D,CAC/D,CAAE,IAAK,iBAAkB,MAAO,gBAAiB,EACjD,CAAE,IAAK,eAAgB,MAAO,eAAgB,EAC9C,CAAE,IAAK,iBAAkB,MAAO,iBAAkB,EAClD,CAAE,IAAK,iBAAkB,MAAO,iBAAkB,EAClD,CAAE,IAAK,eAAgB,MAAO,eAAgB,EAC9C,CAAE,IAAK,qBAAsB,MAAO,qBAAsB,EAC1D,CAAE,IAAK,kBAAmB,MAAO,kBAAmB,EACpD,CAAE,IAAK,aAAc,MAAO,aAAc,EAC3C,CAgBc,SAAS,IACtB,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAS,EAAE,EACvC,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EACzD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAgB,MAElD,EAAO,UACX,GAAW,GACX,EAAS,IACT,GAAI,CACF,GAAM,CAAC,EAAM,EAAM,EAAK,CAAG,MAAM,QAAQ,GAAG,CAAC,CAC3C,MAAM,cACN,MAAM,uBACN,MAAM,oBACP,EACK,CAAC,EAAO,EAAO,EAAM,CAAG,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAK,IAAI,GAAI,EAAK,IAAI,GAAI,EAAK,IAAI,GAAG,EACvF,GAAI,CAAC,EAAK,EAAE,CAAE,MAAM,AAAI,MAAM,EAAM,KAAK,EAAI,wBAC7C,GAAI,CAAC,EAAK,EAAE,CAAE,MAAM,AAAI,MAAM,EAAM,KAAK,EAAI,2BAC7C,GAAI,CAAC,EAAK,EAAE,CAAE,MAAM,AAAI,MAAM,EAAM,KAAK,EAAI,8BAC7C,EAAS,EAAM,KAAK,EAAI,EAAE,EAC1B,EAAY,EAAM,QAAQ,EAAI,EAAE,EAChC,EAAe,EAAM,WAAW,EAAI,EAAE,CACxC,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,iBACzB,QAAU,CACR,GAAW,EACb,CACF,EAEA,CAAA,EAAA,EAAA,SAAS,AAAT,EAAU,KAAQ,GAAO,EAAG,EAAE,EAE9B,IAAM,EAAa,MAAO,EAAS,KACjC,EAAY,EAAE,EAAE,EAChB,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,CAAC,WAAW,EAAE,EAAE,EAAE,CAAA,CAAE,CAAE,CAC5C,OAAQ,QACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,EACvB,GACM,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,EAAK,KAAK,EAAI,wBAC3C,OAAM,GACR,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,wBACzB,QAAU,CACR,EAAY,KACd,CACF,EAEM,EAAa,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,IAAM,IAAI,IAAI,EAAS,GAAG,CAAC,GAAK,EAAE,EAAE,GAAI,CAAC,EAAS,EACvE,EAAgB,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,IAAM,IAAI,IAAI,EAAY,GAAG,CAAC,GAAK,EAAE,EAAE,GAAI,CAAC,EAAY,SAEtF,AAAI,EAAgB,CAAA,EAAA,EAAA,EAAP,CAAO,EAAC,MAAA,CAAI,UAAU,eAAM,qBACrC,EAAc,CAAA,EAAA,EAAP,AAAO,GAAA,EAAC,MAAA,CAAI,UAAU,4BAAoB,IAGnD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,iBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,QAAS,WAAM,eAE3C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+BACb,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,+BACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,oCACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,SACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,UACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,SACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,aACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,gBACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAM,WACpB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0BAAiB,iBAGnC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UACE,EAAM,GAAG,CAAC,IACT,IAAM,EAAgB,IAAI,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,GAAM,EAAG,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,GAAM,EAAW,GAAG,CAAC,KAC5F,EAAa,IAAI,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,GAAM,EAAG,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,GAAM,EAAc,GAAG,CAAC,KACxG,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAc,UAAU,qBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAO,EAAE,QAAQ,EAAI,MACnC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eAAO,EAAE,KAAK,GAC5B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,MAAO,EAAE,IAAI,CAAE,SAAU,GAAK,EAAW,EAAG,CAAE,KAAM,EAAE,MAAM,CAAC,KAAK,AAAQ,GAAI,UAAU,+BAC9F,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,oBAAW,aACzB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,UACtB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,mBAAU,YACxB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,iBAAQ,eAG1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACZ,EAAS,GAAG,CAAC,GACZ,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAiB,UAAU,oCAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAc,GAAG,CAAC,EAAE,EAAE,EAAG,SAAU,KACjE,IAAM,EAAU,IAAI,IAAI,GACpB,EAAQ,GAAG,CAAC,EAAE,EAAE,EAAG,EAAQ,MAAM,CAAC,EAAE,EAAE,EAAQ,EAAQ,GAAG,CAAC,EAAE,EAAE,EAClE,EAAW,EAAG,CAAE,WAAY,MAAM,IAAI,CAAC,EAAS,EAClD,IACA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oBAAY,EAAE,IAAI,KANxB,EAAE,EAAE,OAWtB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACZ,EAAY,GAAG,CAAC,GACf,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAiB,UAAU,oCAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAW,GAAG,CAAC,EAAE,EAAE,EAAG,SAAU,KAC9D,IAAM,EAAU,IAAI,IAAI,GACpB,EAAQ,GAAG,CAAC,EAAE,EAAE,EAAG,EAAQ,MAAM,CAAC,EAAE,EAAE,EAAQ,EAAQ,GAAG,CAAC,EAAE,EAAE,EAClE,EAAW,EAAG,CAAE,cAAe,MAAM,IAAI,CAAC,EAAS,EACrD,IACA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oBAAY,EAAE,IAAI,KANxB,EAAE,EAAE,OAWtB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACZ,EAAgB,GAAG,CAAC,IACnB,IAAM,EAAM,IAAI,IAAI,CAAC,EAAE,eAAe,EAAI,EAAA,AAAE,EAAE,GAAG,CAAC,GAAM,EAAG,UAAU,GAC/D,EAAU,EAAI,GAAG,CAAC,EAAE,GAAG,EAC7B,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAkB,UAAU,oCAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAS,SAAU,KAC7C,EAAS,EAAI,MAAM,CAAC,EAAE,GAAG,EAAQ,EAAI,GAAG,CAAC,EAAE,GAAG,EAClD,EAAW,EAAG,CAAE,YAAa,MAAM,IAAI,CAAC,EAAK,EAC/C,IACA,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oBAAY,EAAE,KAAK,KALzB,EAAE,GAAG,CAQrB,OAGJ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,eACZ,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,QAAS,EAAE,QAAQ,CAAE,SAAU,IAAK,EAAW,EAAG,CAAE,SAAU,CAAC,EAAE,QAAQ,AAAC,OAEnG,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0BACZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,KAAK,SAAU,IAAW,EAAE,EAAE,CAAE,QAAQ,UAAU,QAAS,IAAI,EAAW,EAAG,CAAE,SAAU,EAAE,QAAQ,EAAI,EAAG,YACpH,IAAW,EAAE,EAAE,CAAG,YAAc,aA7D9B,EAAE,EAAE,CAkEjB,YAMZ"}