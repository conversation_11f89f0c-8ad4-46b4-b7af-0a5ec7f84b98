// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/auth/login/page.tsx
{
  const handler = {} as typeof import("../../src/app/auth/login/page.js")
  handler satisfies AppPageConfig<"/auth/login">
}

// Validate ../../src/app/auth/signup/page.tsx
{
  const handler = {} as typeof import("../../src/app/auth/signup/page.js")
  handler satisfies AppPageConfig<"/auth/signup">
}

// Validate ../../src/app/dashboard/departments/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/departments/page.js")
  handler satisfies AppPageConfig<"/dashboard/departments">
}

// Validate ../../src/app/dashboard/email-config/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/email-config/page.js")
  handler satisfies AppPageConfig<"/dashboard/email-config">
}

// Validate ../../src/app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../src/app/dashboard/reports/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/reports/page.js")
  handler satisfies AppPageConfig<"/dashboard/reports">
}

// Validate ../../src/app/dashboard/tickets/[id]/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/tickets/[id]/page.js")
  handler satisfies AppPageConfig<"/dashboard/tickets/[id]">
}

// Validate ../../src/app/dashboard/tickets/new/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/tickets/new/page.js")
  handler satisfies AppPageConfig<"/dashboard/tickets/new">
}

// Validate ../../src/app/dashboard/tickets/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/tickets/page.js")
  handler satisfies AppPageConfig<"/dashboard/tickets">
}

// Validate ../../src/app/dashboard/users/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/users/page.js")
  handler satisfies AppPageConfig<"/dashboard/users">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/api/audit/users/route.ts
{
  const handler = {} as typeof import("../../src/app/api/audit/users/route.js")
  handler satisfies RouteHandlerConfig<"/api/audit/users">
}

// Validate ../../src/app/api/auth/login/route.ts
{
  const handler = {} as typeof import("../../src/app/api/auth/login/route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/login">
}

// Validate ../../src/app/api/auth/logout/route.ts
{
  const handler = {} as typeof import("../../src/app/api/auth/logout/route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/logout">
}

// Validate ../../src/app/api/departments/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/departments/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/departments/[id]">
}

// Validate ../../src/app/api/departments/route.ts
{
  const handler = {} as typeof import("../../src/app/api/departments/route.js")
  handler satisfies RouteHandlerConfig<"/api/departments">
}

// Validate ../../src/app/api/email/config/route.ts
{
  const handler = {} as typeof import("../../src/app/api/email/config/route.js")
  handler satisfies RouteHandlerConfig<"/api/email/config">
}

// Validate ../../src/app/api/email/config/test/route.ts
{
  const handler = {} as typeof import("../../src/app/api/email/config/test/route.js")
  handler satisfies RouteHandlerConfig<"/api/email/config/test">
}

// Validate ../../src/app/api/email/config/test/send-ack/route.ts
{
  const handler = {} as typeof import("../../src/app/api/email/config/test/send-ack/route.js")
  handler satisfies RouteHandlerConfig<"/api/email/config/test/send-ack">
}

// Validate ../../src/app/api/email/inbound/route.ts
{
  const handler = {} as typeof import("../../src/app/api/email/inbound/route.js")
  handler satisfies RouteHandlerConfig<"/api/email/inbound">
}

// Validate ../../src/app/api/me/route.ts
{
  const handler = {} as typeof import("../../src/app/api/me/route.js")
  handler satisfies RouteHandlerConfig<"/api/me">
}

// Validate ../../src/app/api/projects/route.ts
{
  const handler = {} as typeof import("../../src/app/api/projects/route.js")
  handler satisfies RouteHandlerConfig<"/api/projects">
}

// Validate ../../src/app/api/tickets/[id]/messages/route.ts
{
  const handler = {} as typeof import("../../src/app/api/tickets/[id]/messages/route.js")
  handler satisfies RouteHandlerConfig<"/api/tickets/[id]/messages">
}

// Validate ../../src/app/api/tickets/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/tickets/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/tickets/[id]">
}

// Validate ../../src/app/api/tickets/route.ts
{
  const handler = {} as typeof import("../../src/app/api/tickets/route.js")
  handler satisfies RouteHandlerConfig<"/api/tickets">
}

// Validate ../../src/app/api/users/[id]/route.ts
{
  const handler = {} as typeof import("../../src/app/api/users/[id]/route.js")
  handler satisfies RouteHandlerConfig<"/api/users/[id]">
}

// Validate ../../src/app/api/users/route.ts
{
  const handler = {} as typeof import("../../src/app/api/users/route.js")
  handler satisfies RouteHandlerConfig<"/api/users">
}





// Validate ../../src/app/dashboard/layout.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/layout.js")
  handler satisfies LayoutConfig<"/dashboard">
}

// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
