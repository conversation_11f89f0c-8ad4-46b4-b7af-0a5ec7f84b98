(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,62892,e=>{"use strict";e.s(["default",()=>s]);var t=e.i(43476),a=e.i(71645);function s(e){let{ticketId:s}=e,[r,l]=(0,a.useState)(""),[d,n]=(0,a.useState)(!1),[i,c]=(0,a.useState)(""),o=async e=>{e.preventDefault(),n(!0),c("");try{let e=await fetch("/api/tickets/".concat(s,"/messages"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r})});if(!e.ok){let t=await e.json().catch(()=>({}));c(t.error||"Failed to send message");return}l(""),window.location.reload()}catch(e){c("Unexpected error")}finally{n(!1)}};return(0,t.jsxs)("form",{onSubmit:o,className:"space-y-3",children:[i&&(0,t.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:i}),(0,t.jsx)("textarea",{value:r,onChange:e=>l(e.target.value),placeholder:"Write a message...",className:"w-full border rounded p-2 h-28",required:!0}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:d,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:d?"Sending...":"Send"})})]})}},61023,e=>{"use strict";e.s(["default",()=>s]);var t=e.i(43476),a=e.i(71645);function s(e){let{ticketId:s,initial:r,role:l}=e,[d,n]=(0,a.useState)(r.status),[i,c]=(0,a.useState)(r.priority),[o,u]=(0,a.useState)(r.departmentId||""),[m,p]=(0,a.useState)(r.assignedToId||""),[h,x]=(0,a.useState)([]),[N,f]=(0,a.useState)([]),[j,b]=(0,a.useState)(!1),[g,S]=(0,a.useState)("");(0,a.useEffect)(()=>{("ADMIN"===l||"MANAGER"===l)&&(async()=>{try{let e=await fetch("/api/departments"),t=await e.json();e.ok&&(f(t.departments||[]),!r.departmentId&&(t.departments||[]).length>0&&u(t.departments[0].id))}catch(e){}})()},[l]),(0,a.useEffect)(()=>{("ADMIN"===l||"MANAGER"===l)&&(async()=>{try{let e=new URLSearchParams;e.set("role","AGENT"),o&&e.set("departmentId",o),r.projectId&&e.set("projectId",r.projectId);let t=await fetch("/api/users?".concat(e.toString())),a=await t.json();if(t.ok){let e=(a.users||[]).map(e=>({id:e.id,fullName:e.fullName,email:e.email}));x(e),m&&!e.some(e=>e.id===m)&&p("")}}catch(e){}})()},[o,r.projectId,l]);let v=async e=>{e.preventDefault(),b(!0),S("");try{let e={status:d,priority:i};("ADMIN"===l||"MANAGER"===l)&&(e.departmentId=o,e.assignedToId=m||null);let t=await fetch("/api/tickets/".concat(s),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json().catch(()=>({}));S(e.error||"Failed to update ticket");return}window.location.reload()}catch(e){S("Unexpected error")}finally{b(!1)}};return(0,t.jsxs)("form",{onSubmit:v,className:"space-y-3",children:[g&&(0,t.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:g}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(()=>{let e="AGENT"===l?["IN_PROGRESS","WAITING_FOR_CUSTOMER","RESOLVED"]:["OPEN","IN_PROGRESS","WAITING_FOR_CUSTOMER","WAITING_FOR_AGENT","RESOLVED","CLOSED","CANCELLED"];return(0,t.jsx)("select",{value:d,onChange:e=>n(e.target.value),className:"mt-1 w-full border rounded p-2",children:e.map(e=>(0,t.jsx)("option",{value:e,children:e},e))})})()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Priority"}),(()=>{let e="AGENT"===l?["LOW","MEDIUM","HIGH"]:["LOW","MEDIUM","HIGH","URGENT"];return(0,t.jsx)("select",{value:i,onChange:e=>c(e.target.value),className:"mt-1 w-full border rounded p-2",children:e.map(e=>(0,t.jsx)("option",{value:e,children:e},e))})})()]}),("ADMIN"===l||"MANAGER"===l)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Department"}),(0,t.jsx)("select",{value:o,onChange:e=>u(e.target.value),className:"mt-1 w-full border rounded p-2",children:N.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Assign To"}),(0,t.jsxs)("select",{value:m,onChange:e=>p(e.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,t.jsx)("option",{value:"",children:"Unassigned"}),h.map(e=>(0,t.jsx)("option",{value:e.id,children:e.fullName||e.email},e.id))]})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:j,className:"bg-gray-800 hover:bg-black text-white px-4 py-2 rounded",children:j?"Saving...":"Save"})})]})}}]);