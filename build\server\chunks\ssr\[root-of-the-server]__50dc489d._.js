module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40695,a=>{"use strict";a.s(["Button",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,variant:c="default",size:e="default",...f},g)=>(0,b.jsx)("button",{className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[c],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[e],a),ref:g,...f}));e.displayName="Button"},5522,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},50944,(a,b,c)=>{b.exports=a.r(74137)},3130,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},29867,a=>{"use strict";a.s(["default",()=>i]);var b=a.i(87924),c=a.i(72131),d=a.i(50944),e=a.i(38246),f=a.i(40695),g=a.i(5522),h=a.i(3130);function i(){let[a,i]=(0,c.useState)(""),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(!1),[n,o]=(0,c.useState)(""),p=(0,d.useRouter)(),q=async b=>{b.preventDefault(),m(!0),o("");try{let b=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:j})}),c=await b.json();if(!b.ok)return void o(c.error||"Login failed");p.push("/dashboard"),p.refresh()}catch(a){o("An unexpected error occurred")}finally{m(!1)}};return(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,b.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Access your ticketing portal"}),(0,b.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md",children:[(0,b.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,b.jsx)("strong",{children:"Demo Mode:"})," Use <EMAIL> / demo123 to login"]}),(0,b.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"To enable full functionality, set up your Supabase project and update the environment variables"})]})]}),(0,b.jsxs)(h.Card,{children:[(0,b.jsxs)(h.CardHeader,{children:[(0,b.jsx)(h.CardTitle,{children:"Welcome back"}),(0,b.jsx)(h.CardDescription,{children:"Enter your credentials to access your account"})]}),(0,b.jsx)(h.CardContent,{children:(0,b.jsxs)("form",{onSubmit:q,className:"space-y-4",children:[n&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:n}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,b.jsx)(g.Input,{id:"email",type:"email",value:a,onChange:a=>i(a.target.value),required:!0,className:"mt-1",placeholder:"Enter your email"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,b.jsx)(g.Input,{id:"password",type:"password",value:j,onChange:a=>k(a.target.value),required:!0,className:"mt-1",placeholder:"Enter your password"})]}),(0,b.jsx)(f.Button,{type:"submit",disabled:l,className:"w-full",children:l?"Signing in...":"Sign in"}),(0,b.jsxs)("div",{className:"text-center space-y-2",children:[(0,b.jsx)(e.default,{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot your password?"}),(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,b.jsx)(e.default,{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500",children:"Sign up"})]})]})]})})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__50dc489d._.js.map