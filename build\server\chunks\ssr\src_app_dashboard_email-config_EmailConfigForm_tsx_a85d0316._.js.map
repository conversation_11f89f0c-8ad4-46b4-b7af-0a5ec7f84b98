{"version": 3, "sources": ["turbopack:///[project]/src/app/dashboard/email-config/EmailConfigForm.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useMemo, useState } from 'react'\n\ntype EmailConfig = {\n  webhookSecret?: string\n  routingMap?: Record<string, string>\n  defaultProjectId?: string\n  defaultDepartmentId?: string\n  rateLimitPerHour?: number\n  smtp?: {\n    host?: string\n    port?: number\n    user?: string\n    pass?: string\n    fromEmail?: string\n    fromName?: string\n    secure?: boolean\n  }\n}\n\ntype Project = { id: string; name: string }\ntype Department = { id: string; name: string }\n\nexport default function EmailConfigForm({ initial }: { initial: EmailConfig }) {\n  const [config, setConfig] = useState<EmailConfig>(initial || {})\n  const [projects, setProjects] = useState<Project[]>([])\n  const [departments, setDepartments] = useState<Department[]>([])\n  const [saving, setSaving] = useState(false)\n  const [message, setMessage] = useState<string | null>(null)\n  // Address book rows derived from routingMap\n  const [rows, setRows] = useState<Array<{ toEmail: string; departmentId: string }>>([])\n  const [rowMsgs, setRowMsgs] = useState<Record<number, string>>({})\n\n  useEffect(() => {\n    async function loadRefs() {\n      try {\n        const [prj, dept] = await Promise.all([\n          fetch('/api/projects').then(r => r.json()).catch(() => ({ projects: [] })),\n          fetch('/api/departments').then(r => r.json()).catch(() => ({ departments: [] })),\n        ])\n        setProjects(prj.projects || [])\n        setDepartments(dept.departments || [])\n      } catch {}\n    }\n    loadRefs()\n  }, [])\n\n  useEffect(() => {\n    const m = config.routingMap || {}\n    const next = Object.entries(m).map(([to, dep]) => ({ toEmail: to, departmentId: String(dep || '') }))\n    if (next.length === 0) next.push({ toEmail: '', departmentId: '' })\n    setRows(next)\n  }, [config.routingMap])\n\n  function update<K extends keyof EmailConfig>(key: K, value: EmailConfig[K]) {\n    setConfig(prev => ({ ...prev, [key]: value }))\n  }\n\n  async function onSubmit(e: React.FormEvent) {\n    e.preventDefault()\n    setSaving(true)\n    setMessage(null)\n    try {\n      const routingMap: Record<string, string> = {}\n      for (const r of rows) {\n        const to = (r.toEmail || '').trim().toLowerCase()\n        if (to && r.departmentId) routingMap[to] = r.departmentId\n      }\n      const payload = { ...config, routingMap }\n      const res = await fetch('/api/email/config', {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(payload),\n      })\n      if (!res.ok) throw new Error('Save failed')\n      setMessage('Saved successfully')\n    } catch (err: any) {\n      setMessage(err?.message || 'Failed to save')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  return (\n    <form onSubmit={onSubmit} className=\"space-y-6\">\n      <div className=\"bg-white rounded-lg shadow p-4 space-y-4\">\n        <h2 className=\"font-semibold\">Webhook</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm text-gray-600\">Webhook secret</label>\n            <input type=\"text\" value={config.webhookSecret || ''} onChange={e => update('webhookSecret', e.target.value)} className=\"mt-1 w-full border rounded px-3 py-2\" placeholder=\"e.g., random-long-secret\" />\n            <p className=\"text-xs text-gray-500 mt-1\">Header: x-email-webhook-secret must match.</p>\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">Rate limit (per hour per sender)</label>\n            <input type=\"number\" value={config.rateLimitPerHour ?? 20} onChange={e => update('rateLimitPerHour', Number(e.target.value))} className=\"mt-1 w-full border rounded px-3 py-2\" />\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-4 space-y-4\">\n        <h2 className=\"font-semibold\">Routing</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm text-gray-600\">Default Project</label>\n            <select value={config.defaultProjectId || ''} onChange={e => update('defaultProjectId', e.target.value)} className=\"mt-1 w-full border rounded px-3 py-2\">\n              <option value=\"\">— None —</option>\n              {projects.map(p => <option key={p.id} value={p.id}>{p.name}</option>)}\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">Default Department</label>\n            <select value={config.defaultDepartmentId || ''} onChange={e => update('defaultDepartmentId', e.target.value)} className=\"mt-1 w-full border rounded px-3 py-2\">\n              <option value=\"\">— None —</option>\n              {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}\n            </select>\n          </div>\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm text-gray-600 mb-2\">Address book (Recipient → Department)</label>\n            <div className=\"space-y-3\">\n              {rows.map((row, idx) => (\n                <div key={idx} className=\"grid grid-cols-1 md:grid-cols-12 gap-2 items-center\">\n                  <div className=\"md:col-span-5\">\n                    <input\n                      placeholder=\"<EMAIL>\"\n                      className=\"w-full border rounded px-3 py-2\"\n                      value={row.toEmail}\n                      onChange={e => setRows(prev => prev.map((r,i) => i===idx ? { ...r, toEmail: e.target.value } : r))}\n                    />\n                  </div>\n                  <div className=\"md:col-span-4\">\n                    <select\n                      className=\"w-full border rounded px-3 py-2\"\n                      value={row.departmentId}\n                      onChange={e => setRows(prev => prev.map((r,i) => i===idx ? { ...r, departmentId: e.target.value } : r))}\n                    >\n                      <option value=\"\">— Select department —</option>\n                      {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}\n                    </select>\n                  </div>\n                  <div className=\"md:col-span-4 flex flex-wrap gap-2\">\n                    <button type=\"button\" className=\"border rounded px-3 py-2 text-sm\" onClick={async () => {\n                      const to = (row.toEmail||'').trim()\n                      if (!to) { setRowMsgs(m => ({...m, [idx]: 'Enter a recipient email first'})); return }\n                      setRowMsgs(m => ({...m, [idx]: 'Testing routing...'}))\n                      try {\n                        const res = await fetch('/api/email/config/test', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ to }) })\n                        const data = await res.json()\n                        if (!res.ok) throw new Error(data?.error || 'Failed')\n                        const d = data?.resolved\n                        setRowMsgs(m => ({...m, [idx]: `Department: ${d?.departmentName || d?.departmentId || 'n/a'} • Project: ${d?.projectName || d?.projectId || 'n/a'}`}))\n                      } catch (e:any) {\n                        setRowMsgs(m => ({...m, [idx]: e?.message || 'Test failed'}))\n                      }\n                    }}>Test routing</button>\n                    <button type=\"button\" className=\"border rounded px-3 py-2 text-sm\" onClick={async () => {\n                      const to = (row.toEmail||'').trim()\n                      if (!to) { setRowMsgs(m => ({...m, [idx]: 'Enter a recipient email first'})); return }\n                      setRowMsgs(m => ({...m, [idx]: 'Sending test email...'}))\n                      try {\n                        const res = await fetch('/api/email/config/test/send-ack', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ to }) })\n                        const data = await res.json()\n                        if (!res.ok) throw new Error(data?.error || 'Failed')\n                        setRowMsgs(m => ({...m, [idx]: `Test email sent ✓ (messageId: ${data?.messageId || 'n/a'})`}))\n                      } catch (e:any) {\n                        setRowMsgs(m => ({...m, [idx]: e?.message || 'Send failed'}))\n                      }\n                    }}>Send test email</button>\n                    <button type=\"button\" className=\"border rounded px-3 py-2 text-sm\" onClick={() => setRows(prev => prev.filter((_,i) => i!==idx))}>Remove</button>\n                  </div>\n                  {rowMsgs[idx] && <div className=\"md:col-span-12 text-xs text-gray-600\">{rowMsgs[idx]}</div>}\n                </div>\n              ))}\n              <button type=\"button\" className=\"inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-9 px-3 py-1\" onClick={() => setRows(prev => [...prev, { toEmail: '', departmentId: '' }])}>+ Add address</button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white rounded-lg shadow p-4 space-y-4\">\n        <h2 className=\"font-semibold\">Acknowledgement Email (SMTP)</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm text-gray-600\">SMTP Host</label>\n            <input className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.host || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), host: e.target.value } }))} />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">SMTP Port</label>\n            <input type=\"number\" className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.port ?? 587} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), port: Number(e.target.value) } }))} />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">User</label>\n            <input className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.user || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), user: e.target.value } }))} />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">Password</label>\n            <input type=\"password\" className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.pass || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), pass: e.target.value } }))} />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">From Email</label>\n            <input className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.fromEmail || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), fromEmail: e.target.value } }))} />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600\">From Name</label>\n            <input className=\"mt-1 w-full border rounded px-3 py-2\" value={config.smtp?.fromName || ''} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), fromName: e.target.value } }))} />\n          </div>\n          <div>\n            <label className=\"inline-flex items-center text-sm text-gray-600 mt-6\">\n              <input type=\"checkbox\" className=\"mr-2\" checked={!!config.smtp?.secure} onChange={e => setConfig(prev => ({ ...prev, smtp: { ...(prev.smtp||{}), secure: e.target.checked } }))} />\n              Use TLS (secure)\n            </label>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center gap-3\">\n        <button type=\"submit\" disabled={saving} className=\"inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2\">\n          {saving ? 'Saving…' : 'Save configuration'}\n        </button>\n        {message && <span className=\"text-sm text-gray-600\">{message}</span>}\n      </div>\n    </form>\n  )\n}\n\n"], "names": [], "mappings": "+EAEA,EAAA,EAAA,CAAA,CAAA,OAsBe,SAAS,EAAgB,SAAE,CAAO,CAA4B,EAC3E,GAAM,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAc,GAAW,CAAC,GACxD,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAY,EAAE,EAChD,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EACzD,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GAC/B,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAwB,MAEhD,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAmD,EAAE,EAC/E,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAyB,CAAC,GAuBhE,SAAS,EAAoC,CAAM,CAAE,CAAqB,EACxE,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,CAAC,EAAI,CAAE,EAAM,CAAC,CAC9C,CAEA,eAAe,EAAS,CAAkB,EACxC,EAAE,cAAc,GAChB,EAAU,IACV,EAAW,MACX,GAAI,CACF,IAAM,EAAqC,CAAC,EAC5C,IAAK,IAAM,KAAK,EAAM,CACpB,IAAM,EAAK,CAAC,EAAE,OAAO,EAAI,EAAA,CAAE,CAAE,IAAI,GAAG,WAAW,GAC3C,GAAM,EAAE,YAAY,GAAE,CAAU,CAAC,EAAG,CAAG,EAAE,YAAA,AAAY,CAC3D,CACA,IAAM,EAAU,CAAE,GAAG,CAAM,YAAE,CAAW,EAMxC,GAAI,CAAC,CALO,MAAM,MAAM,oBAAqB,CAC3C,OAAQ,QACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,EACvB,EAAA,EACS,EAAE,CAAE,MAAM,AAAI,MAAM,eAC7B,EAAW,qBACb,CAAE,MAAO,EAAU,CACjB,EAAW,GAAK,SAAW,iBAC7B,QAAU,CACR,GAAU,EACZ,CACF,CAEA,MAlDA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,MAWR,AAVA,eAAe,EACb,GAAI,CACF,GAAM,CAAC,EAAK,EAAK,CAAG,MAAM,QAAQ,GAAG,CAAC,CACpC,MAAM,iBAAiB,IAAI,CAAC,GAAK,EAAE,IAAI,IAAI,KAAK,CAAC,IAAM,CAAC,CAAE,SAAU,EAAE,CAAC,CAAC,EACxE,MAAM,oBAAoB,IAAI,CAAC,GAAK,EAAE,IAAI,IAAI,KAAK,CAAC,IAAM,CAAC,CAAE,YAAa,EAAE,CAAC,CAAC,EAC/E,EACD,EAAY,EAAI,QAAQ,EAAI,EAAE,EAC9B,EAAe,EAAK,WAAW,EAAI,EAAE,CACvC,CAAE,KAAM,CAAC,CACX,GAEF,EAAG,EAAE,EAEL,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAER,IAAM,EAAO,OAAO,OAAO,CAAC,AADlB,EAAO,UAAU,EAAI,CAAC,GACD,GAAG,CAAC,CAAC,CAAC,EAAI,EAAI,GAAK,AAAC,EAAE,QAAS,EAAI,aAAc,OAAO,GAAO,IAAI,CAAC,EAC/E,IAAhB,EAAK,MAAM,EAAQ,EAAK,IAAI,CAAC,CAAE,QAAS,GAAI,aAAc,EAAG,GACjE,EAAQ,EACV,EAAG,CAAC,EAAO,UAAU,CAAC,EAgCpB,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAU,UAAU,sBAClC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,YAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,mBAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,OAAO,MAAO,EAAO,aAAa,EAAI,GAAI,SAAU,GAAK,EAAO,gBAAiB,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,uCAAuC,YAAY,6BAC3K,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,kDAE5C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,qCAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,SAAS,MAAO,EAAO,gBAAgB,EAAI,GAAI,SAAU,GAAK,EAAO,mBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK,GAAI,UAAU,kDAK9I,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,YAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,oBAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,MAAO,EAAO,gBAAgB,EAAI,GAAI,SAAU,GAAK,EAAO,mBAAoB,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,iDACjH,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,aAChB,EAAS,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,SAGxC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,uBAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,MAAO,EAAO,mBAAmB,EAAI,GAAI,SAAU,GAAK,EAAO,sBAAuB,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,iDACvH,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,aAChB,EAAY,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,SAG3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,4CAAmC,0CACpD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACZ,EAAK,GAAG,CAAC,CAAC,EAAK,IACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAc,UAAU,gEACvB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,YAAY,uBACZ,UAAU,kCACV,MAAO,EAAI,OAAO,CAClB,SAAU,GAAK,EAAQ,GAAQ,EAAK,GAAG,CAAC,CAAC,EAAE,IAAM,IAAI,EAAM,CAAE,GAAG,CAAC,CAAE,QAAS,EAAE,MAAM,CAAC,KAAM,AAAD,EAAK,QAGnG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CACC,UAAU,kCACV,MAAO,EAAI,YAAY,CACvB,SAAU,GAAK,EAAQ,GAAQ,EAAK,GAAG,CAAC,CAAC,EAAE,IAAM,IAAI,EAAM,CAAE,GAAG,CAAC,CAAE,aAAc,EAAE,MAAM,CAAC,KAAK,AAAC,EAAI,cAEpG,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,0BAChB,EAAY,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,QAG3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,UAAU,mCAAmC,QAAS,UAC1E,IAAM,EAAK,CAAC,EAAI,OAAO,EAAE,EAAA,CAAE,CAAE,IAAI,GACjC,GAAI,CAAC,EAAI,YAAE,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,gCAA+B,CAAC,EAC1E,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,qBAAoB,CAAC,EACpD,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,yBAA0B,CAAE,OAAQ,OAAQ,QAAS,CAAE,eAAgB,kBAAmB,EAAG,KAAM,KAAK,SAAS,CAAC,IAAE,CAAG,EAAG,GAC5I,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,GAAM,OAAS,UAC5C,IAAM,EAAI,GAAM,SAChB,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,CAAC,YAAY,EAAE,GAAG,gBAAkB,GAAG,cAAgB,MAAM,YAAY,EAAE,GAAG,aAAe,GAAG,WAAa,MAAA,CAAO,CAAC,EACtJ,CAAE,MAAO,EAAO,CACd,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,GAAG,SAAW,cAAa,CAAC,CAC7D,CACF,WAAG,iBACH,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,UAAU,mCAAmC,QAAS,UAC1E,IAAM,EAAK,CAAC,EAAI,OAAO,EAAE,EAAA,CAAE,CAAE,IAAI,GACjC,GAAI,CAAC,EAAI,YAAE,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,+BAA+B,CAAC,GAC1E,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,wBAAuB,CAAC,EACvD,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,kCAAmC,CAAE,OAAQ,OAAQ,QAAS,CAAE,eAAgB,kBAAmB,EAAG,KAAM,KAAK,SAAS,CAAC,IAAE,CAAG,EAAG,GACrJ,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,GAAM,OAAS,UAC5C,EAAW,IAAK,AAAC,CAAC,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,CAAC,8BAA8B,EAAE,GAAM,WAAa,MAAM,CAAC,CAAC,CAAA,CAAC,CAC9F,CAAE,MAAO,EAAO,CACd,EAAW,GAAM,CAAD,CAAE,GAAG,CAAC,CAAE,CAAC,EAAI,CAAE,GAAG,SAAW,cAAa,CAAC,CAC7D,CACF,WAAG,oBACH,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,UAAU,mCAAmC,QAAS,IAAM,EAAQ,GAAQ,EAAK,MAAM,CAAC,CAAC,EAAE,IAAM,IAAI,aAAO,cAEnI,CAAO,CAAC,EAAI,EAAI,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gDAAwC,CAAO,CAAC,EAAI,KAjD5E,IAoDZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,UAAU,sHAAsH,QAAS,IAAM,EAAQ,GAAQ,IAAI,EAAM,CAAE,QAAS,GAAI,aAAc,EAAG,EAAE,WAAG,8BAM5O,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qDACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,iCAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,cAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,MAAQ,GAAI,SAAU,GAAK,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,KAAM,EAAE,MAAM,CAAC,KAAK,AAAC,CAAE,CAAC,QAE3L,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,cAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,SAAS,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,MAAQ,IAAK,SAAU,GAAK,EAAU,GAAS,EAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,KAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAE,EAAE,CAAC,OAElN,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,SAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,MAAQ,GAAI,SAAU,GAAK,EAAU,GAAS,EAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,KAAM,EAAE,MAAM,CAAC,KAAK,AAAC,EAAE,CAAC,OAE3L,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,aAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,MAAQ,GAAI,SAAU,GAAK,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,KAAM,EAAE,MAAM,CAAC,KAAK,AAAC,EAAE,CAAC,OAE3M,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,eAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,WAAa,GAAI,SAAU,GAAK,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,UAAW,EAAE,MAAM,CAAC,KAAK,AAAC,EAAE,CAAC,OAErM,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,cAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAAuC,MAAO,EAAO,IAAI,EAAE,UAAY,GAAI,SAAU,GAAK,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,SAAU,EAAE,MAAM,CAAC,KAAK,AAAC,EAAE,CAAC,OAEnM,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,gEACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,WAAW,UAAU,OAAO,QAAS,CAAC,CAAC,EAAO,IAAI,EAAE,OAAQ,SAAU,GAAK,EAAU,IAAS,CAAE,EAAH,CAAM,CAAI,CAAE,KAAM,CAAE,GAAI,EAAK,IAAI,EAAE,CAAC,CAAC,CAAG,OAAQ,EAAE,MAAM,CAAC,OAAO,AAAC,EAAE,CAAC,IAAM,8BAO3L,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,oCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,SAAU,EAAQ,UAAU,mHAC/C,EAAS,UAAY,uBAEvB,GAAW,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAyB,SAI7D"}