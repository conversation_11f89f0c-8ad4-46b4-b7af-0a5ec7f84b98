{"version": 3, "sources": ["turbopack:///[project]/src/app/dashboard/tickets/[id]/MessageComposer.tsx", "turbopack:///[project]/src/app/dashboard/tickets/[id]/TicketControls.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\n\nexport default function MessageComposer({ ticketId }: { ticketId: string }) {\n  const [content, setContent] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const submit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n    try {\n      const res = await fetch(`/api/tickets/${ticketId}/messages`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ content }),\n      })\n      if (!res.ok) {\n        const data = await res.json().catch(() => ({}))\n        setError(data.error || 'Failed to send message')\n        return\n      }\n      setContent('')\n      // Soft refresh messages by reloading the page (simple approach)\n      window.location.reload()\n    } catch (e) {\n      setError('Unexpected error')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={submit} className=\"space-y-3\">\n      {error && <div className=\"bg-red-50 text-red-700 border border-red-200 p-2 rounded\">{error}</div>}\n      <textarea\n        value={content}\n        onChange={(e) => setContent(e.target.value)}\n        placeholder=\"Write a message...\"\n        className=\"w-full border rounded p-2 h-28\"\n        required\n      />\n      <div className=\"flex justify-end\">\n        <button type=\"submit\" disabled={loading} className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded\">\n          {loading ? 'Sending...' : 'Send'}\n        </button>\n      </div>\n    </form>\n  )\n}\n\n", "\"use client\"\n\nimport { useEffect, useState } from 'react'\n\ntype Department = { id: string; name: string }\ntype Agent = { id: string; fullName: string | null; email: string }\n\nexport default function TicketControls({ ticketId, initial, role }: { ticketId: string, initial: { status: string, priority: string, departmentId?: string, assignedToId?: string, projectId?: string }, role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER' }) {\n  const [status, setStatus] = useState(initial.status)\n  const [priority, setPriority] = useState(initial.priority)\n  const [departmentId, setDepartmentId] = useState(initial.departmentId || '')\n  const [assignedToId, setAssignedToId] = useState(initial.assignedToId || '')\n  const [agents, setAgents] = useState<Agent[]>([])\n  const [departments, setDepartments] = useState<Department[]>([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    // Load departments (only needed for admin/manager)\n    if (role === 'ADMIN' || role === 'MANAGER') {\n      (async () => {\n        try {\n          const res = await fetch('/api/departments')\n          const data = await res.json()\n          if (res.ok) {\n            setDepartments(data.departments || [])\n            if (!initial.departmentId && (data.departments || []).length > 0) {\n              setDepartmentId(data.departments[0].id)\n            }\n          }\n        } catch {}\n      })()\n    }\n  }, [role])\n\n  useEffect(() => {\n    // Load agents filtered by department + project (only for admin/manager)\n    if (role === 'ADMIN' || role === 'MANAGER') {\n      (async () => {\n        try {\n          const params = new URLSearchParams()\n          params.set('role', 'AGENT')\n          if (departmentId) params.set('departmentId', departmentId)\n          if (initial.projectId) params.set('projectId', initial.projectId)\n          const res = await fetch(`/api/users?${params.toString()}`)\n          const data = await res.json()\n          if (res.ok) {\n            const list: Agent[] = (data.users || []).map((u: any) => ({ id: u.id, fullName: u.fullName, email: u.email }))\n            setAgents(list)\n            // If current assignee is not in list, clear\n            if (assignedToId && !list.some(a => a.id === assignedToId)) {\n              setAssignedToId('')\n            }\n          }\n        } catch {}\n      })()\n    }\n  }, [departmentId, initial.projectId, role])\n\n  const save = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n    try {\n      const payload: any = { status, priority }\n      if (role === 'ADMIN' || role === 'MANAGER') {\n        payload.departmentId = departmentId\n        payload.assignedToId = assignedToId || null\n      }\n      const res = await fetch(`/api/tickets/${ticketId}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(payload),\n      })\n      if (!res.ok) {\n        const data = await res.json().catch(() => ({}))\n        setError(data.error || 'Failed to update ticket')\n        return\n      }\n      window.location.reload()\n    } catch (e) {\n      setError('Unexpected error')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <form onSubmit={save} className=\"space-y-3\">\n      {error && <div className=\"bg-red-50 text-red-700 border border-red-200 p-2 rounded\">{error}</div>}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Status</label>\n        {(() => {\n          const all = ['OPEN','IN_PROGRESS','WAITING_FOR_CUSTOMER','WAITING_FOR_AGENT','RESOLVED','CLOSED','CANCELLED']\n          const allowed = role === 'AGENT' ? ['IN_PROGRESS','WAITING_FOR_CUSTOMER','RESOLVED'] : all\n          return (\n            <select value={status} onChange={e=>setStatus(e.target.value)} className=\"mt-1 w-full border rounded p-2\">\n              {allowed.map(s => <option key={s} value={s}>{s}</option>)}\n            </select>\n          )\n        })()}\n      </div>\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700\">Priority</label>\n        {(() => {\n          const all = ['LOW','MEDIUM','HIGH','URGENT']\n          const allowed = role === 'AGENT' ? ['LOW','MEDIUM','HIGH'] : all\n          return (\n            <select value={priority} onChange={e=>setPriority(e.target.value)} className=\"mt-1 w-full border rounded p-2\">\n              {allowed.map(p => <option key={p} value={p}>{p}</option>)}\n            </select>\n          )\n        })()}\n      </div>\n      {(role === 'ADMIN' || role === 'MANAGER') && (\n        <>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700\">Department</label>\n            <select value={departmentId} onChange={e=>setDepartmentId(e.target.value)} className=\"mt-1 w-full border rounded p-2\">\n              {departments.map(d => <option key={d.id} value={d.id}>{d.name}</option>)}\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700\">Assign To</label>\n            <select value={assignedToId} onChange={e=>setAssignedToId(e.target.value)} className=\"mt-1 w-full border rounded p-2\">\n              <option value=\"\">Unassigned</option>\n              {agents.map(a => <option key={a.id} value={a.id}>{a.fullName || a.email}</option>)}\n            </select>\n          </div>\n        </>\n      )}\n      <div className=\"flex justify-end\">\n        <button type=\"submit\" disabled={loading} className=\"bg-gray-800 hover:bg-black text-white px-4 py-2 rounded\">\n          {loading ? 'Saving...' : 'Save'}\n        </button>\n      </div>\n    </form>\n  )\n}\n"], "names": [], "mappings": "+EAEA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,EAAgB,UAAE,CAAQ,CAAwB,EACxE,GAAM,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAE7B,EAAS,MAAO,IACpB,EAAE,cAAc,GAChB,GAAW,GACX,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,CAAC,aAAa,EAAE,EAAS,SAAS,CAAC,CAAE,CAC3D,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,SAAE,CAAQ,EACjC,GACA,GAAI,CAAC,EAAI,EAAE,CAAE,CACX,IAAM,EAAO,MAAM,EAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,EAAC,CAAC,EAC7C,EAAS,EAAK,KAAK,EAAI,0BACvB,MACF,CACA,EAAW,IAEX,OAAO,QAAQ,CAAC,MAAM,EACxB,CAAE,MAAO,EAAG,CACV,EAAS,mBACX,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAQ,UAAU,sBAC/B,GAAS,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEAA4D,IACrF,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,MAAO,EACP,SAAU,AAAC,GAAM,EAAW,EAAE,MAAM,CAAC,KAAK,EAC1C,YAAY,qBACZ,UAAU,iCACV,QAAQ,CAAA,CAAA,IAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,SAAU,EAAS,UAAU,sEAChD,EAAU,aAAe,aAKpC,kECjDA,EAAA,EAAA,CAAA,CAAA,OAKe,SAAS,EAAe,CAAE,UAAQ,SAAE,CAAO,MAAE,CAAI,CAA2L,EACzP,GAAM,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAQ,MAAM,EAC7C,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,EAAQ,QAAQ,EACnD,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAS,EAAQ,YAAY,EAAI,IACnE,CAAC,EAAc,EAAgB,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAQ,YAAY,EAAI,IACnE,CAAC,EAAQ,EAAU,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAU,EAAE,EAC1C,CAAC,EAAa,EAAe,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EACzD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,GAAC,GACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAEnC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,MAEK,UAAT,GAA6B,YAAT,CAAS,GAAW,AAC1C,CAAC,UACC,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,oBAClB,EAAO,MAAM,EAAI,IAAI,GACvB,EAAI,EAAE,EAAE,CACV,EAAe,EAAK,WAAW,EAAI,EAAE,EACjC,CAAC,EAAQ,YAAY,EAAI,CAAC,EAAK,WAAW,EAAI,EAAA,AAAE,EAAE,MAAM,CAAG,GAAG,AAChE,EAAgB,EAAK,WAAW,CAAC,EAAE,CAAC,EAAE,EAG5C,CAAE,KAAM,CAAC,EACX,CAAC,EAEL,EAAG,CAAC,EAAK,EAET,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,MAEK,UAAT,GAA6B,YAAT,CAAS,GAAW,AAC1C,CAAC,UACC,GAAI,CACF,IAAM,EAAS,IAAI,gBACnB,EAAO,GAAG,CAAC,OAAQ,SACf,GAAc,EAAO,GAAG,CAAC,eAAgB,GACzC,EAAQ,SAAS,EAAE,EAAO,GAAG,CAAC,YAAa,EAAQ,SAAS,EAChE,IAAM,EAAM,MAAM,MAAM,CAAC,WAAW,EAAE,EAAO,QAAQ,GAAA,CAAI,EACnD,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,EAAI,EAAE,CAAE,CACV,IAAM,EAAgB,CAAC,EAAK,KAAK,EAAI,EAAA,AAAE,EAAE,GAAG,CAAC,AAAC,GAAY,CAAD,CAAG,GAAI,EAAE,EAAE,CAAE,SAAU,EAAE,QAAQ,CAAE,MAAO,EAAE,KAAK,CAAC,CAAC,EAC5G,EAAU,GAEN,GAAgB,CAAC,EAAK,IAAI,CAAC,GAAK,EAAE,EAAE,GAAK,IAC3C,EAAgB,GAEpB,CACF,CAAE,IAJ8D,CAIxD,CAAC,EACX,CAAC,EAEL,EAAG,CAAC,EAAc,EAAQ,SAAS,CAAE,EAAK,EAE1C,IAAM,EAAO,MAAO,IAClB,EAAE,cAAc,GAChB,GAAW,GACX,EAAS,IACT,GAAI,CACF,IAAM,EAAe,QAAE,WAAQ,CAAS,GAC3B,UAAT,GAA6B,YAAT,CAAS,GAAW,CAC1C,EAAQ,YAAY,CAAG,EACvB,EAAQ,YAAY,CAAG,GAAgB,MAEzC,IAAM,EAAM,MAAM,MAAM,CAAC,aAAa,EAAE,EAAA,CAAU,CAAE,CAClD,OAAQ,QACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,EACvB,GACA,GAAI,CAAC,EAAI,EAAE,CAAE,CACX,IAAM,EAAO,MAAM,EAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,EAAC,CAAC,EAC7C,EAAS,EAAK,KAAK,EAAI,2BACvB,MACF,CACA,OAAO,QAAQ,CAAC,MAAM,EACxB,CAAE,MAAO,EAAG,CACV,EAAS,mBACX,QAAU,CACR,GAAW,EACb,CACF,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SAAU,EAAM,UAAU,sBAC7B,GAAS,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEAA4D,IACrF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,WAC1D,CAAC,KAEA,IAAM,EAAmB,UAAT,EAAmB,CAAC,cAAc,uBAAuB,WAAW,CADxE,CAAC,CAC0E,MADnE,cAAc,uBAAuB,oBAAoB,WAAW,SAAS,YAAY,CAE7G,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAO,EAAQ,SAAU,GAAG,EAAU,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,0CACtE,EAAQ,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAe,MAAO,WAAI,GAAd,MAGrC,CAAC,MAEH,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,aAC1D,CAAC,KAEA,IAAM,EAAmB,UAAT,EAAmB,CAAC,MAAM,SAAS,OAAO,CAD9C,CAAC,CACgD,KAD1C,SAAS,OAAO,SAAS,CAE5C,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAO,EAAU,SAAU,GAAG,EAAY,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,0CAC1E,EAAQ,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAe,MAAO,WAAI,GAAd,MAGrC,CAAC,MAEF,CAAU,UAAT,GAA6B,YAAT,CAAS,CAAS,EACtC,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,QAAA,CAAA,WACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,eAC3D,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAO,EAAc,SAAU,GAAG,EAAgB,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,0CAClF,EAAY,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,QAG3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,mDAA0C,cAC3D,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,MAAO,EAAc,SAAU,GAAG,EAAgB,EAAE,MAAM,CAAC,KAAK,EAAG,UAAU,2CACnF,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,eAChB,EAAO,GAAG,CAAC,GAAK,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,QAAQ,EAAI,EAAE,KAAK,EAAzC,EAAE,EAAE,YAK1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,SAAU,EAAS,UAAU,mEAChD,EAAU,YAAc,aAKnC"}