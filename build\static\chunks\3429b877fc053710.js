(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,67881,e=>{"use strict";e.s(["Button",()=>r]);var t=e.i(43476),a=e.i(71645),i=e.i(47163);let r=a.forwardRef((e,a)=>{let{className:r,variant:n="default",size:s="default",...l}=e;return(0,t.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[n],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[s],r),ref:a,...l})});r.displayName="Button"},23750,e=>{"use strict";e.s(["Input",()=>r]);var t=e.i(43476),a=e.i(71645),i=e.i(47163);let r=a.forwardRef((e,a)=>{let{className:r,type:n,...s}=e;return(0,t.jsx)("input",{type:n,className:(0,i.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:a,...s})});r.displayName="Input"},69959,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(43476),a=e.i(71645),i=e.i(67881),r=e.i(23750);function n(){let[e,n]=(0,a.useState)([]),[l,o]=(0,a.useState)(!0),[d,c]=(0,a.useState)(""),[u,p]=(0,a.useState)(null),[m,h]=(0,a.useState)(""),[f,g]=(0,a.useState)(""),b=async()=>{o(!0),c("");try{let e=await fetch("/api/departments"),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to load departments");n(t.departments||[])}catch(e){c((null==e?void 0:e.message)||"Failed to load")}finally{o(!1)}};(0,a.useEffect)(()=>{b()},[]);let v=async()=>{if(m.trim()){p("new"),c("");try{let e=await fetch("/api/departments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:m.trim(),description:f.trim()||null})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to create");h(""),g(""),await b()}catch(e){c((null==e?void 0:e.message)||"Failed to create department")}finally{p(null)}}},x=async(e,t,a)=>{p(e),c("");try{let i=await fetch("/api/departments/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t,description:a})}),r=await i.json();if(!i.ok)throw Error(r.error||"Failed to update");await b()}catch(e){c((null==e?void 0:e.message)||"Failed to update department")}finally{p(null)}},y=async e=>{if(confirm("Delete this department? This cannot be undone.")){p(e),c("");try{let t=await fetch("/api/departments/".concat(e),{method:"DELETE"}),a=await t.json().catch(()=>({success:t.ok}));if(!t.ok)throw Error(a.error||"Failed to delete");await b()}catch(e){c((null==e?void 0:e.message)||"Failed to delete department")}finally{p(null)}}};return l?(0,t.jsx)("div",{className:"p-4",children:"Loading departments..."}):(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"font-semibold",children:"Departments"}),(0,t.jsx)(i.Button,{variant:"outline",onClick:b,children:"Refresh"})]}),(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[d&&(0,t.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:d}),(0,t.jsxs)("div",{className:"grid gap-2 sm:grid-cols-[240px_1fr_auto] items-start",children:[(0,t.jsx)(r.Input,{placeholder:"New department name",value:m,onChange:e=>h(e.target.value)}),(0,t.jsx)(r.Input,{placeholder:"Description (optional)",value:f,onChange:e=>g(e.target.value)}),(0,t.jsx)(i.Button,{onClick:v,disabled:"new"===u||!m.trim(),children:"new"===u?"Adding...":"Add"})]}),(0,t.jsxs)("div",{className:"divide-y",children:[e.map(e=>(0,t.jsx)(s,{d:e,onSave:x,onDelete:y,saving:u===e.id},e.id)),0===e.length&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"No departments found."})]})]})]})}function s(e){let{d:n,onSave:s,onDelete:l,saving:o}=e,[d,c]=(0,a.useState)(n.name),[u,p]=(0,a.useState)(n.description||""),m=d!==n.name||(u||"")!==(n.description||"");return(0,t.jsxs)("div",{className:"py-3 grid gap-2 sm:grid-cols-[240px_1fr_auto] items-center",children:[(0,t.jsx)(r.Input,{value:d,onChange:e=>c(e.target.value)}),(0,t.jsx)(r.Input,{value:u,onChange:e=>p(e.target.value)}),(0,t.jsxs)("div",{className:"flex gap-2 justify-end",children:[(0,t.jsx)(i.Button,{size:"sm",variant:"outline",onClick:()=>l(n.id),disabled:o,children:"Delete"}),(0,t.jsx)(i.Button,{size:"sm",onClick:()=>s(n.id,d,u),disabled:o||!m,children:o?"Saving...":"Save"})]})]})}}]);