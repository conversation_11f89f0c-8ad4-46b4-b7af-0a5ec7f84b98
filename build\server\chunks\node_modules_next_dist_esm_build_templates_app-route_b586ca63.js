module.exports=[43222,e=>{"use strict";e.s(["handler",()=>D,"patchFetch",()=>U,"routeModule",()=>P,"serverHooks",()=>j,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>_],43222);var t=e.i(47909),r=e.i(74017),a=e.i(96250),i=e.i(59756),n=e.i(61916),o=e.i(69741),s=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),c=e.i(66012),p=e.i(70101),m=e.i(26937),f=e.i(10372),h=e.i(93695);e.i(52474);var w=e.i(220);e.s(["POST",()=>M,"runtime",()=>v],69096);var g=e.i(89171),R=e.i(15270),E=e.i(24868),y=e.i(14747);let v="nodejs";function I(e){return e?String(e).replace(/<style[\s\S]*?<\/style>/gi,"").replace(/<script[\s\S]*?<\/script>/gi,"").replace(/<[^>]+>/g,"").trim():""}async function A(e){let t=process.env.EMAIL_ORG_ID;if(t)return t;if(e){let t=/@([^>\s]+)$/.exec(e),r=t?.[1]?.toLowerCase();if(r){let e=await R.prisma.organization.findFirst({where:{domain:r}});if(e)return e.id}}let r=await R.prisma.organization.findMany({select:{id:!0},take:2});return 1===r.length?r[0].id:null}async function C(e,t){let r=await R.prisma.user.findUnique({where:{email:t}});if(r)return r;let a=t.split("@")[0],i=Math.random().toString(36).slice(2);return R.prisma.user.create({data:{email:t,password:i,fullName:a,role:"CUSTOMER",organizationId:e}})}async function x(e,t){if(t?.defaultProjectId)return t.defaultProjectId;let r=process.env.EMAIL_DEFAULT_PROJECT_ID;if(r)return r;let a=await R.prisma.project.findFirst({where:{organizationId:e},select:{id:!0},orderBy:{createdAt:"asc"}});return a?.id}async function T(e,t,r,a){try{let r=a?.routingMap;if(r&&t){let a=r[t.toLowerCase()];if(a){let t=await R.prisma.department.findFirst({where:{organizationId:e,name:a},select:{id:!0}});if(t)return t.id;if(/^c[a-z0-9]{20,}$/i.test(a))return a}}}catch{}try{let r=process.env.EMAIL_ROUTING_MAP;if(r&&t){let a=JSON.parse(r),i=t.toLowerCase();if(a[i]){let t=await R.prisma.department.findFirst({where:{organizationId:e,name:a[i]},select:{id:!0}});if(t)return t.id;if(/^c[a-z0-9]{20,}$/i.test(a[i]))return a[i]}}}catch{}if(r){let t=/\[(?:DEPT|DEPARTMENT):\s*([^\]]+)\]/i.exec(r),a=t?.[1]?.trim();if(a){let t=await R.prisma.department.findFirst({where:{organizationId:e,name:a},select:{id:!0}});if(t)return t.id}}if(a?.defaultDepartmentId)return a.defaultDepartmentId;let i=await R.prisma.department.findFirst({where:{organizationId:e,name:"General Support"},select:{id:!0}});if(i)return i.id;let n=await R.prisma.department.findFirst({where:{organizationId:e},select:{id:!0},orderBy:{createdAt:"asc"}});return n?.id}async function N(e,t,r,a){let i=y.default.join(process.cwd(),"public","uploads");await (0,E.mkdir)(i,{recursive:!0});let n=t.replace(/[^a-zA-Z0-9._-]/g,"_"),o=`${Date.now()}-${Math.random().toString(36).slice(2,8)}-${n}`,s=y.default.join(i,o);await (0,E.writeFile)(s,e);let l=`/uploads/${o}`;await R.prisma.attachment.create({data:{filename:o,originalName:t,mimeType:r||"application/octet-stream",size:e.length,url:l,ticketId:a}})}async function S(e){console.log(`[ACK] Would send acknowledgment to ${e.to} for ${e.ticketNumber}`)}async function b(e,t){let r=new Date(Date.now()-36e5);return await R.prisma.emailThread.count({where:{fromEmail:e,receivedAt:{gte:r}}})<(t||20)}async function M(e){try{let t,r,a,i,n=e.headers.get("content-type")||"";if(n.includes("application/json"))t=await e.json();else{if(!n.includes("multipart/form-data"))return g.NextResponse.json({error:"Unsupported content type"},{status:415});let r=await e.formData();t=Object.fromEntries(Array.from(r.entries()))}let o=String(t.from||"").trim(),s=String(t.to||"").trim(),l=I(t.subject),d=I(t.text||t.body),u=I(t.html),c=d||u,p=String(t.messageId||t["Message-Id"]||"").trim(),m=String(t.inReplyTo||t["In-Reply-To"]||"").trim()||void 0,f=Array.isArray(t.references)?t.references.join(", "):t.references||void 0;if(!o||!l||!c)return g.NextResponse.json({error:"Missing required email fields"},{status:400});let h=await A(s);if(!h)return g.NextResponse.json({error:"Organization resolution failed"},{status:400});let w=await R.prisma.organization.findUnique({where:{id:h},select:{settings:!0}}),E=w?.settings?.emailConfig||{},y=e.headers.get("x-email-webhook-secret")||"",v=E.webhookSecret||process.env.EMAIL_WEBHOOK_SECRET||"";if(!v||y!==v)return g.NextResponse.json({error:"Unauthorized"},{status:401});let M=Number(E.rateLimitPerHour)>0?Number(E.rateLimitPerHour):20;if(!await b(o.toLowerCase(),M))return g.NextResponse.json({error:"Rate limited"},{status:429});if(p&&await R.prisma.emailThread.findUnique({where:{messageId:p}}))return g.NextResponse.json({ok:!0,deduped:!0});if(m){let e=await R.prisma.emailThread.findUnique({where:{messageId:m}});e?.ticketId&&(r=e.ticketId)}let O=await C(h,o.toLowerCase()),P=await T(h,s,l,E),k=await x(h,E);if(r){let e=await R.prisma.ticket.findUnique({where:{id:r},select:{id:!0,ticketNumber:!0}});if(!e)return g.NextResponse.json({error:"Parent ticket not found"},{status:404});a=e.id,i=e.ticketNumber,await R.prisma.ticketMessage.create({data:{content:c,isInternal:!1,ticketId:a,authorId:O.id}})}else i=function(){let e=new Date().toISOString().replace(/[-:TZ.]/g,"").slice(0,14),t=Math.floor(1e4*Math.random()).toString().padStart(4,"0");return`TKT-${e}-${t}`}(),a=(await R.prisma.ticket.create({data:{ticketNumber:i,title:l,description:c,status:"OPEN",priority:"MEDIUM",organizationId:h,createdById:O.id,projectId:k,departmentId:P,metadata:p?{messageId:p}:void 0},select:{id:!0}})).id,await R.prisma.ticketMessage.create({data:{content:`[AUDIT] Ticket created from email by ${o}`,isInternal:!0,ticketId:a,authorId:O.id}});if(await R.prisma.emailThread.create({data:{subject:l||"(no subject)",fromEmail:o,toEmail:s||"",messageId:p||`${Date.now()}-${Math.random().toString(36).slice(2)}`,inReplyTo:m,references:f,body:c,isHtml:!1,ticketId:a,userId:O.id}}),Array.isArray(t.attachments)&&t.attachments.length)for(let e of t.attachments.slice(0,5))try{let t=String(e.filename||"attachment"),r=String(e.mimeType||"application/octet-stream"),i=String(e.contentBase64||"");if(!i)continue;let n=Buffer.from(i,"base64");await N(n,t,r,a)}catch(e){console.error("Attachment save failed",e)}return await S({to:o,ticketNumber:i}),g.NextResponse.json({ok:!0,ticketId:a,ticketNumber:i})}catch(e){return console.error("Email inbound error",e),g.NextResponse.json({error:"Internal error"},{status:500})}}var O=e.i(69096);let P=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/email/inbound/route",pathname:"/api/email/inbound",filename:"route",bundlePath:""},distDir:"build",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/email/inbound/route.ts",nextConfigOutput:"",userland:O}),{workAsyncStorage:k,workUnitAsyncStorage:_,serverHooks:j}=P;function U(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:_})}async function D(e,t,a){var g;let R="/api/email/inbound/route";R=R.replace(/\/index$/,"")||"/";let E=await P.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!E)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:y,params:v,nextConfig:I,isDraftMode:A,prerenderManifest:C,routerServerContext:x,isOnDemandRevalidate:T,revalidateOnlyGenerated:N,resolvedPathname:S}=E,b=(0,o.normalizeAppPath)(R),M=!!(C.dynamicRoutes[b]||C.routes[S]);if(M&&!A){let e=!!C.routes[S],t=C.dynamicRoutes[b];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let O=null;!M||P.isDev||A||(O="/index"===(O=S)?"/":O);let k=!0===P.isDev||!M,_=M&&!k,j=e.method||"GET",U=(0,n.getTracer)(),D=U.getActiveScopeSpan(),$={params:v,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!I.experimental.cacheComponents,authInterrupts:!!I.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=I.experimental)?void 0:g.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>P.onRequestError(e,t,a,x)},sharedContext:{buildId:y}},H=new s.NodeNextRequest(e),q=new s.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let o=async r=>P.handle(z,$).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=a.get("next.route");if(i){let e=`${j} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${j} ${e.url}`)}),s=async n=>{var s,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&T&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let s=await o(n);e.fetchMetrics=$.renderOpts.fetchMetrics;let l=$.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=$.renderOpts.collectedTags;if(!M)return await (0,c.sendResponse)(H,q,s,$.renderOpts.pendingWaitUntil),null;{let e=await s.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(s.headers);d&&(t[f.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==$.renderOpts.collectedRevalidate&&!($.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&$.renderOpts.collectedRevalidate,a=void 0===$.renderOpts.collectedExpire||$.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:$.renderOpts.collectedExpire;return{value:{kind:w.CachedRouteKind.APP_ROUTE,status:s.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await P.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:T})},x),t}},h=await P.handleResponse({req:e,nextConfig:I,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:T,revalidateOnlyGenerated:N,responseGenerator:d,waitUntil:a.waitUntil});if(!M)return null;if((null==h||null==(s=h.value)?void 0:s.kind)!==w.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",T?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&M||g.delete(f.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,c.sendResponse)(H,q,new Response(h.value.body,{headers:g,status:h.value.status||200})),null};D?await s(D):await U.withPropagatedContext(e.headers,()=>U.trace(d.BaseServerSpan.handleRequest,{spanName:`${j} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":j,"http.target":e.url}},s))}catch(t){if(D||t instanceof h.NoFallbackError||await P.onRequestError(e,t,{routerKind:"App Router",routePath:b,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:T})}),M)throw t;return await (0,c.sendResponse)(H,q,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=node_modules_next_dist_esm_build_templates_app-route_b586ca63.js.map