{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/instrumentation/utils.ts", "turbopack:///[project]/node_modules/next/dist/esm/server/web/spec-extension/adapters/headers.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/node.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/get-cookie-parser.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/page-path/ensure-leading-slash.js", "turbopack:///[project]/node_modules/next/dist/src/server/lib/cache-control.ts", "turbopack:///[project]/lib/permissions.ts", "turbopack:///[project]/src/app/dashboard/reports/page.tsx"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n", "import { ReflectAdapter } from './reflect';\n/**\n * @internal\n */ export class ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nexport class HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map", "import { RedirectStatusCode } from '../../client/components/redirect-status-code';\nimport { getCookieParser } from '../api-utils/get-cookie-parser';\nexport class BaseNextRequest {\n    constructor(method, url, body){\n        this.method = method;\n        this.url = url;\n        this.body = body;\n    }\n    // Utils implemented using the abstract methods above\n    get cookies() {\n        if (this._cookies) return this._cookies;\n        return this._cookies = getCookieParser(this.headers)();\n    }\n}\nexport class BaseNextResponse {\n    constructor(destination){\n        this.destination = destination;\n    }\n    // Utils implemented using the abstract methods above\n    redirect(destination, statusCode) {\n        this.setHeader('Location', destination);\n        this.statusCode = statusCode;\n        // Since IE11 doesn't support the 308 header add backwards\n        // compatibility using refresh header\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n            this.setHeader('Refresh', `0;url=${destination}`);\n        }\n        return this;\n    }\n}\n\n//# sourceMappingURL=index.js.map", "import { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { PRERENDER_REVALIDATE_HEADER, PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER } from '../../lib/constants';\nimport { getTracer } from '../lib/trace/tracer';\nimport { NodeSpan } from '../lib/trace/constants';\nexport function wrapApiHandler(page, handler) {\n    return (...args)=>{\n        getTracer().setRootSpanAttribute('next.route', page);\n        // Call API route method\n        return getTracer().trace(NodeSpan.runHandler, {\n            spanName: `executing api route (pages) ${page}`\n        }, ()=>handler(...args));\n    };\n}\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */ export function sendStatusCode(res, statusCode) {\n    res.statusCode = statusCode;\n    return res;\n}\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */ export function redirect(res, statusOrUrl, url) {\n    if (typeof statusOrUrl === 'string') {\n        url = statusOrUrl;\n        statusOrUrl = 307;\n    }\n    if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n        throw Object.defineProperty(new Error(`Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`), \"__NEXT_ERROR_CODE\", {\n            value: \"E389\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    res.writeHead(statusOrUrl, {\n        Location: url\n    });\n    res.write(url);\n    res.end();\n    return res;\n}\nexport function checkIsOnDemandRevalidate(req, previewProps) {\n    const headers = HeadersAdapter.from(req.headers);\n    const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER);\n    const isOnDemandRevalidate = previewModeId === previewProps.previewModeId;\n    const revalidateOnlyGenerated = headers.has(PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER);\n    return {\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated\n    };\n}\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`;\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`;\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024;\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA);\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS);\nexport function clearPreviewData(res, options = {}) {\n    if (SYMBOL_CLEARED_COOKIES in res) {\n        return res;\n    }\n    const { serialize } = require('next/dist/compiled/cookie');\n    const previous = res.getHeader('Set-Cookie');\n    res.setHeader(`Set-Cookie`, [\n        ...typeof previous === 'string' ? [\n            previous\n        ] : Array.isArray(previous) ? previous : [],\n        serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        }),\n        serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        })\n    ]);\n    Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n        value: true,\n        enumerable: false\n    });\n    return res;\n}\n/**\n * Custom error class\n */ export class ApiError extends Error {\n    constructor(statusCode, message){\n        super(message);\n        this.statusCode = statusCode;\n    }\n}\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */ export function sendError(res, statusCode, message) {\n    res.statusCode = statusCode;\n    res.statusMessage = message;\n    res.end(message);\n}\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */ export function setLazyProp({ req }, prop, getter) {\n    const opts = {\n        configurable: true,\n        enumerable: true\n    };\n    const optsReset = {\n        ...opts,\n        writable: true\n    };\n    Object.defineProperty(req, prop, {\n        ...opts,\n        get: ()=>{\n            const value = getter();\n            // we set the property on the object to avoid recalculating it\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n            return value;\n        },\n        set: (value)=>{\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n        }\n    });\n}\n\n//# sourceMappingURL=index.js.map", "import { SYMBOL_CLEARED_COOKIES } from '../api-utils';\nimport { NEXT_REQUEST_META } from '../request-meta';\nimport { BaseNextRequest, BaseNextResponse } from './index';\nlet prop;\nexport class NodeNextRequest extends BaseNextRequest {\n    static #_ = prop = _NEXT_REQUEST_META = NEXT_REQUEST_META;\n    constructor(_req){\n        var _this__req;\n        super(_req.method.toUpperCase(), _req.url, _req), this._req = _req, this.headers = this._req.headers, this.fetchMetrics = (_this__req = this._req) == null ? void 0 : _this__req.fetchMetrics, this[_NEXT_REQUEST_META] = this._req[NEXT_REQUEST_META] || {}, this.streaming = false;\n    }\n    get originalRequest() {\n        // Need to mimic these changes to the original req object for places where we use it:\n        // render.tsx, api/ssg requests\n        this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META];\n        this._req.url = this.url;\n        this._req.cookies = this.cookies;\n        return this._req;\n    }\n    set originalRequest(value) {\n        this._req = value;\n    }\n    /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */ stream() {\n        if (this.streaming) {\n            throw Object.defineProperty(new Error('Invariant: NodeNextRequest.stream() can only be called once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E467\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        this.streaming = true;\n        return new ReadableStream({\n            start: (controller)=>{\n                this._req.on('data', (chunk)=>{\n                    controller.enqueue(new Uint8Array(chunk));\n                });\n                this._req.on('end', ()=>{\n                    controller.close();\n                });\n                this._req.on('error', (err)=>{\n                    controller.error(err);\n                });\n            }\n        });\n    }\n}\nexport class NodeNextResponse extends BaseNextResponse {\n    get originalResponse() {\n        if (SYMBOL_CLEARED_COOKIES in this) {\n            this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES];\n        }\n        return this._res;\n    }\n    constructor(_res){\n        super(_res), this._res = _res, this.textBody = undefined;\n    }\n    get sent() {\n        return this._res.finished || this._res.headersSent;\n    }\n    get statusCode() {\n        return this._res.statusCode;\n    }\n    set statusCode(value) {\n        this._res.statusCode = value;\n    }\n    get statusMessage() {\n        return this._res.statusMessage;\n    }\n    set statusMessage(value) {\n        this._res.statusMessage = value;\n    }\n    setHeader(name, value) {\n        this._res.setHeader(name, value);\n        return this;\n    }\n    removeHeader(name) {\n        this._res.removeHeader(name);\n        return this;\n    }\n    getHeaderValues(name) {\n        const values = this._res.getHeader(name);\n        if (values === undefined) return undefined;\n        return (Array.isArray(values) ? values : [\n            values\n        ]).map((value)=>value.toString());\n    }\n    hasHeader(name) {\n        return this._res.hasHeader(name);\n    }\n    getHeader(name) {\n        const values = this.getHeaderValues(name);\n        return Array.isArray(values) ? values.join(',') : undefined;\n    }\n    getHeaders() {\n        return this._res.getHeaders();\n    }\n    appendHeader(name, value) {\n        const currentValues = this.getHeaderValues(name) ?? [];\n        if (!currentValues.includes(value)) {\n            this._res.setHeader(name, [\n                ...currentValues,\n                value\n            ]);\n        }\n        return this;\n    }\n    body(value) {\n        this.textBody = value;\n        return this;\n    }\n    send() {\n        this._res.end(this.textBody);\n    }\n    onClose(callback) {\n        this.originalResponse.on('close', callback);\n    }\n}\nvar _NEXT_REQUEST_META;\n\n//# sourceMappingURL=node.js.map", "/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */ export function getCookieParser(headers) {\n    return function parseCookie() {\n        const { cookie } = headers;\n        if (!cookie) {\n            return {};\n        }\n        const { parse: parseCookieFn } = require('next/dist/compiled/cookie');\n        return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie);\n    };\n}\n\n//# sourceMappingURL=get-cookie-parser.js.map", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash';\nimport { isGroupSegment } from '../../segment';\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */ export function normalizeAppPath(route) {\n    return ensureLeadingSlash(route.split('/').reduce((pathname, segment, index, segments)=>{\n        // Empty segments are ignored.\n        if (!segment) {\n            return pathname;\n        }\n        // Groups are ignored.\n        if (isGroupSegment(segment)) {\n            return pathname;\n        }\n        // Parallel segments are ignored.\n        if (segment[0] === '@') {\n            return pathname;\n        }\n        // The last segment (if it's a leaf) should be ignored.\n        if ((segment === 'page' || segment === 'route') && index === segments.length - 1) {\n            return pathname;\n        }\n        return pathname + \"/\" + segment;\n    }, ''));\n}\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */ export function normalizeRscURL(url) {\n    return url.replace(/\\.rsc($|\\?)/, // $1 ensures `?` is preserved\n    '$1');\n}\n\n//# sourceMappingURL=app-paths.js.map", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ export function ensureLeadingSlash(path) {\n    return path.startsWith('/') ? path : \"/\" + path;\n}\n\n//# sourceMappingURL=ensure-leading-slash.js.map", "import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n", "import { prisma } from '@/lib/prisma'\n\nexport type PermissionKey =\n  | 'DASHBOARD_VIEW'\n  | 'TICKETS_VIEW'\n  | 'TICKETS_UPDATE'\n  | 'TICKETS_ASSIGN'\n  | 'REPORTS_VIEW'\n  | 'DEPARTMENTS_MANAGE'\n  | 'PROJECTS_MANAGE'\n  | 'AUDIT_VIEW'\n\nexport const ALL_PERMISSIONS: PermissionKey[] = [\n  'DASHBOARD_VIEW',\n  'TICKETS_VIEW',\n  'TICKETS_UPDATE',\n  'TICKETS_ASSIGN',\n  'REPORTS_VIEW',\n  'DEPARTMENTS_MANAGE',\n  'PROJECTS_MANAGE',\n  'AUDIT_VIEW',\n]\n\nexport type AuthUserLite = {\n  id: string\n  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'\n  organizationId: string\n}\n\nexport async function getUserPermissions(user: AuthUserLite): Promise<Set<PermissionKey>> {\n  // Admins implicitly have all permissions\n  if (user.role === 'ADMIN') return new Set<PermissionKey>(ALL_PERMISSIONS)\n\n  // Everyone else: only what Admin explicitly assigned\n  const rows = await prisma.userPermission.findMany({\n    where: { userId: user.id, organizationId: user.organizationId },\n    select: { permission: true },\n  })\n  return new Set(rows.map(r => r.permission as PermissionKey))\n}\n\nexport function hasPermission(perms: Set<PermissionKey>, key: PermissionKey) {\n  return perms.has(key)\n}\n\n", "import { cookies } from 'next/headers'\nimport { prisma } from '@/lib/prisma'\nimport { verifyToken } from '@/lib/auth-new'\nimport { getUserPermissions, hasPermission } from '@/lib/permissions'\n\nfunction fmt(d: Date) {\n  return d.toISOString().slice(0, 10)\n}\n\nexport default async function ReportsPage({ searchParams }: { searchParams?: Record<string, string | string[] | undefined> }) {\n  const cookieStore = await cookies()\n  const token = cookieStore.get('auth-token')?.value\n  const user = token ? verifyToken(token) : null\n  if (!user) return <div className=\"p-6\">Unauthorized</div>\n\n  // Permission: non-admins must have TICKETS_VIEW to see reports\n  if (user.role !== 'ADMIN') {\n    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })\n    if (!hasPermission(perms, 'TICKETS_VIEW')) {\n      return <div className=\"p-6\">Forbidden</div>\n    }\n  }\n\n  // Scope by projects and departments for non-admins\n  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })\n  const projectIds = memberships.map(m => m.projectId)\n  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })\n  const departmentIds = deptLinks.map(d => d.departmentId)\n\n  const baseWhere: any = user.role === 'ADMIN' ? { organizationId: user.organizationId } : {\n    organizationId: user.organizationId,\n    projectId: { in: projectIds.length ? projectIds : ['__none__'] },\n    departmentId: { in: departmentIds.length ? departmentIds : ['__none__'] },\n  }\n\n  // Filters\n  const status = typeof searchParams?.status === 'string' ? searchParams!.status.trim() : ''\n  const projectId = typeof searchParams?.projectId === 'string' ? searchParams!.projectId.trim() : ''\n  const departmentId = typeof searchParams?.departmentId === 'string' ? searchParams!.departmentId.trim() : ''\n  const fromStr = typeof searchParams?.from === 'string' ? searchParams!.from.trim() : ''\n  const toStr = typeof searchParams?.to === 'string' ? searchParams!.to.trim() : ''\n\n  const where: any = {\n    ...baseWhere,\n    ...(status ? { status } : {}),\n    ...(projectId ? { projectId } : {}),\n    ...(departmentId ? { departmentId } : {}),\n    ...(fromStr ? { createdAt: { gte: new Date(fromStr) } } : {}),\n    ...(toStr ? { createdAt: { ...(fromStr ? { gte: new Date(fromStr) } : {}), lte: new Date(toStr) } } : {}),\n  }\n\n  // For select options\n  const [projectsList, departmentsList] = await Promise.all([\n    user.role === 'ADMIN'\n      ? prisma.project.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })\n      : prisma.project.findMany({ where: { id: { in: projectIds.length ? projectIds : ['__none__'] } }, select: { id: true, name: true } }),\n    user.role === 'ADMIN'\n      ? prisma.department.findMany({ where: { organizationId: user.organizationId }, select: { id: true, name: true } })\n      : prisma.department.findMany({ where: { id: { in: departmentIds.length ? departmentIds : ['__none__'] } }, select: { id: true, name: true } }),\n  ])\n\n  // Stats\n  const [countsByStatus, countsByDeptRaw, countsByProjRaw, totals, timeWindowTickets] = await Promise.all([\n    prisma.ticket.groupBy({ by: ['status'], where, _count: { _all: true } }),\n    prisma.ticket.groupBy({ by: ['departmentId'], where, _count: { _all: true } }),\n    prisma.ticket.groupBy({ by: ['projectId'], where, _count: { _all: true } }),\n    Promise.all([\n      prisma.ticket.count({ where }),\n      prisma.ticket.count({ where: { ...where, status: 'OPEN' } }),\n      prisma.ticket.count({ where: { ...where, status: 'IN_PROGRESS' } }),\n      prisma.ticket.count({ where: { ...where, status: 'RESOLVED' } }),\n      prisma.ticket.count({ where: { ...where, status: 'CLOSED' } }),\n      prisma.ticket.count({ where: { ...where, status: 'CANCELLED' } }),\n    ]),\n    // time series (last 30 days or provided window)\n    prisma.ticket.findMany({\n      where: {\n        ...where,\n        ...(fromStr || toStr\n          ? {}\n          : { createdAt: { gte: new Date(Date.now() - 29 * 24 * 3600 * 1000) } }),\n      },\n      select: { id: true, createdAt: true },\n      orderBy: { createdAt: 'asc' },\n    }),\n  ])\n\n  const [total, open, wip, resolved, closed, cancelled] = totals\n\n  // Map dept/project names\n  const deptNameMap = new Map(departmentsList.map(d => [d.id, d.name]))\n  const projNameMap = new Map(projectsList.map(p => [p.id, p.name]))\n\n  // Build timeseries counts by day\n  const seriesMap = new Map<string, number>()\n  for (const t of timeWindowTickets) {\n    const day = fmt(t.createdAt)\n    seriesMap.set(day, (seriesMap.get(day) || 0) + 1)\n  }\n  // Ensure continuous window for nice output (last 14 days for brevity)\n  const now = new Date()\n  const start = new Date(fromStr || new Date(Date.now() - 13 * 24 * 3600 * 1000))\n  const series: { day: string; count: number }[] = []\n  for (let d = new Date(start); d <= now; d = new Date(d.getTime() + 24 * 3600 * 1000)) {\n    const key = fmt(d)\n    series.push({ day: key, count: seriesMap.get(key) || 0 })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Reports</h1>\n              <p className=\"mt-1 text-sm text-gray-600\">Organization analytics (scoped to your access)</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <form method=\"GET\" className=\"bg-white rounded-lg shadow p-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-6 gap-3\">\n            <div>\n              <label className=\"block text-xs text-gray-600\">Status</label>\n              <select name=\"status\" defaultValue={status} className=\"mt-1 w-full border rounded px-3 py-2\">\n                <option value=\"\">All</option>\n                {['OPEN','IN_PROGRESS','WAITING_FOR_CUSTOMER','WAITING_FOR_AGENT','RESOLVED','CLOSED','CANCELLED'].map(s => (\n                  <option key={s} value={s}>{s.replaceAll('_',' ')}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-xs text-gray-600\">Project</label>\n              <select name=\"projectId\" defaultValue={projectId} className=\"mt-1 w-full border rounded px-3 py-2\">\n                <option value=\"\">All</option>\n                {projectsList.map((p:any) => (\n                  <option key={p.id} value={p.id}>{p.name}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-xs text-gray-600\">Department</label>\n              <select name=\"departmentId\" defaultValue={departmentId} className=\"mt-1 w-full border rounded px-3 py-2\">\n                <option value=\"\">All</option>\n                {departmentsList.map((d:any) => (\n                  <option key={d.id} value={d.id}>{d.name}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-xs text-gray-600\">From</label>\n              <input type=\"date\" name=\"from\" defaultValue={fromStr} className=\"mt-1 w-full border rounded px-3 py-2\" />\n            </div>\n            <div>\n              <label className=\"block text-xs text-gray-600\">To</label>\n              <input type=\"date\" name=\"to\" defaultValue={toStr} className=\"mt-1 w-full border rounded px-3 py-2\" />\n            </div>\n            <div className=\"flex items-end gap-2\">\n              <button type=\"submit\" className=\"inline-flex items-center justify-center rounded bg-blue-600 text-white hover:bg-blue-700 h-10 px-4 py-2\">Apply</button>\n              <a href=\"/dashboard/reports\" className=\"inline-flex items-center justify-center rounded border border-gray-300 text-gray-700 hover:bg-gray-50 h-10 px-4 py-2\">Reset</a>\n            </div>\n          </div>\n        </form>\n\n        {/* KPI cards */}\n        <div className=\"grid grid-cols-2 md:grid-cols-6 gap-4\">\n          {[{label:'Open',value:open},{label:'WIP',value:wip},{label:'Resolved',value:resolved},{label:'Closed',value:closed},{label:'Discarded',value:cancelled},{label:'Total',value:total}].map(s=> (\n            <div key={s.label} className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"text-sm text-gray-500\">{s.label}</div>\n              <div className=\"text-2xl font-bold\">{s.value}</div>\n            </div>\n          ))}\n        </div>\n\n        {/* By status */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-4 border-b\"><h2 className=\"font-semibold\">Tickets by Status</h2></div>\n          <ul className=\"divide-y\">\n            {countsByStatus.sort((a,b)=>a.status.localeCompare(b.status)).map(row => (\n              <li key={row.status} className=\"p-3 flex items-center justify-between\">\n                <div className=\"text-sm\">{row.status.replaceAll('_',' ')}</div>\n                <div className=\"text-sm font-semibold\">{row._count._all}</div>\n              </li>\n            ))}\n            {countsByStatus.length === 0 && <li className=\"p-4 text-sm text-gray-500\">No data.</li>}\n          </ul>\n        </div>\n\n        {/* By department */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-4 border-b\"><h2 className=\"font-semibold\">Tickets by Department</h2></div>\n          <ul className=\"divide-y\">\n            {countsByDeptRaw.sort((a,b)=> (deptNameMap.get(a.departmentId||'')||'').localeCompare(deptNameMap.get(b.departmentId||'')||'')).map(row => (\n              <li key={row.departmentId || 'none'} className=\"p-3 flex items-center justify-between\">\n                <div className=\"text-sm\">{deptNameMap.get(row.departmentId || '') || 'Unassigned'}</div>\n                <div className=\"text-sm font-semibold\">{row._count._all}</div>\n              </li>\n            ))}\n            {countsByDeptRaw.length === 0 && <li className=\"p-4 text-sm text-gray-500\">No data.</li>}\n          </ul>\n        </div>\n\n        {/* By project */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-4 border-b\"><h2 className=\"font-semibold\">Tickets by Project</h2></div>\n          <ul className=\"divide-y\">\n            {countsByProjRaw.sort((a,b)=> (projNameMap.get(a.projectId||'')||'').localeCompare(projNameMap.get(b.projectId||'')||'')).map(row => (\n              <li key={row.projectId || 'none'} className=\"p-3 flex items-center justify-between\">\n                <div className=\"text-sm\">{projNameMap.get(row.projectId || '') || 'Unassigned'}</div>\n                <div className=\"text-sm font-semibold\">{row._count._all}</div>\n              </li>\n            ))}\n            {countsByProjRaw.length === 0 && <li className=\"p-4 text-sm text-gray-500\">No data.</li>}\n          </ul>\n        </div>\n\n        {/* Created over time */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-4 border-b\"><h2 className=\"font-semibold\">Tickets Created Over Time</h2></div>\n          <div className=\"p-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-700\">\n              {series.slice(-14).map(pt => (\n                <div key={pt.day} className=\"flex items-center justify-between bg-gray-50 rounded px-3 py-2\">\n                  <span>{pt.day}</span>\n                  <span className=\"font-semibold\">{pt.count}</span>\n                </div>\n              ))}\n              {series.length === 0 && (\n                <div className=\"text-sm text-gray-500\">No tickets in this period.</div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined", "CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>"], "mappings": "8LAAO,SAASA,EAAoBC,CAGnC,SACKA,AAAJ,EAAWC,oBAAoB,CACtB,CADwB,WAG7BD,EAAOE,YAAY,CACd,CADgB,aAI3B,0HCXA,IG0HI,EH1HJ,EAAA,EAAA,CAAA,CAAA,MAGW,MGyHX,CHzHiB,UAA6B,MAC1C,aAAa,CACT,CGuHwB,IHvHnB,CAAC,qGACV,CACA,OAAO,UAAW,CACd,MAAM,IAAI,CACd,CACJ,CACO,MAAM,UAAuB,QAChC,YAAY,CAAO,CAAC,CAGhB,KAAK,GACL,IAAI,CAAC,OAAO,CAAG,IAAI,MAAM,EAAS,CAC9B,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EAIvB,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,GAE5C,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,GAAI,KAAoB,IAAb,EAEX,OAFqC,AAE9B,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAU,EAChD,EACA,IAAK,CAAM,CAAE,CAAI,CAAE,CAAK,CAAE,CAAQ,EAC9B,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAAO,GAEnD,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAAY,EAAM,EAAO,EAC/D,EACA,IAAK,CAAM,CAAE,CAAI,EACb,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAChE,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,IAAI,CAAoB,IAAb,GAEJ,EAAA,IAF8B,OAAO,GAEvB,CAAC,GAAG,CAAC,EAAQ,EACtC,EACA,eAAgB,CAAM,CAAE,CAAI,EACxB,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,cAAc,CAAC,EAAQ,GAC3E,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,AAAwB,IAApB,KAAO,GAEJ,CAF8B,CAE9B,MAFqC,QAEvB,CAAC,cAAc,CAAC,EAAQ,EACjD,CACJ,EACJ,CAIE,OAAO,KAAK,CAAO,CAAE,CACnB,OAAO,IAAI,MAAM,EAAS,CACtB,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EACvB,OAAO,GACH,IAAK,SACL,IAAK,SACL,IAAK,MACD,OAAO,EAAqB,QAAQ,AACxC,SACI,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAChD,CACJ,CACJ,EACJ,CAOE,MAAM,CAAK,CAAE,QACP,AAAJ,MAAU,OAAO,CAAC,GAAe,EAAM,GAAb,CAAiB,CAAC,MACrC,CACX,CAME,OAAO,KAAK,CAAO,CAAE,QACnB,AAAI,aAAmB,QAAgB,CAAP,CACzB,IAAI,EAAe,EAC9B,CACA,OAAO,CAAI,CAAE,CAAK,CAAE,CAChB,IAAM,EAAW,IAAI,CAAC,OAAO,CAAC,EAAK,CACX,UAApB,AAA8B,OAAvB,EACP,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACjB,EACA,EACH,CACM,MAAM,OAAO,CAAC,GACrB,EAAS,IAAI,CAAC,CADkB,EAGhC,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CAE7B,CACA,OAAO,CAAI,CAAE,CACT,OAAO,IAAI,CAAC,OAAO,CAAC,EAAK,AAC7B,CACA,IAAI,CAAI,CAAE,CACN,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,EAAK,QAC5B,AAAJ,KAAqB,IAAV,EAA8B,IAAI,CAAC,EAAZ,GAAiB,CAAC,GAC7C,IACX,CACA,IAAI,CAAI,CAAE,CACN,OAAO,KAA8B,IAAvB,IAAI,CAAC,OAAO,CAAC,EAAK,AACpC,CACA,IAAI,CAAI,CAAE,CAAK,CAAE,CACb,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACzB,CACA,QAAQ,CAAU,CAAE,CAAO,CAAE,CACzB,IAAK,GAAM,CAAC,EAAM,EAAM,GAAI,IAAI,CAAC,OAAO,GAAG,AACvC,EAAW,IAAI,CAAC,EAAS,EAAO,EAAM,IAAI,CAElD,CACA,CAAC,SAAU,CACP,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,GAGtB,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,MAAM,CACF,EACA,EACH,AACL,CACJ,CACA,CAAC,MAAO,CACJ,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,EAC5B,OAAM,CACV,CACJ,CACA,CAAC,QAAS,CACN,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAGxC,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,OAAM,CACV,CACJ,CACA,CAAC,OAAO,QAAQ,CAAC,EAAG,CAChB,OAAO,IAAI,CAAC,OAAO,EACvB,CACJ,CExKA,CF0KA,CE1KA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAuDmC,GFiHA,IEnHO,AAEA,CAFC,mBAAmB,CAAC,EAGxD,IAAM,EAAyB,OAAO,AAJD,CAAC,kBAAkB,CAAC,ECtDhE,IAAA,EAAA,EAAA,CAAA,CAAA,OFDA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACT,YAAY,CAAM,CAAE,CAAG,CAAE,CAAI,CAAC,CAC1B,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,GAAG,CAAG,EACX,IAAI,CAAC,IAAI,CAAG,CAChB,CAEA,IAAI,SAAU,cACV,AAAI,IAAI,CAAC,QAAQ,CAAS,CAAP,GAAW,CAAC,QAAQ,CAChC,IAAI,CAAC,QAAQ,CAAG,CGRK,EHQW,IAAI,CGRR,AHQS,OAAO,CGPhD,SAAS,EACZ,GAAM,QAAE,CAAM,CAAE,CAAG,EACnB,GAAI,CAAC,EACD,MADS,AACF,CAAC,EAEZ,GAAM,CAAE,MAAO,CAAa,CAAE,CAAA,EAAA,CAAA,CAAA,OAC9B,OAAO,EAAc,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,MAAQ,EACrE,IHCA,CACJ,CACO,MAAM,EACT,YAAY,CAAW,CAAC,CACpB,IAAI,CAAC,WAAW,CAAG,CACvB,CAEA,SAAS,CAAW,CAAE,CAAU,CAAE,CAQ9B,OAPA,IAAI,CAAC,SAAS,CAAC,WAAY,GAC3B,IAAI,CAAC,UAAU,CAAG,EAGd,IAAe,EAAA,kBAAkB,CAAC,iBAAiB,EAAE,AACrD,IAAI,CAAC,SAAS,CAAC,UAAW,CAAC,MAAM,EAAE,EAAA,CAAa,EAE7C,IAAI,AACf,CACJ,CEzBO,CF2BP,KE3Ba,UAAwB,EACjC,QAAO,CAAA,AAAE,CAAU,EAAP,AAA4B,EAAA,EF0BX,eE1B4B,AAAC,AAC1D,aAAY,CAAI,CAAC,CACb,IAAI,EACJ,KAAK,CAAC,EAAK,MAAM,CAAC,WAAW,GAAI,EAAK,GAAG,CAAE,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,YAAY,CAAG,AAA4B,MAA3B,GAAa,IAAI,CAAC,IAAI,AAAJ,EAAgB,KAAK,EAAI,EAAW,YAAY,CAAE,IAAI,CAAC,EAAmB,CAAG,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,EAAI,CAAC,EAAG,IAAI,CAAC,SAAS,EAAG,CACnR,CACA,IAAI,iBAAkB,CAMlB,OAHA,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,CAAG,IAAI,CAAC,EAAA,iBAAiB,CAAC,CACtD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,GAAG,CACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CACzB,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,gBAAgB,CAAK,CAAE,CACvB,IAAI,CAAC,IAAI,CAAG,CAChB,CAOE,QAAS,CACP,GAAI,IAAI,CAAC,SAAS,CACd,CADgB,KACV,OAAO,cAAc,CAAC,AAAI,MAAM,+DAAgE,oBAAqB,CACvH,MAAO,OACP,WAAY,GACZ,aAAc,EAClB,GAGJ,OADA,IAAI,CAAC,SAAS,EAAG,EACV,IAAI,eAAe,CACtB,MAAQ,AAAD,IACH,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAQ,AAAC,IAClB,EAAW,OAAO,CAAC,IAAI,WAAW,GACtC,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAO,KAChB,EAAW,KAAK,EACpB,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAS,AAAC,IACnB,EAAW,KAAK,CAAC,EACrB,EACJ,CACJ,EACJ,CACJ,CACO,MAAM,UAAyB,EAClC,IAAI,kBAAmB,CAInB,OAHI,KAA0B,IAAI,EAAE,CAChC,IAAI,CAAC,IAAI,CAAC,EAAuB,CAAG,IAAI,CAAC,EAAA,AAAuB,EAE7D,IAAI,CAAC,IAChB,AADoB,CAEpB,YAAY,CAAI,CAAC,CACb,KAAK,CAAC,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,QAAQ,MAAG,CACnD,CACA,IAAI,MAAO,CACP,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,IAAI,CAAC,WAC3C,AADsD,CAEtD,IAAI,YAAa,CACb,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,AAC/B,CACA,IAAI,WAAW,CAAK,CAAE,CAClB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAG,CAC3B,CACA,IAAI,eAAgB,CAChB,OAAO,IAAI,CAAC,IAAI,CAAC,aACrB,AADkC,CAElC,IAAI,cAAc,CAAK,CAAE,CACrB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAG,CAC9B,CACA,UAAU,CAAI,CAAE,CAAK,CAAE,CAEnB,OADA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,GACnB,IAAI,AACf,CACA,aAAa,CAAI,CAAE,CAEf,OADA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAChB,IAAI,AACf,CACA,gBAAgB,CAAI,CAAE,CAClB,IAAM,EAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GACnC,GAAI,KAAW,MACf,KAD0B,CACnB,CAAC,KADyB,CACnB,OAAO,CAAC,GAAU,EAAS,CACrC,EACH,EAAE,GAAG,CAAC,AAAC,GAAQ,EAAM,QAAQ,GAClC,CACA,UAAU,CAAI,CAAE,CACZ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAC/B,CACA,UAAU,CAAI,CAAE,CACZ,IAAM,EAAS,IAAI,CAAC,eAAe,CAAC,GACpC,OAAO,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,KAAO,MACtD,CACA,YAAa,CACT,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAC/B,CACA,aAAa,CAAI,CAAE,CAAK,CAAE,CACtB,IAAM,EAAgB,IAAI,CAAC,eAAe,CAAC,IAAS,EAAE,CAOtD,OANI,AAAC,EAAc,QAAQ,CAAC,IACxB,IADgC,AAC5B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,IACnB,EACH,EACH,EAEE,IAAI,AACf,CACA,KAAK,CAAK,CAAE,CAER,OADA,IAAI,CAAC,QAAQ,CAAG,EACT,IAAI,AACf,CACA,MAAO,CACH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC/B,CACA,QAAQ,CAAQ,CAAE,CACd,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,QAAS,EACtC,CACJ,gEExHA,IAAA,EAAA,EAAA,CAAA,CAAA,OAmBW,SAAS,EAAiB,CAAK,QACtC,MCjBO,CDiBA,AClB4B,EDkBT,EAAM,AClBO,KDkBF,CAAC,KAAK,MAAM,CAAC,CAAC,EAAU,EAAS,EAAO,IAEzE,AAAI,CAAC,GAID,CAAA,EAAA,EAAA,CAJU,aAII,AAAd,EAAe,IAIA,KAAK,CAJK,AAIzB,CAAO,CAAC,EAAE,EAIV,CAAa,SAAZ,GAAkC,UAAZ,CAAY,CAAO,EAAK,IAAU,EAAS,MAAM,CAAG,EAXpE,CAWuE,CAG3E,EAAW,IAAM,EACzB,KCnCS,UAAU,CAAC,KAAO,EAAO,IAAM,CDoC/C,+DExCA,IAAA,EAA+B,EAAqB,CAA3CE,AAA2C,CAAA,IAAA,GAgB7C,MAhBgB,GAgBPC,EAAsB,CACpCC,EAjB6B,UAiBnB,QACVC,CAAM,CACO,EACb,IAAMC,EACkB,UAAtB,OAAOF,QACIH,IAAXI,GACAD,EAAaC,EACT,CAAC,yBAAyB,EAAEA,EAASD,EAAAA,CAAY,CACjD,UAEN,AAAmB,GAAG,CAAlBA,EACK,0DACwB,UAAtB,AAAgC,OAAzBA,EACT,CAAC,SAAS,EAAEA,EAAAA,EAAaE,EAAAA,CAAW,CAGtC,CAAC,SAAS,EAAEJ,EAAAA,cAAAA,CAAAA,EAAiBI,EAAAA,CAAW,AACjD,uIClCA,IAAA,EAAA,EAAA,CAAA,CAAA,OAYO,IAAM,EAAmC,CAC9C,iBACA,eACA,iBACA,iBACA,eACA,qBACA,kBACA,aACD,CAQM,eAAe,EAAmB,CAAkB,aAEnB,IAApB,SAAS,CAAvB,EAAK,IAAI,AAAqB,CAAuB,EAO1C,CAJF,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAChD,MAAO,CAAE,OAAQ,EAAK,EAAE,CAAE,eAAgB,EAAK,cAAc,AAAC,EAC9D,OAAQ,CAAE,YAAY,CAAK,CAC7B,EAAA,EACoB,GAAG,CAAC,GAAK,EAAE,UAAU,EAC3C,CAEO,SAAS,EAAc,CAAyB,CAAE,CAAkB,EACzE,OAAO,EAAM,GAAG,CAAC,EACnB,kFC3CA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAEA,SAAS,EAAI,CAAO,EAClB,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,EAAG,GAClC,CAEe,eAAe,EAAY,cAAE,CAAY,CAAoE,EAC1H,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAO,IAC3B,EAAQ,EAAY,GAAG,CAAC,eAAe,MACvC,EAAO,EAAQ,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GAAS,KAC1C,GAAI,CAAC,EAAM,MAAO,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,eAAM,iBAGvC,GAAkB,UAAd,EAAK,IAAI,CAAc,CACzB,IAAM,EAAQ,MAAM,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,CAAE,GAAI,EAAK,EAAE,CAAE,KAAM,EAAK,IAAI,CAAE,eAAgB,EAAK,cAAe,AAAD,GAC1G,GAAI,CAAC,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,EAAO,gBACxB,CADyC,KAClC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,eAAM,aAEhC,CAIA,IAAM,EAAa,CADC,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAK,EAAE,AAAC,EAAG,OAAQ,CAAE,WAAW,CAAK,CAAE,EAAA,EACjF,GAAG,CAAC,GAAK,EAAE,SAAS,EAE7C,EAAgB,CADJ,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAK,EAAE,AAAC,EAAG,OAAQ,CAAE,cAAc,CAAK,CAAE,EAAA,EACpF,GAAG,CAAC,GAAK,EAAE,YAAY,EAEjD,EAA+B,UAAd,EAAK,IAAI,CAAe,CAAE,eAAgB,EAAK,cAAc,AAAC,EAAI,CACvF,eAAgB,EAAK,cAAc,CACnC,UAAW,CAAE,GAAI,EAAW,MAAM,CAAG,EAAa,CAAC,WAAW,AAAC,EAC/D,aAAc,CAAE,GAAI,EAAc,MAAM,CAAG,EAAgB,CAAC,WAAW,AAAC,CAC1E,EAGM,EAAyC,UAAhC,OAAO,GAAc,OAAsB,EAAc,MAAM,CAAC,IAAI,GAAK,GAClF,EAA+C,UAAnC,OAAO,GAAc,UAAyB,EAAc,SAAS,CAAC,IAAI,GAAK,GAC3F,EAAqD,UAAtC,OAAO,GAAc,aAA4B,EAAc,YAAY,CAAC,IAAI,GAAK,GACpG,EAAwC,UAA9B,OAAO,GAAc,KAAoB,EAAc,IAAI,CAAC,IAAI,GAAK,GAC/E,EAAoC,UAA5B,OAAO,GAAc,GAAkB,EAAc,EAAE,CAAC,IAAI,GAAK,GAEzE,EAAa,CACjB,GAAG,CAAS,CACZ,GAAI,EAAS,QAAE,CAAO,EAAI,CAAC,CAAC,CAC5B,GAAI,EAAY,WAAE,CAAU,EAAI,CAAC,CAAC,CAClC,GAAI,EAAe,cAAE,CAAa,EAAI,CAAC,CAAC,CACxC,GAAI,EAAU,CAAE,UAAW,CAAE,IAAK,IAAI,KAAK,EAAS,CAAE,EAAI,CAAC,CAAC,CAC5D,GAAI,EAAQ,CAAE,UAAW,CAAE,GAAI,EAAU,CAAE,IAAK,IAAI,KAAK,EAAS,EAAI,CAAC,CAAC,CAAG,IAAK,IAAI,KAAK,EAAO,CAAE,EAAI,CAAC,CAAC,AAC1G,EAGM,CAAC,EAAc,EAAgB,CAAG,MAAM,QAAQ,GAAG,CAAC,CAC1C,UAAd,EAAK,IAAI,CACL,EAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAK,cAAc,AAAC,EAAG,OAAQ,CAAE,IAAI,EAAM,MAAM,CAAK,CAAE,GAC3G,EAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,GAAI,CAAE,GAAI,EAAW,MAAM,CAAG,EAAa,CAAC,WAAW,AAAC,CAAE,EAAG,OAAQ,CAAE,IAAI,EAAM,MAAM,CAAK,CAAE,GACvH,UAAd,EAAK,IAAI,CACL,EAAA,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAK,cAAe,AAAD,EAAI,OAAQ,CAAE,IAAI,EAAM,MAAM,CAAK,CAAE,GAC9G,EAAA,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,GAAI,CAAE,GAAI,EAAc,MAAM,CAAG,EAAgB,CAAC,WAAW,AAAC,CAAE,EAAG,OAAQ,CAAE,IAAI,EAAM,MAAM,CAAK,CAAE,GAC/I,EAGK,CAAC,EAAgB,EAAiB,EAAiB,EAAQ,EAAkB,CAAG,MAAM,QAAQ,GAAG,CAAC,CACtG,EAAA,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAE,GAAI,CAAC,SAAS,OAAE,EAAO,OAAQ,CAAE,MAAM,CAAK,CAAE,GACtE,EAAA,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAE,GAAI,CAAC,eAAe,OAAE,EAAO,OAAQ,CAAE,MAAM,CAAK,CAAE,GAC5E,EAAA,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAE,GAAI,CAAC,YAAY,OAAE,EAAO,OAAQ,CAAE,MAAM,CAAK,CAAE,GACzE,QAAQ,GAAG,CAAC,CACV,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAE,CAAM,GAC5B,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAK,CAAE,OAAQ,MAAO,CAAE,GAC1D,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAK,CAAE,OAAQ,aAAc,CAAE,GACjE,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAK,CAAE,OAAQ,UAAW,CAAE,GAC9D,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAK,CAAE,OAAQ,QAAS,CAAE,GAC5D,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAK,CAAE,OAAQ,WAAY,CAAE,GAChE,EAED,EAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CACrB,MAAO,CACL,GAAG,CAAK,CACR,GAAI,GAAW,EACX,CAAC,EACD,CAAE,UAAW,CAAE,IAAK,IAAI,KAAK,KAAK,GAAG,GAAK,KAAK,GAAkB,CAAE,CAAC,AAC1E,AAD0D,EAE1D,KAFiE,EAEzD,CAAE,IAAI,EAAM,WAAW,CAAK,EACpC,QAAS,CAAE,UAAW,KAAM,CAC9B,GACD,EAEK,CAAC,EAAO,EAAM,EAAK,EAAU,EAAQ,EAAU,CAAG,EAGlD,EAAc,IAAI,IAAI,EAAgB,GAAG,CAAC,GAAK,CAAC,EAAE,EAAE,CAAE,EAAE,IAAI,CAAC,GAC7D,EAAc,IAAI,IAAI,EAAa,GAAG,CAAC,GAAK,CAAC,EAAE,EAAE,CAAE,EAAE,IAAI,CAAC,GAG1D,EAAY,IAAI,IACtB,IAAK,IAAM,KAAK,EAAmB,CACjC,IAAM,EAAM,EAAI,EAAE,SAAS,EAC3B,EAAU,GAAG,CAAC,EAAK,CAAC,EAAU,GAAG,CAAC,KAAQ,CAAC,CAAI,EACjD,CAEA,IAAM,EAAM,IAAI,KACV,EAAQ,IAAI,KAAK,GAAW,IAAI,KAAK,KAAK,GAAG,GAAK,KAAK,KAAK,AAC5D,EAA2C,EAAE,CACnD,EAFyE,EAEpE,IAAI,EAAI,IAAI,KAAK,GAAQ,GAAK,EAAK,EAAI,IAAI,KAAK,EAAE,OAAO,GAAK,KAAK,EAAc,CACpF,IAD6E,AACvE,EAAM,EAAI,GAChB,EAAO,IAAI,CAAC,CAAE,IAAK,EAAK,MAAO,EAAU,GAAG,CAAC,IAAQ,CAAE,EACzD,CAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,YACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,0DAMhD,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,OAAO,MAAM,UAAU,0CAC3B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,WAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,KAAK,SAAS,aAAc,EAAQ,UAAU,iDACpD,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,QAChB,CAAC,OAAO,cAAc,uBAAuB,oBAAoB,WAAW,SAAS,YAAY,CAAC,GAAG,CAAC,GACrG,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAe,MAAO,WAAI,EAAE,UAAU,CAAC,IAAI,MAA/B,UAInB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,YAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,KAAK,YAAY,aAAc,EAAW,UAAU,iDAC1D,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,QAChB,EAAa,GAAG,CAAE,AAAD,GAChB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,SAIvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,eAC/C,CAAA,EAAA,EAAA,IAAA,EAAC,SAAA,CAAO,KAAK,eAAe,aAAc,EAAc,UAAU,iDAChE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,MAAM,YAAG,QAChB,EAAgB,GAAG,CAAC,AAAC,GACpB,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAkB,MAAO,EAAE,EAAE,UAAG,EAAE,IAAI,EAA1B,EAAE,EAAE,SAIvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,SAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,OAAO,KAAK,OAAO,aAAc,EAAS,UAAU,4CAElE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,uCAA8B,OAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,KAAK,OAAO,KAAK,KAAK,aAAc,EAAO,UAAU,4CAE9D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iCACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,KAAK,SAAS,UAAU,mHAA0G,UAC1I,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,KAAK,qBAAqB,UAAU,gIAAuH,kBAMpK,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACZ,CAAC,CAAC,MAAM,OAAO,MAAM,CAAI,EAAE,CAAC,MAAM,MAAM,MAAM,CAAG,EAAE,CAAC,MAAM,WAAW,MAAM,CAAQ,EAAE,CAAC,MAAM,SAAS,MAAM,CAAM,EAAE,CAAC,MAAM,YAAY,MAAM,CAAS,EAAE,CAAC,MAAM,QAAQ,MAAM,CAAK,EAAE,CAAC,GAAG,CAAC,GACvL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAkB,UAAU,2CAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAE,KAAK,GAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAE,KAAK,KAFpC,EAAE,KAAK,KAQrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAe,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,wBAC5D,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,qBACX,EAAe,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,GAChE,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAoB,UAAU,kDAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mBAAW,EAAI,MAAM,CAAC,UAAU,CAAC,IAAI,OACpD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAI,MAAM,CAAC,IAAI,KAFhD,EAAI,MAAM,GAKM,IAA1B,EAAe,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qCAA4B,mBAK9E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAe,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,4BAC5D,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,qBACX,EAAgB,IAAI,CAAC,CAAC,EAAE,IAAK,CAAC,EAAY,GAAG,CAAC,EAAE,YAAY,EAAE,KAAK,EAAA,CAAE,CAAE,aAAa,CAAC,EAAY,GAAG,CAAC,EAAE,YAAY,EAAE,KAAK,KAAK,GAAG,CAAC,GAClI,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAoC,UAAU,kDAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mBAAW,EAAY,GAAG,CAAC,EAAI,YAAY,EAAI,KAAO,eACrE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAI,MAAM,CAAC,IAAI,KAFhD,EAAI,YAAY,EAAI,SAKH,IAA3B,EAAgB,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qCAA4B,mBAK/E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAe,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,yBAC5D,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,qBACX,EAAgB,IAAI,CAAC,CAAC,EAAE,IAAK,CAAC,EAAY,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,EAAA,CAAE,CAAE,aAAa,CAAC,EAAY,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,KAAK,GAAG,CAAC,GAC5H,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAiC,UAAU,kDAC1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mBAAW,EAAY,GAAG,CAAC,EAAI,SAAS,EAAI,KAAO,eAClE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAI,MAAM,CAAC,IAAI,KAFhD,EAAI,SAAS,EAAI,SAKA,IAA3B,EAAgB,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qCAA4B,mBAK/E,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAe,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,gCAC5D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,eACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wEACZ,EAAO,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,GACrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAiB,UAAU,2EAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAM,EAAG,GAAG,GACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,yBAAiB,EAAG,KAAK,KAFjC,EAAG,GAAG,GAKC,IAAlB,EAAO,MAAM,EACZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,2CAQvD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}