(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return s},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return a}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return s}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",s=e.pathname||"",i=e.hash||"",u=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==l?(l="//"+(l||""),s&&"/"!==s[0]&&(s="/"+s)):l||(l=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+a+l+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return b},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return h},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return l},getLocationOrigin:function(){return i},getURL:function(){return u},isAbsoluteUrl:function(){return s},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>a.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=i();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,h=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=e.r(18967),o=e.r(52817);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return g},useLinkStatus:function(){return b}});let n=e.r(90809),o=e.r(43476),a=n._(e.r(71645)),s=e.r(95057),i=e.r(8372),u=e.r(18581),l=e.r(18967),c=e.r(5550);e.r(33525);let d=e.r(91949),f=e.r(73668),p=e.r(99781);e.r(84508);let h=e.r(65165);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){var t;let r,n,s,[g,b]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),x=(0,a.useRef)(null),{href:v,as:j,children:N,prefetch:P=null,passHref:w,replace:C,shallow:O,scroll:S,onClick:_,onMouseEnter:E,onTouchStart:T,legacyBehavior:R=!1,onNavigate:A,ref:M,unstable_dynamicOnHover:k,...I}=e;r=N,R&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let L=a.default.useContext(i.AppRouterContext),U=!1!==P,F=!1!==P?null===(t=P)||"auto"===t?h.FetchStrategy.PPR:h.FetchStrategy.Full:h.FetchStrategy.PPR,{href:D,as:B}=a.default.useMemo(()=>{let e=m(v);return{href:e,as:j?m(j):e}},[v,j]);R&&(n=a.default.Children.only(r));let K=R?n&&"object"==typeof n&&n.ref:M,q=a.default.useCallback(e=>(null!==L&&(x.current=(0,d.mountLinkInstance)(e,D,L,F,U,b)),()=>{x.current&&((0,d.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,d.unmountPrefetchableInstance)(e)}),[U,D,L,F,b]),z={ref:(0,u.useMergedRef)(q,K),onClick(e){R||"function"!=typeof _||_(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,o,s,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==s||s,n.current)})}}(e,D,B,x,C,S,A))},onMouseEnter(e){R||"function"!=typeof E||E(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===k)},onTouchStart:function(e){R||"function"!=typeof T||T(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===k)}};return(0,l.isAbsoluteUrl)(B)?z.href=B:R&&!w&&("a"!==n.type||"href"in n.props)||(z.href=(0,c.addBasePath)(B)),s=R?a.default.cloneElement(n,z):(0,o.jsx)("a",{...I,...z,children:r}),(0,o.jsx)(y.Provider,{value:g,children:s})}let y=(0,a.createContext)(d.IDLE_LINK_STATUS),b=()=>(0,a.useContext)(y);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},67881,e=>{"use strict";e.s(["Button",()=>o]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let o=r.forwardRef((e,r)=>{let{className:o,variant:a="default",size:s="default",...i}=e;return(0,t.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[a],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[s],o),ref:r,...i})});o.displayName="Button"},23750,e=>{"use strict";e.s(["Input",()=>o]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let o=r.forwardRef((e,r)=>{let{className:o,type:a,...s}=e;return(0,t.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o),ref:r,...s})});o.displayName="Input"},18566,(e,t,r)=>{t.exports=e.r(76562)},70065,e=>{"use strict";e.s(["Card",()=>o,"CardContent",()=>u,"CardDescription",()=>i,"CardHeader",()=>a,"CardTitle",()=>s]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let o=r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",o),...a})});o.displayName="Card";let a=r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",o),...a})});a.displayName="CardHeader";let s=r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",o),...a})});s.displayName="CardTitle";let i=r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-gray-600",o),...a})});i.displayName="CardDescription";let u=r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",o),...a})});u.displayName="CardContent",r.forwardRef((e,r)=>{let{className:o,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",o),...a})}).displayName="CardFooter"},45301,e=>{"use strict";e.s(["default",()=>u]);var t=e.i(43476),r=e.i(71645),n=e.i(18566),o=e.i(22016),a=e.i(67881),s=e.i(23750),i=e.i(70065);function u(){let[e,u]=(0,r.useState)(""),[l,c]=(0,r.useState)(""),[d,f]=(0,r.useState)(!1),[p,h]=(0,r.useState)(""),m=(0,n.useRouter)(),g=async t=>{t.preventDefault(),f(!0),h("");try{let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:l})}),r=await t.json();if(!t.ok)return void h(r.error||"Login failed");m.push("/dashboard"),m.refresh()}catch(e){h("An unexpected error occurred")}finally{f(!1)}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Access your ticketing portal"}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md",children:[(0,t.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,t.jsx)("strong",{children:"Demo Mode:"})," Use <EMAIL> / demo123 to login"]}),(0,t.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"To enable full functionality, set up your Supabase project and update the environment variables"})]})]}),(0,t.jsxs)(i.Card,{children:[(0,t.jsxs)(i.CardHeader,{children:[(0,t.jsx)(i.CardTitle,{children:"Welcome back"}),(0,t.jsx)(i.CardDescription,{children:"Enter your credentials to access your account"})]}),(0,t.jsx)(i.CardContent,{children:(0,t.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[p&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:p}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,t.jsx)(s.Input,{id:"email",type:"email",value:e,onChange:e=>u(e.target.value),required:!0,className:"mt-1",placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsx)(s.Input,{id:"password",type:"password",value:l,onChange:e=>c(e.target.value),required:!0,className:"mt-1",placeholder:"Enter your password"})]}),(0,t.jsx)(a.Button,{type:"submit",disabled:d,className:"w-full",children:d?"Signing in...":"Sign in"}),(0,t.jsxs)("div",{className:"text-center space-y-2",children:[(0,t.jsx)(o.default,{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot your password?"}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(o.default,{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500",children:"Sign up"})]})]})]})})]})]})})}}]);