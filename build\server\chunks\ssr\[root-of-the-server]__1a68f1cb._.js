module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},42602,(a,b,c)=>{"use strict";b.exports=a.r(18622)},87924,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactJsxRuntime},72131,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].React},9270,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.AppRouterContext},36313,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.HooksClientContext},18341,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored.contexts.ServerInsertedHtml},38783,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactServerDOMTurbopackClient},35112,(a,b,c)=>{"use strict";b.exports=a.r(42602).vendored["react-ssr"].ReactDOM},51234,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HandleISRError",{enumerable:!0,get:function(){return e}});let d=a.r(56704).workAsyncStorage;function e(a){let{error:b}=a;if(d){let a=d.getStore();if((null==a?void 0:a.isRevalidate)||(null==a?void 0:a.isStaticGeneration))throw console.error(b),b}return null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40622,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return g}});let d=a.r(87924),e=a.r(51234),f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}},g=function(a){let{error:b}=a,c=null==b?void 0:b.digest;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.HandleISRError,{error:b}),(0,d.jsx)("div",{style:f.error,children:(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{style:f.text,children:["Application error: a ",c?"server":"client","-side exception has occurred while loading ",window.location.hostname," (see the"," ",c?"server logs":"browser console"," for more information)."]}),c?(0,d.jsx)("p",{style:f.text,children:"Digest: "+c}):null]})})]})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},50944,(a,b,c)=>{b.exports=a.r(74137)},1615,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(87924),c=a.i(50944);function d({className:a=""}){let d=(0,c.useRouter)(),e=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch{}d.push("/auth/login"),d.refresh()};return(0,b.jsx)("button",{onClick:e,className:a,children:"Sign out"})}},2365,a=>{"use strict";a.s(["Sidebar",()=>m],2365);var b=a.i(87924),c=a.i(38246),d=a.i(50944),e=a.i(97895),f=a.i(72131);let g=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}),h=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"}))}),i=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),j=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),k=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}),l=f.forwardRef(function({title:a,titleId:b,...c},d){return f.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?f.createElement("title",{id:b},a):null,f.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});function m({user:a,profile:f}){let m=(0,d.usePathname)(),n=(a?.role||f?.role||"").toString().toLowerCase(),o=a?.fullName||f?.full_name||a?.email||f?.email||"User",p=[{name:"Dashboard",href:"/dashboard",icon:g},{name:"Tickets",href:"/dashboard/tickets",icon:h},{name:"New Ticket",href:"/dashboard/tickets/new",icon:l}];return("admin"===n||"agent"===n||"manager"===n)&&p.push({name:"Reports",href:"/dashboard/reports",icon:j}),"admin"===n&&p.push({name:"Users",href:"/dashboard/users",icon:i}),"admin"===n&&(p.push({name:"Departments",href:"/dashboard/departments",icon:k}),p.push({name:"Email configuration",href:"/dashboard/email-config",icon:k})),(0,b.jsx)("aside",{className:"hidden md:flex md:w-64 md:flex-col",children:(0,b.jsxs)("div",{className:"flex flex-col flex-grow pt-5 bg-slate-900 text-slate-200 border-r border-slate-800 overflow-y-auto",children:[(0,b.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,b.jsx)("h1",{className:"text-xl font-semibold text-white",children:"WeCare"})}),(0,b.jsx)("div",{className:"mt-6 flex-grow flex flex-col",children:(0,b.jsx)("nav",{className:"flex-1 px-2 space-y-1",children:p.map(a=>{let d=m===a.href;return(0,b.jsxs)(c.default,{href:a.href,className:(0,e.cn)(d?"bg-slate-800 border-blue-500 text-white":"border-transparent text-slate-300 hover:bg-slate-800 hover:text-white","group flex items-center px-2 py-2 text-sm font-medium border-l-4 rounded-r-md"),children:[(0,b.jsx)(a.icon,{className:(0,e.cn)(d?"text-blue-400":"text-slate-400 group-hover:text-slate-300","mr-3 h-5 w-5"),"aria-hidden":"true"}),a.name]},a.name)})})}),(0,b.jsx)("div",{className:"flex-shrink-0 p-4 border-t border-slate-800",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-sm font-medium text-white",children:(o||"").charAt(0)||"U"})})}),(0,b.jsxs)("div",{className:"ml-3",children:[(0,b.jsx)("p",{className:"text-sm font-medium text-slate-100",children:o}),n&&(0,b.jsx)("p",{className:"text-xs text-slate-400 capitalize",children:n})]})]})})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__1a68f1cb._.js.map