module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(36984);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},15843,a=>{a.n(a.i(18357))},99026,(a,b,c)=>{},41805,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/dashboard/tickets/[id]/MessageComposer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/dashboard/tickets/[id]/MessageComposer.tsx <module evaluation>","default")},41679,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/dashboard/tickets/[id]/MessageComposer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/dashboard/tickets/[id]/MessageComposer.tsx","default")},13227,a=>{"use strict";a.i(41805);var b=a.i(41679);a.n(b)},67135,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/dashboard/tickets/[id]/TicketControls.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/dashboard/tickets/[id]/TicketControls.tsx <module evaluation>","default")},21163,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(11857).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/src/app/dashboard/tickets/[id]/TicketControls.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/src/app/dashboard/tickets/[id]/TicketControls.tsx","default")},42043,a=>{"use strict";a.i(67135);var b=a.i(21163);a.n(b)},87145,a=>{"use strict";a.s(["default",()=>i]);var b=a.i(7997),c=a.i(5246),d=a.i(97647),e=a.i(31191),f=a.i(14929),g=a.i(13227),h=a.i(42043);async function i({params:a}){let i=await (0,c.cookies)(),j=i.get("auth-token")?.value,k=j?(0,f.verifyToken)(j):null;if(!k)return(0,b.jsx)("div",{className:"p-6",children:"Unauthorized"});let l=await e.prisma.ticket.findUnique({where:{id:a.id},include:{createdBy:{select:{fullName:!0,email:!0}},assignedTo:{select:{id:!0,fullName:!0,email:!0}},project:{select:{id:!0,name:!0}},department:{select:{id:!0,name:!0}},messages:{include:{author:{select:{fullName:!0,email:!0}}},orderBy:{createdAt:"asc"}},attachments:!0}});if(!l||l.organizationId!==k.organizationId||l.project?.id&&!await e.prisma.userProject.findUnique({where:{userId_projectId:{userId:k.id,projectId:l.project.id}}})&&"ADMIN"!==k.role||l.department?.id&&!await e.prisma.userDepartment.findUnique({where:{userId_departmentId:{userId:k.id,departmentId:l.department.id}}})&&"ADMIN"!==k.role)return(0,b.jsx)("div",{className:"p-6",children:"Not found"});let m=l.messages.filter(a=>!a.isInternal),n=l.messages.filter(a=>a.isInternal&&a.content.startsWith("[AUDIT]"));return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:l.ticketNumber}),(0,b.jsx)("p",{className:"text-gray-600",children:l.title})]}),(0,b.jsx)(d.default,{href:"/dashboard/tickets",className:"text-blue-600 hover:underline",children:"Back to Tickets"})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,b.jsx)("div",{className:"lg:col-span-2 space-y-6",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Conversation"})}),(0,b.jsxs)("div",{className:"p-4 space-y-4",children:[m.map(a=>(0,b.jsxs)("div",{className:"p-3 bg-gray-50 rounded",children:[(0,b.jsxs)("div",{className:"text-sm text-gray-500",children:[a.author.fullName||a.author.email," • ",new Date(a.createdAt).toLocaleString()]}),(0,b.jsx)("div",{className:"mt-1 text-sm text-gray-800 whitespace-pre-wrap",children:a.content})]},a.id)),0===m.length&&(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"No messages yet."})]}),(0,b.jsx)("div",{className:"p-4 border-t",children:(0,b.jsx)(g.default,{ticketId:l.id})})]})}),(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"Details"}),(0,b.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Status:"})," ",l.status]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Priority:"})," ",l.priority]}),l.project&&(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Project:"})," ",l.project.name]}),l.department&&(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Department:"})," ",l.department.name]}),l.category&&(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Category:"})," ",l.category]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Created:"})," ",new Date(l.createdAt).toLocaleString()]}),l.assignedTo&&(0,b.jsxs)("div",{children:[(0,b.jsx)("strong",{children:"Assigned To:"})," ",l.assignedTo.fullName||l.assignedTo.email]})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"Update"}),(0,b.jsx)(h.default,{ticketId:l.id,role:k.role,initial:{status:l.status,priority:l.priority,departmentId:l.department?.id||"",assignedToId:l.assignedTo?.id||"",projectId:l.project?.id||""}})]}),l.attachments.length>0&&(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"Attachments"}),(0,b.jsx)("div",{className:"grid grid-cols-2 gap-3",children:l.attachments.map(a=>(0,b.jsx)("a",{href:a.url,target:"_blank",className:"block",children:(0,b.jsx)("img",{src:a.url,alt:a.originalName,className:"w-full h-32 object-cover rounded border"})},a.id))})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3",children:"Lifecycle"}),(0,b.jsxs)("div",{className:"space-y-2",children:[n.map(a=>(0,b.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,b.jsx)("div",{children:a.content.replace(/^\[AUDIT\]\s*/,"")}),(0,b.jsxs)("div",{className:"text-xs text-gray-400",children:[new Date(a.createdAt).toLocaleString()," • ",a.author.fullName||a.author.email]})]},a.id)),0===n.length&&(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"No lifecycle entries yet."})]})]})]})]})]})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__fdc5374f._.js.map