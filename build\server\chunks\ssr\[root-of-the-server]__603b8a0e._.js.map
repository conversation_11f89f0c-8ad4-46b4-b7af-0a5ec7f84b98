{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/dist/src/server/instrumentation/utils.ts", "turbopack:///[project]/node_modules/next/dist/esm/server/web/spec-extension/adapters/headers.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/index.js", "turbopack:///[project]/node_modules/next/dist/esm/server/base-http/node.js", "turbopack:///[project]/node_modules/next/dist/esm/server/api-utils/get-cookie-parser.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js", "turbopack:///[project]/node_modules/next/dist/esm/shared/lib/page-path/ensure-leading-slash.js", "turbopack:///[project]/node_modules/next/dist/src/server/lib/cache-control.ts", "turbopack:///[project]/lib/permissions.ts", "turbopack:///[project]/src/app/dashboard/page.tsx"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n", "import { ReflectAdapter } from './reflect';\n/**\n * @internal\n */ export class ReadonlyHeadersError extends Error {\n    constructor(){\n        super('Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers');\n    }\n    static callable() {\n        throw new ReadonlyHeadersError();\n    }\n}\nexport class HeadersAdapter extends Headers {\n    constructor(headers){\n        // We've already overridden the methods that would be called, so we're just\n        // calling the super constructor to ensure that the instanceof check works.\n        super();\n        this.headers = new Proxy(headers, {\n            get (target, prop, receiver) {\n                // Because this is just an object, we expect that all \"get\" operations\n                // are for properties. If it's a \"get\" for a symbol, we'll just return\n                // the symbol.\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.get(target, prop, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return undefined.\n                if (typeof original === 'undefined') return;\n                // If the original casing exists, return the value.\n                return ReflectAdapter.get(target, original, receiver);\n            },\n            set (target, prop, value, receiver) {\n                if (typeof prop === 'symbol') {\n                    return ReflectAdapter.set(target, prop, value, receiver);\n                }\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, use the prop as the key.\n                return ReflectAdapter.set(target, original ?? prop, value, receiver);\n            },\n            has (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return false.\n                if (typeof original === 'undefined') return false;\n                // If the original casing exists, return true.\n                return ReflectAdapter.has(target, original);\n            },\n            deleteProperty (target, prop) {\n                if (typeof prop === 'symbol') return ReflectAdapter.deleteProperty(target, prop);\n                const lowercased = prop.toLowerCase();\n                // Let's find the original casing of the key. This assumes that there is\n                // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n                // headers object.\n                const original = Object.keys(headers).find((o)=>o.toLowerCase() === lowercased);\n                // If the original casing doesn't exist, return true.\n                if (typeof original === 'undefined') return true;\n                // If the original casing exists, delete the property.\n                return ReflectAdapter.deleteProperty(target, original);\n            }\n        });\n    }\n    /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */ static seal(headers) {\n        return new Proxy(headers, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'append':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyHeadersError.callable;\n                    default:\n                        return ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n    /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */ merge(value) {\n        if (Array.isArray(value)) return value.join(', ');\n        return value;\n    }\n    /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */ static from(headers) {\n        if (headers instanceof Headers) return headers;\n        return new HeadersAdapter(headers);\n    }\n    append(name, value) {\n        const existing = this.headers[name];\n        if (typeof existing === 'string') {\n            this.headers[name] = [\n                existing,\n                value\n            ];\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            this.headers[name] = value;\n        }\n    }\n    delete(name) {\n        delete this.headers[name];\n    }\n    get(name) {\n        const value = this.headers[name];\n        if (typeof value !== 'undefined') return this.merge(value);\n        return null;\n    }\n    has(name) {\n        return typeof this.headers[name] !== 'undefined';\n    }\n    set(name, value) {\n        this.headers[name] = value;\n    }\n    forEach(callbackfn, thisArg) {\n        for (const [name, value] of this.entries()){\n            callbackfn.call(thisArg, value, name, this);\n        }\n    }\n    *entries() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(name);\n            yield [\n                name,\n                value\n            ];\n        }\n    }\n    *keys() {\n        for (const key of Object.keys(this.headers)){\n            const name = key.toLowerCase();\n            yield name;\n        }\n    }\n    *values() {\n        for (const key of Object.keys(this.headers)){\n            // We assert here that this is a string because we got it from the\n            // Object.keys() call above.\n            const value = this.get(key);\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n}\n\n//# sourceMappingURL=headers.js.map", "import { RedirectStatusCode } from '../../client/components/redirect-status-code';\nimport { getCookieParser } from '../api-utils/get-cookie-parser';\nexport class BaseNextRequest {\n    constructor(method, url, body){\n        this.method = method;\n        this.url = url;\n        this.body = body;\n    }\n    // Utils implemented using the abstract methods above\n    get cookies() {\n        if (this._cookies) return this._cookies;\n        return this._cookies = getCookieParser(this.headers)();\n    }\n}\nexport class BaseNextResponse {\n    constructor(destination){\n        this.destination = destination;\n    }\n    // Utils implemented using the abstract methods above\n    redirect(destination, statusCode) {\n        this.setHeader('Location', destination);\n        this.statusCode = statusCode;\n        // Since IE11 doesn't support the 308 header add backwards\n        // compatibility using refresh header\n        if (statusCode === RedirectStatusCode.PermanentRedirect) {\n            this.setHeader('Refresh', `0;url=${destination}`);\n        }\n        return this;\n    }\n}\n\n//# sourceMappingURL=index.js.map", "import { HeadersAdapter } from '../web/spec-extension/adapters/headers';\nimport { PRERENDER_REVALIDATE_HEADER, PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER } from '../../lib/constants';\nimport { getTracer } from '../lib/trace/tracer';\nimport { NodeSpan } from '../lib/trace/constants';\nexport function wrapApiHandler(page, handler) {\n    return (...args)=>{\n        getTracer().setRootSpanAttribute('next.route', page);\n        // Call API route method\n        return getTracer().trace(NodeSpan.runHandler, {\n            spanName: `executing api route (pages) ${page}`\n        }, ()=>handler(...args));\n    };\n}\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */ export function sendStatusCode(res, statusCode) {\n    res.statusCode = statusCode;\n    return res;\n}\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */ export function redirect(res, statusOrUrl, url) {\n    if (typeof statusOrUrl === 'string') {\n        url = statusOrUrl;\n        statusOrUrl = 307;\n    }\n    if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n        throw Object.defineProperty(new Error(`Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`), \"__NEXT_ERROR_CODE\", {\n            value: \"E389\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    res.writeHead(statusOrUrl, {\n        Location: url\n    });\n    res.write(url);\n    res.end();\n    return res;\n}\nexport function checkIsOnDemandRevalidate(req, previewProps) {\n    const headers = HeadersAdapter.from(req.headers);\n    const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER);\n    const isOnDemandRevalidate = previewModeId === previewProps.previewModeId;\n    const revalidateOnlyGenerated = headers.has(PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER);\n    return {\n        isOnDemandRevalidate,\n        revalidateOnlyGenerated\n    };\n}\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`;\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`;\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024;\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA);\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS);\nexport function clearPreviewData(res, options = {}) {\n    if (SYMBOL_CLEARED_COOKIES in res) {\n        return res;\n    }\n    const { serialize } = require('next/dist/compiled/cookie');\n    const previous = res.getHeader('Set-Cookie');\n    res.setHeader(`Set-Cookie`, [\n        ...typeof previous === 'string' ? [\n            previous\n        ] : Array.isArray(previous) ? previous : [],\n        serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        }),\n        serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n            // To delete a cookie, set `expires` to a date in the past:\n            // https://tools.ietf.org/html/rfc6265#section-4.1.1\n            // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n            expires: new Date(0),\n            httpOnly: true,\n            sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n            secure: process.env.NODE_ENV !== 'development',\n            path: '/',\n            ...options.path !== undefined ? {\n                path: options.path\n            } : undefined\n        })\n    ]);\n    Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n        value: true,\n        enumerable: false\n    });\n    return res;\n}\n/**\n * Custom error class\n */ export class ApiError extends Error {\n    constructor(statusCode, message){\n        super(message);\n        this.statusCode = statusCode;\n    }\n}\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */ export function sendError(res, statusCode, message) {\n    res.statusCode = statusCode;\n    res.statusMessage = message;\n    res.end(message);\n}\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */ export function setLazyProp({ req }, prop, getter) {\n    const opts = {\n        configurable: true,\n        enumerable: true\n    };\n    const optsReset = {\n        ...opts,\n        writable: true\n    };\n    Object.defineProperty(req, prop, {\n        ...opts,\n        get: ()=>{\n            const value = getter();\n            // we set the property on the object to avoid recalculating it\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n            return value;\n        },\n        set: (value)=>{\n            Object.defineProperty(req, prop, {\n                ...optsReset,\n                value\n            });\n        }\n    });\n}\n\n//# sourceMappingURL=index.js.map", "import { SYMBOL_CLEARED_COOKIES } from '../api-utils';\nimport { NEXT_REQUEST_META } from '../request-meta';\nimport { BaseNextRequest, BaseNextResponse } from './index';\nlet prop;\nexport class NodeNextRequest extends BaseNextRequest {\n    static #_ = prop = _NEXT_REQUEST_META = NEXT_REQUEST_META;\n    constructor(_req){\n        var _this__req;\n        super(_req.method.toUpperCase(), _req.url, _req), this._req = _req, this.headers = this._req.headers, this.fetchMetrics = (_this__req = this._req) == null ? void 0 : _this__req.fetchMetrics, this[_NEXT_REQUEST_META] = this._req[NEXT_REQUEST_META] || {}, this.streaming = false;\n    }\n    get originalRequest() {\n        // Need to mimic these changes to the original req object for places where we use it:\n        // render.tsx, api/ssg requests\n        this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META];\n        this._req.url = this.url;\n        this._req.cookies = this.cookies;\n        return this._req;\n    }\n    set originalRequest(value) {\n        this._req = value;\n    }\n    /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */ stream() {\n        if (this.streaming) {\n            throw Object.defineProperty(new Error('Invariant: NodeNextRequest.stream() can only be called once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E467\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        this.streaming = true;\n        return new ReadableStream({\n            start: (controller)=>{\n                this._req.on('data', (chunk)=>{\n                    controller.enqueue(new Uint8Array(chunk));\n                });\n                this._req.on('end', ()=>{\n                    controller.close();\n                });\n                this._req.on('error', (err)=>{\n                    controller.error(err);\n                });\n            }\n        });\n    }\n}\nexport class NodeNextResponse extends BaseNextResponse {\n    get originalResponse() {\n        if (SYMBOL_CLEARED_COOKIES in this) {\n            this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES];\n        }\n        return this._res;\n    }\n    constructor(_res){\n        super(_res), this._res = _res, this.textBody = undefined;\n    }\n    get sent() {\n        return this._res.finished || this._res.headersSent;\n    }\n    get statusCode() {\n        return this._res.statusCode;\n    }\n    set statusCode(value) {\n        this._res.statusCode = value;\n    }\n    get statusMessage() {\n        return this._res.statusMessage;\n    }\n    set statusMessage(value) {\n        this._res.statusMessage = value;\n    }\n    setHeader(name, value) {\n        this._res.setHeader(name, value);\n        return this;\n    }\n    removeHeader(name) {\n        this._res.removeHeader(name);\n        return this;\n    }\n    getHeaderValues(name) {\n        const values = this._res.getHeader(name);\n        if (values === undefined) return undefined;\n        return (Array.isArray(values) ? values : [\n            values\n        ]).map((value)=>value.toString());\n    }\n    hasHeader(name) {\n        return this._res.hasHeader(name);\n    }\n    getHeader(name) {\n        const values = this.getHeaderValues(name);\n        return Array.isArray(values) ? values.join(',') : undefined;\n    }\n    getHeaders() {\n        return this._res.getHeaders();\n    }\n    appendHeader(name, value) {\n        const currentValues = this.getHeaderValues(name) ?? [];\n        if (!currentValues.includes(value)) {\n            this._res.setHeader(name, [\n                ...currentValues,\n                value\n            ]);\n        }\n        return this;\n    }\n    body(value) {\n        this.textBody = value;\n        return this;\n    }\n    send() {\n        this._res.end(this.textBody);\n    }\n    onClose(callback) {\n        this.originalResponse.on('close', callback);\n    }\n}\nvar _NEXT_REQUEST_META;\n\n//# sourceMappingURL=node.js.map", "/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */ export function getCookieParser(headers) {\n    return function parseCookie() {\n        const { cookie } = headers;\n        if (!cookie) {\n            return {};\n        }\n        const { parse: parseCookieFn } = require('next/dist/compiled/cookie');\n        return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie);\n    };\n}\n\n//# sourceMappingURL=get-cookie-parser.js.map", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash';\nimport { isGroupSegment } from '../../segment';\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */ export function normalizeAppPath(route) {\n    return ensureLeadingSlash(route.split('/').reduce((pathname, segment, index, segments)=>{\n        // Empty segments are ignored.\n        if (!segment) {\n            return pathname;\n        }\n        // Groups are ignored.\n        if (isGroupSegment(segment)) {\n            return pathname;\n        }\n        // Parallel segments are ignored.\n        if (segment[0] === '@') {\n            return pathname;\n        }\n        // The last segment (if it's a leaf) should be ignored.\n        if ((segment === 'page' || segment === 'route') && index === segments.length - 1) {\n            return pathname;\n        }\n        return pathname + \"/\" + segment;\n    }, ''));\n}\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */ export function normalizeRscURL(url) {\n    return url.replace(/\\.rsc($|\\?)/, // $1 ensures `?` is preserved\n    '$1');\n}\n\n//# sourceMappingURL=app-paths.js.map", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */ export function ensureLeadingSlash(path) {\n    return path.startsWith('/') ? path : \"/\" + path;\n}\n\n//# sourceMappingURL=ensure-leading-slash.js.map", "import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n", "import { prisma } from '@/lib/prisma'\n\nexport type PermissionKey =\n  | 'DASHBOARD_VIEW'\n  | 'TICKETS_VIEW'\n  | 'TICKETS_UPDATE'\n  | 'TICKETS_ASSIGN'\n  | 'REPORTS_VIEW'\n  | 'DEPARTMENTS_MANAGE'\n  | 'PROJECTS_MANAGE'\n  | 'AUDIT_VIEW'\n\nexport const ALL_PERMISSIONS: PermissionKey[] = [\n  'DASHBOARD_VIEW',\n  'TICKETS_VIEW',\n  'TICKETS_UPDATE',\n  'TICKETS_ASSIGN',\n  'REPORTS_VIEW',\n  'DEPARTMENTS_MANAGE',\n  'PROJECTS_MANAGE',\n  'AUDIT_VIEW',\n]\n\nexport type AuthUserLite = {\n  id: string\n  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'\n  organizationId: string\n}\n\nexport async function getUserPermissions(user: AuthUserLite): Promise<Set<PermissionKey>> {\n  // Admins implicitly have all permissions\n  if (user.role === 'ADMIN') return new Set<PermissionKey>(ALL_PERMISSIONS)\n\n  // Everyone else: only what Admin explicitly assigned\n  const rows = await prisma.userPermission.findMany({\n    where: { userId: user.id, organizationId: user.organizationId },\n    select: { permission: true },\n  })\n  return new Set(rows.map(r => r.permission as PermissionKey))\n}\n\nexport function hasPermission(perms: Set<PermissionKey>, key: PermissionKey) {\n  return perms.has(key)\n}\n\n", "import { cookies } from 'next/headers'\nimport { redirect } from 'next/navigation'\nimport { prisma } from '@/lib/prisma'\nimport { verifyToken } from '@/lib/auth-new'\nimport { getUserPermissions, hasPermission } from '@/lib/permissions'\n\n\nfunction hours(n: number) {\n  return Math.round(n * 10) / 10\n}\n\nexport default async function DashboardPage() {\n  const cookieStore = await cookies()\n  const token = cookieStore.get('auth-token')?.value\n  const user = token ? verifyToken(token) : null\n  if (!user) redirect('/auth/login')\n\n  // Permission: non-admins must have TICKETS_VIEW to see dashboard data\n  if (user.role !== 'ADMIN') {\n    const perms = await getUserPermissions({ id: user.id, role: user.role, organizationId: user.organizationId })\n    if (!hasPermission(perms, 'TICKETS_VIEW')) {\n      return (\n        <div className=\"min-h-screen bg-gray-50 p-6\">\n          <div className=\"max-w-3xl mx-auto bg-white rounded-lg shadow p-6\">Forbidden</div>\n        </div>\n      )\n    }\n  }\n\n  // Scope by both projects and departments for non-admins\n  const memberships = await prisma.userProject.findMany({ where: { userId: user.id }, select: { projectId: true } })\n  const projectIds = memberships.map(m => m.projectId)\n  const deptLinks = await prisma.userDepartment.findMany({ where: { userId: user.id }, select: { departmentId: true } })\n  const departmentIds = deptLinks.map(d => d.departmentId)\n\n  const baseWhere: any = user.role === 'ADMIN'\n    ? { organizationId: user.organizationId }\n    : {\n        organizationId: user.organizationId,\n        projectId: { in: projectIds.length ? projectIds : ['__none__'] },\n        departmentId: { in: departmentIds.length ? departmentIds : ['__none__'] },\n      }\n\n  const [total, open, inProgress, closed, cancelled, resolved, recent] = await Promise.all([\n    prisma.ticket.count({ where: baseWhere }),\n    prisma.ticket.count({ where: { ...baseWhere, status: 'OPEN' } }),\n    prisma.ticket.count({ where: { ...baseWhere, status: 'IN_PROGRESS' } }),\n    prisma.ticket.count({ where: { ...baseWhere, status: 'CLOSED' } }),\n    prisma.ticket.count({ where: { ...baseWhere, status: 'CANCELLED' } }),\n    prisma.ticket.count({ where: { ...baseWhere, status: 'RESOLVED' } }),\n    prisma.ticket.findMany({\n      where: baseWhere,\n      include: { createdBy: { select: { fullName: true, email: true } } },\n      orderBy: { createdAt: 'desc' },\n      take: 5,\n    }),\n  ])\n\n  // TAT metrics\n  const [resolvedSamples, openSamples] = await Promise.all([\n    prisma.ticket.findMany({\n      where: { ...baseWhere, OR: [{ status: 'RESOLVED' }, { status: 'CLOSED' }], resolvedAt: { not: null } },\n      select: { createdAt: true, resolvedAt: true },\n      orderBy: { resolvedAt: 'desc' },\n      take: 200,\n    }),\n    prisma.ticket.findMany({\n      where: { ...baseWhere, status: { in: ['OPEN', 'IN_PROGRESS', 'WAITING_FOR_CUSTOMER', 'WAITING_FOR_AGENT'] } },\n      select: { createdAt: true },\n      orderBy: { createdAt: 'desc' },\n      take: 200,\n    }),\n  ])\n\n  const resolvedHours: number[] = resolvedSamples\n    .map((t) => ((t.resolvedAt!.getTime() - t.createdAt.getTime()) / 36e5))\n    .filter((n: number) => n >= 0)\n    .sort((a: number, b: number) => a - b)\n  const avgResolvedH = resolvedHours.length ? hours(resolvedHours.reduce((a: number, b: number) => a + b, 0) / resolvedHours.length) : 0\n  const medResolvedH = resolvedHours.length ? hours(resolvedHours[Math.floor(resolvedHours.length / 2)]) : 0\n\n  const now = Date.now()\n  const openAgesH: number[] = openSamples\n    .map((t: { createdAt: Date }) => ((now - t.createdAt.getTime()) / 36e5))\n    .filter((n: number) => n >= 0)\n  const avgOpenAgeH = openAgesH.length ? hours(openAgesH.reduce((a: number, b: number) => a + b, 0) / openAgesH.length) : 0\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">Welcome back, {user.fullName || user.email}</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">Here is the latest on your tickets.</p>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n          {[{label:'Open',value:open},{label:'WIP',value:inProgress},{label:'Closed',value:closed},{label:'Discarded',value:cancelled},{label:'Total',value:total}].map(s=> (\n            <div key={s.label} className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"text-sm text-gray-500\">{s.label}</div>\n              <div className=\"text-2xl font-bold\">{s.value}</div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"text-sm text-gray-500\">TAT (Resolved/Closed)</div>\n            <div className=\"text-2xl font-bold\">{avgResolvedH}h</div>\n            <div className=\"text-xs text-gray-500\">Median: {medResolvedH}h</div>\n          </div>\n          <div className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"text-sm text-gray-500\">Average age (Open/WIP)</div>\n            <div className=\"text-2xl font-bold\">{avgOpenAgeH}h</div>\n            <div className=\"text-xs text-gray-500\">Active: {openSamples.length}</div>\n          </div>\n          <div className=\"bg-white rounded-lg shadow p-4\">\n            <div className=\"text-sm text-gray-500\">Resolved (count)</div>\n            <div className=\"text-2xl font-bold\">{resolved}</div>\n            <div className=\"text-xs text-gray-500\">Last {resolvedSamples.length} used for TAT</div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-4 border-b\"><h2 className=\"font-semibold\">Recent Tickets</h2></div>\n          <ul className=\"divide-y\">\n            {recent.length === 0 ? (\n              <li className=\"p-4 text-sm text-gray-500\">No recent tickets in your departments.</li>\n            ) : recent.map((t: any) => (\n              <li key={t.id} className=\"p-4 flex items-center justify-between\">\n                <div>\n                  <div className=\"font-medium text-blue-700\">{t.ticketNumber}</div>\n                  <div className=\"text-sm text-gray-600\">{t.title}</div>\n                  <div className=\"text-xs text-gray-500\">{t.createdBy.fullName || t.createdBy.email}</div>\n                </div>\n                <span className=\"text-xs rounded-full px-2 py-1 bg-gray-100 text-gray-700\">{t.status}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined", "CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>"], "mappings": "8LAAO,SAASA,EAAoBC,CAGnC,SACKA,AAAJ,EAAWC,oBAAoB,CACtB,CADwB,WAG7BD,EAAOE,YAAY,CACd,CADgB,aAI3B,0HCXA,IG0HI,EH1HJ,EAAA,EAAA,CAAA,CAAA,MAGW,MGyHX,CHzHiB,UAA6B,MAC1C,aAAa,CACT,CGuHwB,IHvHnB,CAAC,qGACV,CACA,OAAO,UAAW,CACd,MAAM,IAAI,CACd,CACJ,CACO,MAAM,UAAuB,QAChC,YAAY,CAAO,CAAC,CAGhB,KAAK,GACL,IAAI,CAAC,OAAO,CAAG,IAAI,MAAM,EAAS,CAC9B,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EAIvB,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,GAE5C,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,GAAI,KAAoB,IAAb,EAEX,OAFqC,AAE9B,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAU,EAChD,EACA,IAAK,CAAM,CAAE,CAAI,CAAE,CAAK,CAAE,CAAQ,EAC9B,GAAoB,UAAhB,AAA0B,OAAnB,EACP,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAAO,GAEnD,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,GAEpE,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAAY,EAAM,EAAO,EAC/D,EACA,IAAK,CAAM,CAAE,CAAI,EACb,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,GAChE,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,IAAI,CAAoB,IAAb,GAEJ,EAAA,IAF8B,OAAO,GAEvB,CAAC,GAAG,CAAC,EAAQ,EACtC,EACA,eAAgB,CAAM,CAAE,CAAI,EACxB,GAAoB,UAAhB,OAAO,EAAmB,OAAO,EAAA,cAAc,CAAC,cAAc,CAAC,EAAQ,GAC3E,IAAM,EAAa,EAAK,WAAW,GAI7B,EAAW,OAAO,IAAI,CAAC,GAAS,IAAI,CAAC,AAAC,GAAI,EAAE,WAAW,KAAO,UAEpE,IAAI,CAAoB,IAAb,GAEJ,EAAA,IAF8B,OAAO,GAEvB,CAAC,cAAc,CAAC,EAAQ,EACjD,CACJ,EACJ,CAIE,OAAO,KAAK,CAAO,CAAE,CACnB,OAAO,IAAI,MAAM,EAAS,CACtB,IAAK,CAAM,CAAE,CAAI,CAAE,CAAQ,EACvB,OAAO,GACH,IAAK,SACL,IAAK,SACL,IAAK,MACD,OAAO,EAAqB,QAAQ,AACxC,SACI,OAAO,EAAA,cAAc,CAAC,GAAG,CAAC,EAAQ,EAAM,EAChD,CACJ,CACJ,EACJ,CAOE,MAAM,CAAK,CAAE,QACX,AAAI,MAAM,OAAO,CAAC,GAAe,EAAM,GAAb,CAAiB,CAAC,MACrC,CACX,CAME,OAAO,KAAK,CAAO,CAAE,QACnB,AAAI,aAAmB,QAAgB,CAAP,CACzB,IAAI,EAAe,EAC9B,CACA,OAAO,CAAI,CAAE,CAAK,CAAE,CAChB,IAAM,EAAW,IAAI,CAAC,OAAO,CAAC,EAAK,CACX,UAApB,AAA8B,OAAvB,EACP,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACjB,EACA,EACH,CACM,MAAM,OAAO,CAAC,GACrB,EAAS,IAAI,CAAC,CADkB,EAGhC,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CAE7B,CACA,OAAO,CAAI,CAAE,CACT,OAAO,IAAI,CAAC,OAAO,CAAC,EAAK,AAC7B,CACA,IAAI,CAAI,CAAE,CACN,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,EAAK,QAChC,AAAI,KAAiB,IAAV,EAA8B,IAAI,CAAC,EAAZ,GAAiB,CAAC,GAC7C,IACX,CACA,IAAI,CAAI,CAAE,CACN,OAAO,KAA8B,IAAvB,IAAI,CAAC,OAAO,CAAC,EAAK,AACpC,CACA,IAAI,CAAI,CAAE,CAAK,CAAE,CACb,IAAI,CAAC,OAAO,CAAC,EAAK,CAAG,CACzB,CACA,QAAQ,CAAU,CAAE,CAAO,CAAE,CACzB,IAAK,GAAM,CAAC,EAAM,EAAM,GAAI,IAAI,CAAC,OAAO,GAAG,AACvC,EAAW,IAAI,CAAC,EAAS,EAAO,EAAM,IAAI,CAElD,CACA,CAAC,SAAU,CACP,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,GAGtB,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,MAAM,CACF,EACA,EACH,AACL,CACJ,CACA,CAAC,MAAO,CACJ,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CACxC,IAAM,EAAO,EAAI,WAAW,EAC5B,OAAM,CACV,CACJ,CACA,CAAC,QAAS,CACN,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAGxC,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EACvB,OAAM,CACV,CACJ,CACA,CAAC,OAAO,QAAQ,CAAC,EAAG,CAChB,OAAO,IAAI,CAAC,OAAO,EACvB,CACJ,CExKA,CF0KA,CE1KA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAuDmC,GFiHA,IEjHO,AAFA,CAAC,mBAAmB,CAAC,EAGxD,IAAM,EAAyB,OAJM,AAIC,CAJA,kBAAkB,CAAC,ECtDhE,IAAA,EAAA,EAAA,CAAA,CAAA,OFDA,EAAA,EAAA,CAAA,CAAA,MAEO,OAAM,EACT,YAAY,CAAM,CAAE,CAAG,CAAE,CAAI,CAAC,CAC1B,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,GAAG,CAAG,EACX,IAAI,CAAC,IAAI,CAAG,CAChB,CAEA,IAAI,SAAU,cACV,AAAI,IAAI,CAAC,QAAQ,CAAS,CAAP,GAAW,CAAC,QAAQ,CAChC,IAAI,CAAC,QAAQ,CAAG,CGRK,EHQW,IAAI,CGRR,AHQS,OAAO,CGPhD,SAAS,EACZ,GAAM,QAAE,CAAM,CAAE,CAAG,EACnB,GAAI,CAAC,EACD,MADS,AACF,CAAC,EAEZ,GAAM,CAAE,MAAO,CAAa,CAAE,CAAA,EAAA,CAAA,CAAA,OAC9B,OAAO,EAAc,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,MAAQ,EACrE,IHCA,CACJ,CACO,MAAM,EACT,YAAY,CAAW,CAAC,CACpB,IAAI,CAAC,WAAW,CAAG,CACvB,CAEA,SAAS,CAAW,CAAE,CAAU,CAAE,CAQ9B,OAPA,IAAI,CAAC,SAAS,CAAC,WAAY,GAC3B,IAAI,CAAC,UAAU,CAAG,EAGd,IAAe,EAAA,kBAAkB,CAAC,iBAAiB,EAAE,AACrD,IAAI,CAAC,SAAS,CAAC,UAAW,CAAC,MAAM,EAAE,EAAA,CAAa,EAE7C,IAAI,AACf,CACJ,CEzBO,CF2BP,KE3Ba,UAAwB,EACjC,QAAO,CAAA,AAAE,CAAU,EAAP,AAA4B,EAAA,EF0BX,eE1B4B,AAAC,AAC1D,aAAY,CAAI,CAAC,CACb,IAAI,EACJ,KAAK,CAAC,EAAK,MAAM,CAAC,WAAW,GAAI,EAAK,GAAG,CAAE,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,YAAY,CAAG,AAA4B,MAA3B,GAAa,IAAI,CAAC,IAAA,AAAI,EAAY,KAAK,EAAI,EAAW,YAAY,CAAE,IAAI,CAAC,EAAmB,CAAG,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,EAAI,CAAC,EAAG,IAAI,CAAC,SAAS,EAAG,CACnR,CACA,IAAI,iBAAkB,CAMlB,OAHA,IAAI,CAAC,IAAI,CAAC,EAAA,iBAAiB,CAAC,CAAG,IAAI,CAAC,EAAA,iBAAiB,CAAC,CACtD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,GAAG,CACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAG,IAAI,CAAC,OAAO,CACzB,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,gBAAgB,CAAK,CAAE,CACvB,IAAI,CAAC,IAAI,CAAG,CAChB,CAOE,QAAS,CACP,GAAI,IAAI,CAAC,SAAS,CACd,CADgB,KACV,OAAO,cAAc,CAAC,AAAI,MAAM,+DAAgE,oBAAqB,CACvH,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAGJ,OADA,IAAI,CAAC,SAAS,EAAG,EACV,IAAI,eAAe,CACtB,MAAO,AAAC,IACJ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAQ,AAAC,IAClB,EAAW,OAAO,CAAC,IAAI,WAAW,GACtC,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAO,KAChB,EAAW,KAAK,EACpB,GACA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAS,AAAC,IACnB,EAAW,KAAK,CAAC,EACrB,EACJ,CACJ,EACJ,CACJ,CACO,MAAM,UAAyB,EAClC,IAAI,kBAAmB,CAInB,OAHI,KAA0B,IAAI,EAAE,CAChC,IAAI,CAAC,IAAI,CAAC,EAAuB,CAAG,IAAI,CAAC,EAAA,AAAuB,EAE7D,IAAI,CAAC,IAAI,AACpB,CACA,YAAY,CAAI,CAAC,CACb,KAAK,CAAC,GAAO,IAAI,CAAC,IAAI,CAAG,EAAM,IAAI,CAAC,QAAQ,MAAG,CACnD,CACA,IAAI,MAAO,CACP,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,IAAI,CAAC,WAC3C,AADsD,CAEtD,IAAI,YAAa,CACb,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,AAC/B,CACA,IAAI,WAAW,CAAK,CAAE,CAClB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAG,CAC3B,CACA,IAAI,eAAgB,CAChB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,AAClC,CACA,IAAI,cAAc,CAAK,CAAE,CACrB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAG,CAC9B,CACA,UAAU,CAAI,CAAE,CAAK,CAAE,CAEnB,OADA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,GACnB,IAAI,AACf,CACA,aAAa,CAAI,CAAE,CAEf,OADA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAChB,IAAI,AACf,CACA,gBAAgB,CAAI,CAAE,CAClB,IAAM,EAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GACnC,QAAe,IAAX,EACJ,KAD0B,CACnB,CAAC,KADyB,CACnB,OAAO,CAAC,GAAU,EAAS,CACrC,EACH,EAAE,GAAG,CAAC,AAAC,GAAQ,EAAM,QAAQ,GAClC,CACA,UAAU,CAAI,CAAE,CACZ,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAC/B,CACA,UAAU,CAAI,CAAE,CACZ,IAAM,EAAS,IAAI,CAAC,eAAe,CAAC,GACpC,OAAO,MAAM,OAAO,CAAC,GAAU,EAAO,IAAI,CAAC,UAAO,CACtD,CACA,YAAa,CACT,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAC/B,CACA,aAAa,CAAI,CAAE,CAAK,CAAE,CACtB,IAAM,EAAgB,IAAI,CAAC,eAAe,CAAC,IAAS,EAAE,CAOtD,OANI,AAAC,EAAc,QAAQ,CAAC,IACxB,IADgC,AAC5B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAM,IACnB,EACH,EACH,EAEE,IAAI,AACf,CACA,KAAK,CAAK,CAAE,CAER,OADA,IAAI,CAAC,QAAQ,CAAG,EACT,IAAI,AACf,CACA,MAAO,CACH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAC/B,CACA,QAAQ,CAAQ,CAAE,CACd,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,QAAS,EACtC,CACJ,gEExHA,IAAA,EAAA,EAAA,CAAA,CAAA,OAmBW,SAAS,EAAiB,CAAK,QACtC,MCjBO,CAD4B,ADkB5B,EAAmB,EClBa,ADkBP,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,EAAU,EAAS,EAAO,IAEzE,AAAI,CAAC,GAID,CAAA,EAAA,EAAA,CAJU,aAIV,AAAc,EAAC,IAIA,KAAK,CAJK,AAIzB,CAAO,CAAC,EAAE,EAIV,AAAC,CAAY,YAAsB,UAAZ,CAAY,CAAO,EAAK,IAAU,EAAS,MAAM,CAAG,EAXpE,CAWuE,CAG3E,EAAW,IAAM,EACzB,KCnCS,UAAU,CAAC,KAAO,EAAO,IAAM,CDoC/C,+DExCA,IAAA,EAA+B,EAAqB,CAA3CE,AAA2C,CAAA,IAAA,GAgB7C,MAhBgB,GAgBPC,EAAsB,GAhBP,SAiB7BC,CAAU,QACVC,CAAM,CACO,EACb,IAAMC,EACkB,UAAtB,OAAOF,GACPC,KAAWJ,OACXG,EAAaC,EACT,CAAC,yBAAyB,EAAEA,EAASD,EAAAA,CAAY,CACjD,UAEN,AAAmB,GAAG,CAAlBA,EACK,0DACwB,UAAtB,AAAgC,OAAzBA,EACT,CAAC,SAAS,EAAEA,EAAAA,EAAaE,EAAAA,CAAW,CAGtC,CAAC,SAAS,EAAEJ,EAAAA,cAAAA,CAAAA,EAAiBI,EAAAA,CAAW,AACjD,uIClCA,IAAA,EAAA,EAAA,CAAA,CAAA,OAYO,IAAM,EAAmC,CAC9C,iBACA,eACA,iBACA,iBACA,eACA,qBACA,kBACA,aACD,CAQM,eAAe,EAAmB,CAAkB,aAEnB,IAApB,SAAS,CAAvB,EAAK,IAAI,AAAqB,CAAuB,EAO1C,CAJF,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAChD,MAAO,CAAE,OAAQ,EAAK,EAAE,CAAE,eAAgB,EAAK,cAAe,AAAD,EAC7D,OAAQ,CAAE,WAAY,EAAK,CAC7B,EAAA,EACoB,GAAG,CAAC,GAAK,EAAE,UAAU,EAC3C,CAEO,SAAS,EAAc,CAAyB,CAAE,CAAkB,EACzE,OAAO,EAAM,GAAG,CAAC,EACnB,mFC3CA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAGA,SAAS,EAAM,CAAS,EACtB,OAAO,KAAK,KAAK,CAAK,GAAJ,GAAU,EAC9B,CAEe,eAAe,IAC5B,IAAM,EAAc,MAAM,CAAA,EAAA,EAAA,OAAA,AAAO,IAC3B,EAAQ,EAAY,GAAG,CAAC,eAAe,MACvC,EAAO,EAAQ,CAAA,EAAA,EAAA,WAAA,AAAW,EAAC,GAAS,KAI1C,GAHI,AAAC,GAAM,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,eAGF,UAAd,EAAK,IAAI,CAAc,CACzB,IAAM,EAAQ,MAAM,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,CAAE,GAAI,EAAK,EAAE,CAAE,KAAM,EAAK,IAAI,CAAE,eAAgB,EAAK,cAAc,AAAC,GAC3G,GAAI,CAAC,CAAA,EAAA,EAAA,aAAA,AAAa,EAAC,EAAO,gBACxB,CADyC,KAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4DAAmD,eAI1E,CAIA,IAAM,EADc,AACD,OADO,EAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAK,EAAE,AAAC,EAAG,OAAQ,CAAE,WAAW,CAAK,CAAE,EAAA,EACjF,GAAG,CAAC,GAAK,EAAE,SAAS,EAE7C,EAAgB,CADJ,MAAM,EAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAE,MAAO,CAAE,OAAQ,EAAK,EAAG,AAAD,EAAI,OAAQ,CAAE,cAAc,CAAK,CAAE,EAAA,EACpF,GAAG,CAAC,GAAK,EAAE,YAAY,EAEjD,EAAiB,AAAc,YAAT,IAAI,CAC5B,CAAE,eAAgB,EAAK,cAAc,AAAC,EACtC,CACE,eAAgB,EAAK,cAAc,CACnC,UAAW,CAAE,GAAI,EAAW,MAAM,CAAG,EAAa,CAAC,WAAW,AAAC,EAC/D,aAAc,CAAE,GAAI,EAAc,MAAM,CAAG,EAAgB,CAAC,WAAY,AAAD,CACzE,EAEE,CAAC,EAAO,EAAM,EAAY,EAAQ,EAAW,EAAU,EAAO,CAAG,MAAM,QAAQ,GAAG,CAAC,CACvF,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAU,GACvC,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,MAAO,CAAE,GAC9D,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,aAAc,CAAE,GACrE,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,QAAS,CAAE,GAChE,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,WAAY,CAAE,GACnE,EAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,UAAW,CAAE,GAClE,EAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CACrB,MAAO,EACP,QAAS,CAAE,UAAW,CAAE,OAAQ,CAAE,UAAU,EAAM,MAAO,EAAK,CAAE,CAAE,EAClE,QAAS,CAAE,UAAW,MAAO,EAC7B,KAAM,CACR,GACD,EAGK,CAAC,EAAiB,EAAY,CAAG,MAAM,QAAQ,GAAG,CAAC,CACvD,EAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CACrB,MAAO,CAAE,GAAG,CAAS,CAAE,GAAI,CAAC,CAAE,OAAQ,UAAW,EAAG,CAAE,OAAQ,QAAS,EAAE,CAAE,WAAY,CAAE,IAAK,IAAK,CAAE,EACrG,OAAQ,CAAE,WAAW,EAAM,YAAY,CAAK,EAC5C,QAAS,CAAE,WAAY,MAAO,EAC9B,KAAM,GACR,GACA,EAAA,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CACrB,MAAO,CAAE,GAAG,CAAS,CAAE,OAAQ,CAAE,GAAI,CAAC,OAAQ,cAAe,uBAAwB,oBAAoB,AAAC,CAAE,EAC5G,OAAQ,CAAE,WAAW,CAAK,EAC1B,QAAS,CAAE,UAAW,MAAO,EAC7B,KAAM,GACR,GACD,EAEK,EAA0B,EAC7B,GAAG,CAAC,AAAC,GAAO,CAAC,EAAE,UAAU,CAAE,OAAO,GAAK,EAAE,SAAS,CAAC,OAAO,EAAA,CAAE,CAAI,MAChE,MAAM,CAAC,AAAC,GAAc,GAAK,GAC3B,IAAI,CAAC,CAAC,EAAW,IAAc,EAAI,GAChC,EAAe,EAAc,MAAM,CAAG,EAAM,EAAc,MAAM,CAAC,CAAC,EAAW,IAAc,EAAI,EAAG,GAAK,EAAc,MAAM,EAAI,EAC/H,EAAe,EAAc,MAAM,CAAG,EAAM,CAAa,CAAC,KAAK,KAAK,CAAC,EAAc,MAAM,CAAG,GAAG,EAAI,EAEnG,EAAM,KAAK,GAAG,GACd,EAAsB,EACzB,GAAG,CAAC,AAAC,GAA4B,CAAC,EAAM,EAAE,SAAS,CAAC,OAAO,EAAA,CAAE,CAAI,MACjE,MAAM,CAAC,AAAC,GAAc,GAAK,GACxB,EAAc,EAAU,MAAM,CAAG,EAAM,EAAU,MAAM,CAAC,CAAC,EAAW,IAAc,EAAI,EAAG,GAAK,EAAU,MAAM,EAAI,EAExH,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,6CAAmC,iBAAe,EAAK,QAAQ,EAAI,EAAK,KAAK,IAC3F,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,2CAG5C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDACZ,CAAC,CAAC,MAAM,OAAO,MAAM,CAAI,EAAE,CAAC,MAAM,MAAM,MAAM,CAAU,EAAE,CAAC,MAAM,SAAS,MAAM,CAAM,EAAE,CAAC,MAAM,YAAY,MAAM,CAAS,EAAE,CAAC,MAAM,QAAQ,MAAM,CAAK,EAAE,CAAC,GAAG,CAAC,GAC5J,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAkB,UAAU,2CAC3B,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAE,KAAK,GAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAE,KAAK,KAFpC,EAAE,KAAK,KAOrB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,0BACvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BAAsB,EAAa,OAClD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAAwB,WAAS,EAAa,UAE/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,2BACvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BAAsB,EAAY,OACjD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAAwB,WAAS,EAAY,MAAM,OAEpE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,qBACvC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,IACrC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAAwB,QAAM,EAAgB,MAAM,CAAC,yBAIxE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wBAAe,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,qBAC5D,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oBACO,IAAlB,EAAO,MAAM,CACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qCAA4B,2CACxC,EAAO,GAAG,CAAC,AAAC,GACd,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAc,UAAU,kDACvB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qCAA6B,EAAE,YAAY,GAC1D,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAE,KAAK,GAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAyB,EAAE,SAAS,CAAC,QAAQ,EAAI,EAAE,SAAS,CAAC,KAAK,MAEnF,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,oEAA4D,EAAE,MAAM,KAN7E,EAAE,EAAE,YAc3B", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8]}