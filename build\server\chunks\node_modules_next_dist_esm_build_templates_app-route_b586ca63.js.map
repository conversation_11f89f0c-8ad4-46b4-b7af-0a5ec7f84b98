{"version": 3, "sources": ["turbopack:///[project]/src/app/api/email/inbound/route.ts", "turbopack:///[project]/node_modules/next/dist/esm/build/templates/app-route.js"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\nimport { writeFile, mkdir } from 'fs/promises'\nimport path from 'path'\n\nexport const runtime = 'nodejs'\n\n// Helpers\nfunction sanitizeText(input: string | undefined | null): string {\n  if (!input) return ''\n  // Strip HTML tags and dangerous content (very basic; consider sanitize-html later)\n  return String(input)\n    .replace(/<style[\\s\\S]*?<\\/style>/gi, '')\n    .replace(/<script[\\s\\S]*?<\\/script>/gi, '')\n    .replace(/<[^>]+>/g, '')\n    .trim()\n}\n\nasync function resolveOrganization(toEmail: string | undefined): Promise<string | null> {\n  const envOrg = process.env.EMAIL_ORG_ID\n  if (envOrg) return envOrg\n  if (toEmail) {\n    const m = /@([^>\\s]+)$/.exec(toEmail)\n    const domain = m?.[1]?.toLowerCase()\n    if (domain) {\n      const org = await prisma.organization.findFirst({ where: { domain } })\n      if (org) return org.id\n    }\n  }\n  // fallback: if only one org exists, use it\n  const any = await prisma.organization.findMany({ select: { id: true }, take: 2 })\n  if (any.length === 1) return any[0].id\n  return null\n}\n\nasync function findOrCreateCustomerUser(orgId: string, email: string) {\n  const existing = await prisma.user.findUnique({ where: { email } })\n  if (existing) return existing\n  const fullName = email.split('@')[0]\n  // minimal random password placeholder; not used for login typically\n  const password = Math.random().toString(36).slice(2)\n  return prisma.user.create({ data: { email, password, fullName, role: 'CUSTOMER', organizationId: orgId } })\n}\n\nasync function resolveProject(orgId: string, cfg?: any): Promise<string | undefined> {\n  if (cfg?.defaultProjectId) return cfg.defaultProjectId\n  const envProj = process.env.EMAIL_DEFAULT_PROJECT_ID\n  if (envProj) return envProj\n  const p = await prisma.project.findFirst({ where: { organizationId: orgId }, select: { id: true }, orderBy: { createdAt: 'asc' } })\n  return p?.id\n}\n\nasync function resolveDepartment(orgId: string, toEmail: string | undefined, subject: string | undefined, cfg?: any): Promise<string | undefined> {\n  // 1) Map by org settings routingMap (recipient -> department name/ID)\n  try {\n    const map = cfg?.routingMap as Record<string, string> | undefined\n    if (map && toEmail) {\n      const key = toEmail.toLowerCase()\n      const val = map[key]\n      if (val) {\n        const byName = await prisma.department.findFirst({ where: { organizationId: orgId, name: val }, select: { id: true } })\n        if (byName) return byName.id\n        if (/^c[a-z0-9]{20,}$/i.test(val)) return val\n      }\n    }\n  } catch {}\n  // 2) Env JSON fallback\n  try {\n    const raw = process.env.EMAIL_ROUTING_MAP\n    if (raw && toEmail) {\n      const map = JSON.parse(raw) as Record<string, string>\n      const key = toEmail.toLowerCase()\n      if (map[key]) {\n        const byName = await prisma.department.findFirst({ where: { organizationId: orgId, name: map[key] }, select: { id: true } })\n        if (byName) return byName.id\n        if (/^c[a-z0-9]{20,}$/i.test(map[key])) return map[key]\n      }\n    }\n  } catch {}\n  // 3) Subject token [DEPT:Name]\n  if (subject) {\n    const m = /\\[(?:DEPT|DEPARTMENT):\\s*([^\\]]+)\\]/i.exec(subject)\n    const deptName = m?.[1]?.trim()\n    if (deptName) {\n      const dep = await prisma.department.findFirst({ where: { organizationId: orgId, name: deptName }, select: { id: true } })\n      if (dep) return dep.id\n    }\n  }\n  // 4) Default from org settings\n  if (cfg?.defaultDepartmentId) return cfg.defaultDepartmentId\n  // 5) Fallback: \"General Support\" or first department\n  const general = await prisma.department.findFirst({ where: { organizationId: orgId, name: 'General Support' }, select: { id: true } })\n  if (general) return general.id\n  const any = await prisma.department.findFirst({ where: { organizationId: orgId }, select: { id: true }, orderBy: { createdAt: 'asc' } })\n  return any?.id\n}\n\nfunction generateTicketNumber(): string {\n  const timestamp = new Date().toISOString().replace(/[-:TZ.]/g, '').slice(0, 14)\n  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')\n  return `TKT-${timestamp}-${random}`\n}\n\nasync function saveAttachmentBuffer(buffer: Buffer, originalName: string, mime: string, ticketId: string) {\n  const uploadDir = path.join(process.cwd(), 'public', 'uploads')\n  await mkdir(uploadDir, { recursive: true })\n  const safeName = originalName.replace(/[^a-zA-Z0-9._-]/g, '_')\n  const basename = `${Date.now()}-${Math.random().toString(36).slice(2,8)}-${safeName}`\n  const filePath = path.join(uploadDir, basename)\n  await writeFile(filePath, buffer)\n  const url = `/uploads/${basename}`\n  await prisma.attachment.create({\n    data: {\n      filename: basename,\n      originalName,\n      mimeType: mime || 'application/octet-stream',\n      size: buffer.length,\n      url,\n      ticketId,\n    },\n  })\n}\n\nasync function sendAcknowledgementEmail(opts: { to: string, ticketNumber: string }) {\n  // Placeholder: implement via nodemailer or provider API after dependencies are approved\n  console.log(`[ACK] Would send acknowledgment to ${opts.to} for ${opts.ticketNumber}`)\n}\n\n// Simple rate limiting per sender per hour\nasync function rateLimitSender(email: string, limit: number): Promise<boolean> {\n  const since = new Date(Date.now() - 60 * 60 * 1000)\n  const cnt = await prisma.emailThread.count({ where: { fromEmail: email, receivedAt: { gte: since } } })\n  return cnt < (limit || 20)\n}\n\nexport async function POST(req: NextRequest) {\n\n  try {\n    const contentType = req.headers.get('content-type') || ''\n\n    // We support JSON payloads shaped like common inbound email webhooks\n    // { subject, from, to, text, html, messageId, inReplyTo, references, attachments: [{ filename, mimeType, contentBase64 }] }\n    let payload: any\n    if (contentType.includes('application/json')) {\n      payload = await req.json()\n    } else if (contentType.includes('multipart/form-data')) {\n      // Some providers send fields as form-data; only parse the basic fields here\n      const form = await req.formData()\n      payload = Object.fromEntries(Array.from(form.entries()))\n      // attachments not handled for multipart in this minimal implementation\n    } else {\n      return NextResponse.json({ error: 'Unsupported content type' }, { status: 415 })\n    }\n\n    const from = String(payload.from || '').trim()\n    const to = String(payload.to || '').trim()\n    const subject = sanitizeText(payload.subject)\n    const textBody = sanitizeText(payload.text || payload.body)\n    const htmlBody = sanitizeText(payload.html)\n    const body = textBody || htmlBody\n    const messageId = String(payload.messageId || payload['Message-Id'] || '').trim()\n    const inReplyTo = String(payload.inReplyTo || payload['In-Reply-To'] || '').trim() || undefined\n    const references = Array.isArray(payload.references) ? payload.references.join(', ') : (payload.references || undefined)\n\n    if (!from || !subject || !body) {\n      return NextResponse.json({ error: 'Missing required email fields' }, { status: 400 })\n    }\n\n    // Resolve org first\n    const orgId = await resolveOrganization(to)\n    if (!orgId) return NextResponse.json({ error: 'Organization resolution failed' }, { status: 400 })\n\n    // Load org settings and validate webhook secret\n    const org = await prisma.organization.findUnique({ where: { id: orgId }, select: { settings: true } })\n    const cfg = (org?.settings as any)?.emailConfig || {}\n    const secret = req.headers.get('x-email-webhook-secret') || ''\n    const expectedSecret = cfg.webhookSecret || process.env.EMAIL_WEBHOOK_SECRET || ''\n    if (!expectedSecret || secret !== expectedSecret) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Rate limit with org setting\n    const limit = Number(cfg.rateLimitPerHour) > 0 ? Number(cfg.rateLimitPerHour) : 20\n    const allowed = await rateLimitSender(from.toLowerCase(), limit)\n    if (!allowed) return NextResponse.json({ error: 'Rate limited' }, { status: 429 })\n\n    // Duplicate detection via Message-ID\n    if (messageId) {\n      const exists = await prisma.emailThread.findUnique({ where: { messageId } })\n      if (exists) {\n        return NextResponse.json({ ok: true, deduped: true })\n      }\n    }\n\n    // If reply: attach to existing ticket by inReplyTo chain\n    let parentTicketId: string | undefined\n    if (inReplyTo) {\n      const parent = await prisma.emailThread.findUnique({ where: { messageId: inReplyTo } })\n      if (parent?.ticketId) parentTicketId = parent.ticketId\n    }\n\n    // Ensure customer user\n    const customer = await findOrCreateCustomerUser(orgId, from.toLowerCase())\n\n    // Routing using org settings (fallback to env/db defaults)\n    const departmentId = await resolveDepartment(orgId, to, subject, cfg)\n    const projectId = await resolveProject(orgId, cfg)\n\n    let ticketId: string\n    let ticketNumber: string\n\n    if (parentTicketId) {\n      // Append as a message to existing ticket\n      const t = await prisma.ticket.findUnique({ where: { id: parentTicketId }, select: { id: true, ticketNumber: true } })\n      if (!t) return NextResponse.json({ error: 'Parent ticket not found' }, { status: 404 })\n      ticketId = t.id\n      ticketNumber = t.ticketNumber\n\n      await prisma.ticketMessage.create({\n        data: {\n          content: body,\n          isInternal: false,\n          ticketId,\n          authorId: customer.id,\n        },\n      })\n    } else {\n      ticketNumber = generateTicketNumber()\n      const ticket = await prisma.ticket.create({\n        data: {\n          ticketNumber,\n          title: subject,\n          description: body,\n          status: 'OPEN',\n          priority: 'MEDIUM',\n          organizationId: orgId,\n          createdById: customer.id,\n          projectId: projectId,\n          departmentId: departmentId,\n          metadata: messageId ? { messageId } : undefined,\n        },\n        select: { id: true },\n      })\n      ticketId = ticket.id\n\n      await prisma.ticketMessage.create({\n        data: {\n          content: `[AUDIT] Ticket created from email by ${from}`,\n          isInternal: true,\n          ticketId,\n          authorId: customer.id,\n        },\n      })\n    }\n\n    // Record email thread\n    await prisma.emailThread.create({\n      data: {\n        subject: subject || '(no subject)',\n        fromEmail: from,\n        toEmail: to || '',\n        messageId: messageId || `${Date.now()}-${Math.random().toString(36).slice(2)}`,\n        inReplyTo,\n        references,\n        body,\n        isHtml: false,\n        ticketId,\n        userId: customer.id,\n      },\n    })\n\n    // Attachments (JSON format: attachments: [{ filename, mimeType, contentBase64 }])\n    if (Array.isArray(payload.attachments) && payload.attachments.length) {\n      for (const att of payload.attachments.slice(0, 5)) {\n        try {\n          const filename = String(att.filename || 'attachment')\n          const mimeType = String(att.mimeType || 'application/octet-stream')\n          const b64 = String(att.contentBase64 || '')\n          if (!b64) continue\n          const buffer = Buffer.from(b64, 'base64')\n          await saveAttachmentBuffer(buffer, filename, mimeType, ticketId)\n        } catch (e) {\n          console.error('Attachment save failed', e)\n        }\n      }\n    }\n\n    // Send acknowledgment (placeholder)\n    await sendAcknowledgementEmail({ to: from, ticketNumber })\n\n    return NextResponse.json({ ok: true, ticketId, ticketNumber })\n  } catch (e: any) {\n    console.error('Email inbound error', e)\n    return NextResponse.json({ error: 'Internal error' }, { status: 500 })\n  }\n}\n\n", "import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/email/inbound/route\",\n        pathname: \"/api/email/inbound\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/src/app/api/email/inbound/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/email/inbound/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n"], "names": [], "mappings": "qLCAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,+CDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,IAAM,EAAU,SAGvB,SAAS,EAAa,CAAgC,SACpD,AAAK,EAEE,EAFH,GAAQ,EAEE,GACX,OAAO,CAAC,4BAA6B,IACrC,OAAO,CAAC,8BAA+B,IACvC,OAAO,CAAC,WAAY,IACpB,IAAI,GANY,EAOrB,CAEA,eAAe,EAAoB,CAA2B,EAC5D,IAAM,EAAS,QAAQ,GAAG,CAAC,YAAY,CACvC,GAAI,EAAQ,OAAO,EACnB,GAAI,EAAS,CACX,IAAM,EAAI,cAAc,IAAI,CAAC,GACvB,EAAS,GAAG,CAAC,EAAE,EAAE,cACvB,GAAI,EAAQ,CACV,IAAM,EAAM,MAAM,EAAA,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAE,MAAO,QAAE,CAAO,CAAE,GACpE,GAAI,EAAK,OAAO,EAAI,EAAE,AACxB,CACF,CAEA,IAAM,EAAM,MAAM,EAAA,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAE,OAAQ,CAAE,IAAI,CAAK,EAAG,KAAM,CAAE,UAC/E,AAAmB,GAAG,CAAlB,EAAI,MAAM,CAAe,CAAG,CAAC,EAAE,CAAC,EAAE,CAC/B,IACT,CAEA,eAAe,EAAyB,CAAa,CAAE,CAAa,EAClE,IAAM,EAAW,MAAM,EAAA,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAE,MAAO,OAAE,CAAM,CAAE,GACjE,GAAI,EAAU,OAAO,EACrB,IAAM,EAAW,EAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAE9B,EAAW,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAClD,OAAO,EAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAE,KAAM,OAAE,WAAO,WAAU,EAAU,KAAM,WAAY,eAAgB,CAAM,CAAE,EAC3G,CAEA,eAAe,EAAe,CAAa,CAAE,CAAS,EACpD,GAAI,GAAK,iBAAkB,OAAO,EAAI,gBAAgB,CACtD,IAAM,EAAU,QAAQ,GAAG,CAAC,wBAAwB,CACpD,GAAI,EAAS,OAAO,EACpB,IAAM,EAAI,MAAM,EAAA,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,CAAM,EAAG,OAAQ,CAAE,IAAI,CAAK,EAAG,QAAS,CAAE,UAAW,KAAM,CAAE,GACjI,OAAO,GAAG,EACZ,CAEA,eAAe,EAAkB,CAAa,CAAE,CAA2B,CAAE,CAA2B,CAAE,CAAS,EAEjH,GAAI,CACF,IAAM,EAAM,GAAK,WACjB,GAAI,GAAO,EAAS,CAElB,IAAM,EAAM,CAAG,CADH,AACI,EADI,WAAW,GACX,CACpB,GAAI,EAAK,CACP,IAAM,EAAS,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAO,KAAM,CAAI,EAAG,OAAQ,CAAE,IAAI,CAAK,CAAE,GACrH,GAAI,EAAQ,OAAO,EAAO,EAAE,CAC5B,GAAI,oBAAoB,IAAI,CAAC,GAAM,OAAO,CAC5C,CACF,CACF,CAAE,KAAM,CAAC,CAET,GAAI,CACF,IAAM,EAAM,QAAQ,GAAG,CAAC,iBAAiB,CACzC,GAAI,GAAO,EAAS,CAClB,IAAM,EAAM,KAAK,KAAK,CAAC,GACjB,EAAM,EAAQ,WAAW,GAC/B,GAAI,CAAG,CAAC,EAAI,CAAE,CACZ,IAAM,EAAS,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAO,KAAM,CAAG,CAAC,EAAI,AAAC,EAAG,OAAQ,CAAE,GAAI,EAAK,CAAE,GAC1H,GAAI,EAAQ,OAAO,EAAO,EAAE,CAC5B,GAAI,oBAAoB,IAAI,CAAC,CAAG,CAAC,EAAI,EAAG,OAAO,CAAG,CAAC,EAAI,AACzD,CACF,CACF,CAAE,KAAM,CAAC,CAET,GAAI,EAAS,CACX,IAAM,EAAI,uCAAuC,IAAI,CAAC,GAChD,EAAW,GAAG,CAAC,EAAE,EAAE,OACzB,GAAI,EAAU,CACZ,IAAM,EAAM,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAO,KAAM,CAAS,EAAG,OAAQ,CAAE,IAAI,CAAK,CAAE,GACvH,GAAI,EAAK,OAAO,EAAI,EAAE,AACxB,CACF,CAEA,GAAI,GAAK,oBAAqB,OAAO,EAAI,mBAAmB,CAE5D,IAAM,EAAU,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,EAAO,KAAM,iBAAkB,EAAG,OAAQ,CAAE,IAAI,CAAK,CAAE,GACpI,GAAI,EAAS,OAAO,EAAQ,EAAE,CAC9B,IAAM,EAAM,MAAM,EAAA,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAE,MAAO,CAAE,eAAgB,CAAM,EAAG,OAAQ,CAAE,IAAI,CAAK,EAAG,QAAS,CAAE,UAAW,KAAM,CAAE,GACtI,OAAO,GAAK,EACd,CAQA,eAAe,EAAqB,CAAc,CAAE,CAAoB,CAAE,CAAY,CAAE,CAAgB,EACtG,IAAM,EAAY,EAAA,OAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAI,SAAU,UACrD,OAAM,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,EAAW,CAAE,WAAW,CAAK,GACzC,IAAM,EAAW,EAAa,OAAO,CAAC,mBAAoB,KACpD,EAAW,CAAA,EAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,EAAA,CAAU,CAC/E,EAAW,EAAA,OAAI,CAAC,IAAI,CAAC,EAAW,EACtC,OAAM,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,EAAU,GAC1B,IAAM,EAAM,CAAC,SAAS,EAAE,EAAA,CAAU,AAClC,OAAM,EAAA,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAC7B,KAAM,CACJ,SAAU,eACV,EACA,SAAU,GAAQ,2BAClB,KAAM,EAAO,MAAM,KACnB,WACA,CACF,CACF,EACF,CAEA,eAAe,EAAyB,CAA0C,EAEhF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,EAAK,EAAE,CAAC,KAAK,EAAE,EAAK,YAAY,CAAA,CAAE,CACtF,CAGA,eAAe,EAAgB,CAAa,CAAE,CAAa,EACzD,IAAM,EAAQ,IAAI,KAAK,KAAK,GAAG,GAAK,KAAK,CAEzC,IAF8C,GAClC,AACL,MADW,AACL,EADK,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAE,MAAO,CAAE,UAAW,EAAO,WAAY,CAAE,IAAK,CAAM,CAAE,CAAE,IACvF,GAAS,EAAA,CAAE,AAC3B,CAEO,eAAe,EAAK,CAAgB,EAEzC,GAAI,CACF,IAII,EAqDA,EAaA,EACA,EAvEE,EAAc,EAAI,OAAO,CAAC,GAAG,CAAC,iBAAmB,GAKvD,GAAI,EAAY,QAAQ,CAAC,oBACvB,CAD4C,CAClC,MAAM,EAAI,IAAI,OAC8B,CAAjD,IAAI,EAAY,QAAQ,CAAC,uBAM9B,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,0BAA2B,EAAG,CAAE,OAAQ,GAAI,GAJ9E,IAAM,EAAO,MAAM,EAAI,QAAQ,GAC/B,EAAU,OAAO,WAAW,CAAC,MAAM,IAAI,CAAC,EAAK,OAAO,IAEtD,CAIA,IAAM,EAJC,AAIM,OAAO,EAAQ,IAAI,EAAI,IAAI,IAAI,GACtC,EAAK,OAAO,EAAQ,EAAE,EAAI,IAAI,IAAI,GAClC,EAAU,EAAa,EAAQ,OAAO,EACtC,EAAW,EAAa,EAAQ,IAAI,EAAI,EAAQ,IAAI,EACpD,EAAW,EAAa,EAAQ,IAAI,EACpC,EAAO,GAAY,EACnB,EAAY,OAAO,EAAQ,SAAS,EAAI,CAAO,CAAC,aAAa,EAAI,IAAI,IAAI,GACzE,EAAY,OAAO,EAAQ,SAAS,EAAI,CAAO,CAAC,cAAc,EAAI,IAAI,IAAI,SAAM,EAChF,EAAa,MAAM,OAAO,CAAC,EAAQ,UAAU,EAAI,EAAQ,UAAU,CAAC,IAAI,CAAC,MAAS,EAAQ,UAAU,OAAI,EAE9G,GAAI,CAAC,GAAQ,CAAC,GAAW,CAAC,EACxB,IAD8B,GACvB,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,+BAAgC,EAAG,CAAE,OAAQ,GAAI,GAIrF,IAAM,EAAQ,MAAM,EAAoB,GACxC,GAAI,CAAC,EAAO,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,gCAAiC,EAAG,CAAE,OAAQ,GAAI,GAGhG,IAAM,EAAM,MAAM,EAAA,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,GAAI,CAAM,EAAG,OAAQ,CAAE,UAAU,CAAK,CAAE,GAC9F,EAAO,GAAK,UAAkB,aAAe,CAAC,EAC9C,EAAS,EAAI,OAAO,CAAC,GAAG,CAAC,2BAA6B,GACtD,EAAiB,EAAI,aAAa,EAAI,QAAQ,GAAG,CAAC,oBAAoB,EAAI,GAChF,GAAI,CAAC,GAAkB,IAAW,EAChC,OAAO,EAAA,KADyC,OAC7B,CAAC,IAAI,CAAC,CAAE,MAAO,cAAe,EAAG,CAAE,OAAQ,GAAI,GAIpE,IAAM,EAAQ,OAAO,EAAI,gBAAgB,EAAI,EAAI,OAAO,EAAI,gBAAgB,EAAI,GAEhF,GAAI,CADY,AACX,MADiB,EAAgB,EAAK,WAAW,GAAI,GAC5C,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,cAAe,EAAG,CAAE,OAAQ,GAAI,GAGhF,GAAI,GACa,MAAM,EADR,AACQ,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAE,MAAO,WAAE,CAAU,CAAE,GAExE,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,IAAI,EAAM,SAAS,CAAK,GAMvD,GAAI,EAAW,CACb,IAAM,EAAS,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,UAAW,CAAU,CAAE,GACjF,GAAQ,WAAU,EAAiB,EAAO,QAAQ,AAAR,CAChD,CAGA,IAAM,EAAW,MAAM,EAAyB,EAAO,EAAK,WAAW,IAGjE,EAAe,MAAM,EAAkB,EAAO,EAAI,EAAS,GAC3D,EAAY,MAAM,EAAe,EAAO,GAK9C,GAAI,EAAgB,CAElB,IAAM,EAAI,MAAM,EAAA,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAE,MAAO,CAAE,GAAI,CAAe,EAAG,OAAQ,CAAE,GAAI,GAAM,cAAc,CAAK,CAAE,GACnH,GAAI,CAAC,EAAG,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,yBAA0B,EAAG,CAAE,OAAQ,GAAI,GACrF,EAAW,EAAE,EAAE,CACf,EAAe,EAAE,YAAY,CAE7B,MAAM,EAAA,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAChC,KAAM,CACJ,QAAS,EACT,YAAY,WACZ,EACA,SAAU,EAAS,EAAE,AACvB,CACF,EACF,MACE,CADK,CACU,AAlIrB,SAAS,EACP,IAAM,EAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,WAAY,IAAI,KAAK,CAAC,EAAG,IACtE,EAAS,KAAK,KAAK,CAAiB,IAAhB,KAAK,MAAM,IAAY,QAAQ,GAAG,QAAQ,CAAC,EAAG,KACxE,MAAO,CAAC,IAAI,EAAE,EAAU,CAAC,EAAE,EAAA,CAAQ,AACrC,IA8IM,EAAW,CAfI,MAAM,EAAA,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CACxC,KAAM,cACJ,EACA,MAAO,EACP,YAAa,EACb,OAAQ,OACR,SAAU,SACV,eAAgB,EAChB,YAAa,EAAS,EAAE,CACxB,UAAW,EACX,aAAc,EACd,SAAU,EAAY,CAAE,WAAU,OAAI,CACxC,EACA,OAAQ,CAAE,IAAI,CAAK,CACrB,EAAA,EACkB,EAAE,CAEpB,MAAM,EAAA,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAChC,KAAM,CACJ,QAAS,CAAC,qCAAqC,EAAE,EAAA,CAAM,CACvD,YAAY,WACZ,EACA,SAAU,EAAS,EAAE,AACvB,CACF,GAoBF,GAhBA,MAAM,EAAA,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAC9B,KAAM,CACJ,QAAS,GAAW,eACpB,UAAW,EACX,QAAS,GAAM,GACf,UAAW,GAAa,CAAA,EAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAA,CAAI,CAC9E,uBACA,OACA,EACA,QAAQ,WACR,EACA,OAAQ,EAAS,EAAE,AACrB,CACF,GAGI,MAAM,OAAO,CAAC,EAAQ,WAAW,GAAK,EAAQ,WAAW,CAAC,MAAM,CAClE,CADoE,GAC/D,IAAM,KAAO,EAAQ,WAAW,CAAC,KAAK,CAAC,EAAG,GAAI,AACjD,GAAI,CACF,IAAM,EAAW,OAAO,EAAI,QAAQ,EAAI,cAClC,EAAW,OAAO,EAAI,QAAQ,EAAI,4BAClC,EAAM,OAAO,EAAI,aAAa,EAAI,IACxC,GAAI,CAAC,EAAK,SACV,IAAM,EAAS,OAAO,IAAI,CAAC,EAAK,SAChC,OAAM,EAAqB,EAAQ,EAAU,EAAU,EACzD,CAAE,MAAO,EAAG,CACV,QAAQ,KAAK,CAAC,yBAA0B,EAC1C,CAOJ,OAFA,MAAM,EAAyB,CAAE,GAAI,eAAM,CAAa,GAEjD,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,IAAI,WAAM,eAAU,CAAa,EAC9D,CAAE,MAAO,EAAQ,CAEf,OADA,QAAQ,KAAK,CAAC,sBAAuB,GAC9B,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,gBAAiB,EAAG,CAAE,OAAQ,GAAI,EACtE,CACF,CCvRA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,2BACN,SAAU,qBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,+CAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,uBACA,CACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,2BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,CAAE,sBAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,EAAQ,EAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,GACgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAS,AAAT,IACT,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,EACA,oBACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,gBAAgB,CAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAA2E,AAAxD,MAAC,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,WAAY,EAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,CAAE,oBAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAyB,AAAzB,EAA0B,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,AAA8C,SAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,YACV,SACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,oBACZ,EACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,GAAK,GAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAAQ,AADqC,GAAG,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAAkB,AAC7F,EAAQ,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [1]}