module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},9606,(e,t,r)=>{},30450,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>A,"routeModule",()=>E,"serverHooks",()=>b,"workAsyncStorage",()=>y,"workUnitAsyncStorage",()=>C],30450);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),p=e.i(47587),d=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),R=e.i(93695);e.i(52474);var v=e.i(220);e.s(["POST",()=>f],11615);var m=e.i(89171),g=e.i(65800);async function f(e){try{let{email:t,password:r}=await e.json();if(!t||!r)return m.NextResponse.json({error:"Email and password are required"},{status:400});let a=await (0,g.signIn)(t,r);if(!a)return m.NextResponse.json({error:"Invalid credentials"},{status:401});let n=m.NextResponse.json({success:!0,user:a.user});return n.cookies.set("auth-token",a.token,{httpOnly:!0,path:"/",sameSite:"lax",secure:!0,maxAge:604800}),n}catch(e){return m.NextResponse.json({error:"Server error"},{status:500})}}var w=e.i(11615);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/auth/login/route.ts",nextConfigOutput:"export",userland:w}),{workAsyncStorage:y,workUnitAsyncStorage:C,serverHooks:b}=E;function A(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:C})}async function q(e,t,a){var m;let g="/api/auth/login/route";g=g.replace(/\/index$/,"")||"/";let f=await E.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:C,isDraftMode:b,prerenderManifest:A,routerServerContext:q,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,resolvedPathname:k}=f,P=(0,o.normalizeAppPath)(g),T=!!(A.dynamicRoutes[P]||A.routes[k]);if(T&&!b){let e=!!A.routes[k],t=A.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new R.NoFallbackError}let O=null;!T||E.isDev||b||(O="/index"===(O=k)?"/":O);let S=!0===E.isDev||!T,_=T&&!S,H=e.method||"GET",I=(0,s.getTracer)(),U=I.getActiveScopeSpan(),M={params:y,prerenderManifest:A,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(m=C.experimental)?void 0:m.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,q)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>E.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=I.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${H} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async s=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!T)return await (0,d.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[h.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})},q),t}},R=await E.handleResponse({req:e,nextConfig:C,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:A,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,responseGenerator:u,waitUntil:a.waitUntil});if(!T)return null;if((null==R||null==(i=R.value)?void 0:i.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==R||null==(l=R.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),b&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,c.fromNodeOutgoingHttpHeaders)(R.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||m.delete(h.NEXT_CACHE_TAGS_HEADER),!R.cacheControl||t.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,x.getCacheControlHeader)(R.cacheControl)),await (0,d.sendResponse)(D,$,new Response(R.value.body,{headers:m,status:R.value.status||200})),null};U?await i(U):await I.withPropagatedContext(e.headers,()=>I.trace(u.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(U||t instanceof R.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})}),T)throw t;return await (0,d.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__510243b8._.js.map