module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},621,(e,t,r)=>{},77981,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>q,"routeModule",()=>C,"serverHooks",()=>A,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>b],77981);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),p=e.i(95169),u=e.i(47587),d=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["GET",()=>y,"runtime",()=>w],50364);var v=e.i(89171),f=e.i(15270),g=e.i(65800);let w="nodejs";async function y(e){let t=e.cookies.get("auth-token")?.value,r=t?(0,g.verifyToken)(t):null;if(!r)return v.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url);if("1"===a.get("all")&&"ADMIN"===r.role){let e=await f.prisma.project.findMany({where:{organizationId:r.organizationId},orderBy:{name:"asc"}});return v.NextResponse.json({projects:e})}let n=(await f.prisma.userProject.findMany({where:{userId:r.id},select:{project:{select:{id:!0,name:!0}}}})).map(e=>e.project);return v.NextResponse.json({projects:n})}var E=e.i(50364);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/projects/route.ts",nextConfigOutput:"export",userland:E}),{workAsyncStorage:j,workUnitAsyncStorage:b,serverHooks:A}=C;function q(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:b})}async function N(e,t,a){var v;let f="/api/projects/route";f=f.replace(/\/index$/,"")||"/";let g=await C.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:j,prerenderManifest:b,routerServerContext:A,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,resolvedPathname:T}=g,k=(0,o.normalizeAppPath)(f),P=!!(b.dynamicRoutes[k]||b.routes[T]);if(P&&!j){let e=!!b.routes[T],t=b.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!P||C.isDev||j||(O="/index"===(O=T)?"/":O);let _=!0===C.isDev||!P,I=P&&!_,S=e.method||"GET",U=(0,s.getTracer)(),H=U.getActiveScopeSpan(),M={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:I,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>C.onRequestError(e,t,a,A)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>C.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${S} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async s=>{var i,l;let p=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let p=M.renderOpts.collectedTags;if(!P)return await (0,d.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);p&&(t[h.NEXT_CACHE_TAGS_HEADER]=p),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:q})},A),t}},m=await C.handleResponse({req:e,nextConfig:E,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,responseGenerator:p,waitUntil:a.waitUntil});if(!P)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,d.sendResponse)(D,$,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};H?await i(H):await U.withPropagatedContext(e.headers,()=>U.trace(p.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(H||t instanceof m.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:q})}),P)throw t;return await (0,d.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__0abefd3a._.js.map