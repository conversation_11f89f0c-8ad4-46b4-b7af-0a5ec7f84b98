(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function a(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,a(e));else t.set(r,a(n));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return s},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return o},formatWithValidation:function(){return l},urlObjectKeys:function(){return s}});let n=e.r(90809)._(e.r(98183)),a=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",s=e.pathname||"",l=e.hash||"",i=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let c=e.search||i&&"?"+i||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||a.test(o))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return o(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return a}});let n=e.r(71645);function a(e,t){let r=(0,n.useRef)(null),a=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=o(e,n)),t&&(a.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return x},NormalizeError:function(){return g},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return m},WEB_VITALS:function(){return n},execOnce:function(){return a},getDisplayName:function(){return u},getLocationOrigin:function(){return l},getURL:function(){return i},isAbsoluteUrl:function(){return s},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function a(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];return r||(r=!0,t=e(...a)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>o.test(e);function l(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function i(){let{href:e}=window.location,t=l();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,m=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=e.r(18967),a=e.r(52817);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,a.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return g},useLinkStatus:function(){return x}});let n=e.r(90809),a=e.r(43476),o=n._(e.r(71645)),s=e.r(95057),l=e.r(8372),i=e.r(18581),u=e.r(18967),c=e.r(5550);e.r(33525);let d=e.r(91949),f=e.r(73668),p=e.r(99781);e.r(84508);let m=e.r(65165);function h(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){var t;let r,n,s,[g,x]=(0,o.useOptimistic)(d.IDLE_LINK_STATUS),b=(0,o.useRef)(null),{href:v,as:j,children:N,prefetch:C=null,passHref:P,replace:w,shallow:O,scroll:_,onClick:S,onMouseEnter:E,onTouchStart:T,legacyBehavior:R=!1,onNavigate:A,ref:I,unstable_dynamicOnHover:k,...M}=e;r=N,R&&("string"==typeof r||"number"==typeof r)&&(r=(0,a.jsx)("a",{children:r}));let L=o.default.useContext(l.AppRouterContext),U=!1!==C,F=!1!==C?null===(t=C)||"auto"===t?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:D,as:B}=o.default.useMemo(()=>{let e=h(v);return{href:e,as:j?h(j):e}},[v,j]);R&&(n=o.default.Children.only(r));let K=R?n&&"object"==typeof n&&n.ref:I,q=o.default.useCallback(e=>(null!==L&&(b.current=(0,d.mountLinkInstance)(e,D,L,F,U,x)),()=>{b.current&&((0,d.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,d.unmountPrefetchableInstance)(e)}),[U,D,L,F,x]),z={ref:(0,i.useMergedRef)(q,K),onClick(e){R||"function"!=typeof S||S(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,r,n,a,s,l){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){a&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}o.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,a?"replace":"push",null==s||s,n.current)})}}(e,D,B,b,w,_,A))},onMouseEnter(e){R||"function"!=typeof E||E(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===k)},onTouchStart:function(e){R||"function"!=typeof T||T(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&U&&(0,d.onNavigationIntent)(e.currentTarget,!0===k)}};return(0,u.isAbsoluteUrl)(B)?z.href=B:R&&!P&&("a"!==n.type||"href"in n.props)||(z.href=(0,c.addBasePath)(B)),s=R?o.default.cloneElement(n,z):(0,a.jsx)("a",{...M,...z,children:r}),(0,a.jsx)(y.Provider,{value:g,children:s})}let y=(0,o.createContext)(d.IDLE_LINK_STATUS),x=()=>(0,o.useContext)(y);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},67881,e=>{"use strict";e.s(["Button",()=>a]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let a=r.forwardRef((e,r)=>{let{className:a,variant:o="default",size:s="default",...l}=e;return(0,t.jsx)("button",{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[o],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[s],a),ref:r,...l})});a.displayName="Button"},23750,e=>{"use strict";e.s(["Input",()=>a]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let a=r.forwardRef((e,r)=>{let{className:a,type:o,...s}=e;return(0,t.jsx)("input",{type:o,className:(0,n.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:r,...s})});a.displayName="Input"},18566,(e,t,r)=>{t.exports=e.r(76562)},70065,e=>{"use strict";e.s(["Card",()=>a,"CardContent",()=>i,"CardDescription",()=>l,"CardHeader",()=>o,"CardTitle",()=>s]);var t=e.i(43476),r=e.i(71645),n=e.i(47163);let a=r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",a),...o})});a.displayName="Card";let o=r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...o})});o.displayName="CardHeader";let s=r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...o})});s.displayName="CardTitle";let l=r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-gray-600",a),...o})});l.displayName="CardDescription";let i=r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",a),...o})});i.displayName="CardContent",r.forwardRef((e,r)=>{let{className:a,...o}=e;return(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",a),...o})}).displayName="CardFooter"},5702,e=>{"use strict";e.s(["default",()=>i]);var t=e.i(43476),r=e.i(71645),n=e.i(18566),a=e.i(22016),o=e.i(67881),s=e.i(23750),l=e.i(70065);function i(){let[e,i]=(0,r.useState)(""),[u,c]=(0,r.useState)(""),[d,f]=(0,r.useState)(""),[p,m]=(0,r.useState)(!1),[h,g]=(0,r.useState)(""),[y,x]=(0,r.useState)(!1),b=(0,n.useRouter)(),v=async e=>{e.preventDefault(),m(!0),g("");try{x(!0),setTimeout(()=>{b.push("/auth/login")},1500)}catch(e){g("An unexpected error occurred")}finally{m(!1)}};return y?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)(l.Card,{className:"max-w-md w-full",children:[(0,t.jsxs)(l.CardHeader,{children:[(0,t.jsx)(l.CardTitle,{className:"text-green-600",children:"Account Created!"}),(0,t.jsx)(l.CardDescription,{children:"Your account has been created successfully. You can now sign in."})]}),(0,t.jsx)(l.CardContent,{children:(0,t.jsx)(o.Button,{onClick:()=>b.push("/auth/login"),className:"w-full",children:"Go to Sign In"})})]})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Join our ticketing portal"})]}),(0,t.jsxs)(l.Card,{children:[(0,t.jsxs)(l.CardHeader,{children:[(0,t.jsx)(l.CardTitle,{children:"Get started"}),(0,t.jsx)(l.CardDescription,{children:"Create your account to start managing tickets"})]}),(0,t.jsx)(l.CardContent,{children:(0,t.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[h&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:h}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsx)(s.Input,{id:"fullName",type:"text",value:d,onChange:e=>f(e.target.value),required:!0,className:"mt-1",placeholder:"Enter your full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,t.jsx)(s.Input,{id:"email",type:"email",value:e,onChange:e=>i(e.target.value),required:!0,className:"mt-1",placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsx)(s.Input,{id:"password",type:"password",value:u,onChange:e=>c(e.target.value),required:!0,className:"mt-1",placeholder:"Create a password",minLength:6}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 6 characters long"})]}),(0,t.jsx)(o.Button,{type:"submit",disabled:p,className:"w-full",children:p?"Creating account...":"Create account"}),(0,t.jsxs)("div",{className:"text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)(a.default,{href:"/auth/login",className:"text-blue-600 hover:text-blue-500",children:"Sign in"})]})]})})]})]})})}}]);