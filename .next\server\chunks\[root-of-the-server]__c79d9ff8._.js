module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},57845,(e,t,r)=>{},44941,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>N,"routeModule",()=>C,"serverHooks",()=>A,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>b],44941);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),i=e.i(61916),s=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),p=e.i(47587),u=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),f=e.i(93695);e.i(52474);var h=e.i(220);e.s(["POST",()=>E,"runtime",()=>w],68311);var g=e.i(89171),R=e.i(15270),v=e.i(65800);let w="nodejs";async function E(e){try{let t,r,a=e.cookies.get("auth-token")?.value,n=a?(0,v.verifyToken)(a):null;if(!n)return g.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==n.role)return g.NextResponse.json({error:"Forbidden"},{status:403});let i=await e.json().catch(()=>null),s=String(i?.to||"").trim().toLowerCase(),o=i?.subject?String(i.subject):void 0;if(!s)return g.NextResponse.json({error:"Missing to"},{status:400});let l=await R.prisma.organization.findUnique({where:{id:n.organizationId},select:{id:!0,settings:!0,name:!0}}),d=l?.settings?.emailConfig||{};if(d?.defaultProjectId)t=d.defaultProjectId;else{let e=await R.prisma.project.findFirst({where:{organizationId:l.id},select:{id:!0},orderBy:{createdAt:"asc"}});t=e?.id}let p=d?.routingMap;if(p&&p[s]){let e=p[s];if(/^c[a-z0-9]{20,}$/i.test(e))r=e;else{let t=await R.prisma.department.findFirst({where:{organizationId:l.id,name:e},select:{id:!0}});t&&(r=t.id)}}if(!r&&o){let e=/\[(?:DEPT|DEPARTMENT):\s*([^\]]+)\]/i.exec(o),t=e?.[1]?.trim();if(t){let e=await R.prisma.department.findFirst({where:{organizationId:l.id,name:t},select:{id:!0}});e&&(r=e.id)}}if(!r&&d?.defaultDepartmentId&&(r=d.defaultDepartmentId),!r){let e=await R.prisma.department.findFirst({where:{organizationId:l.id,name:"General Support"},select:{id:!0}});e&&(r=e.id)}if(!r){let e=await R.prisma.department.findFirst({where:{organizationId:l.id},select:{id:!0},orderBy:{createdAt:"asc"}});r=e?.id}let u=r?await R.prisma.department.findUnique({where:{id:r},select:{id:!0,name:!0}}):null,c=t?await R.prisma.project.findUnique({where:{id:t},select:{id:!0,name:!0}}):null;return g.NextResponse.json({ok:!0,to:s,resolved:{departmentId:u?.id,departmentName:u?.name,projectId:c?.id,projectName:c?.name}})}catch(e){return console.error("email config test error",e),g.NextResponse.json({error:"Internal error"},{status:500})}}var y=e.i(68311);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/email/config/test/route",pathname:"/api/email/config/test",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/email/config/test/route.ts",nextConfigOutput:"export",userland:y}),{workAsyncStorage:j,workUnitAsyncStorage:b,serverHooks:A}=C;function N(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:b})}async function q(e,t,a){var g;let R="/api/email/config/test/route";R=R.replace(/\/index$/,"")||"/";let v=await C.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:y,isDraftMode:j,prerenderManifest:b,routerServerContext:A,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,resolvedPathname:I}=v,P=(0,s.normalizeAppPath)(R),T=!!(b.dynamicRoutes[P]||b.routes[I]);if(T&&!j){let e=!!b.routes[I],t=b.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new f.NoFallbackError}let k=null;!T||C.isDev||j||(k="/index"===(k=I)?"/":k);let O=!0===C.isDev||!T,S=T&&!O,U=e.method||"GET",_=(0,i.getTracer)(),M=_.getActiveScopeSpan(),D={params:E,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=y.experimental)?void 0:g.cacheLife,isRevalidate:S,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>C.onRequestError(e,t,a,A)},sharedContext:{buildId:w}},H=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let s=async r=>C.handle(z,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async i=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await s(i);e.fetchMetrics=D.renderOpts.fetchMetrics;let l=D.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=D.renderOpts.collectedTags;if(!T)return await (0,u.sendResponse)(H,F,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,a=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:N})},A),t}},f=await C.handleResponse({req:e,nextConfig:y,cacheKey:k,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:a.waitUntil});if(!T)return null;if((null==f||null==(o=f.value)?void 0:o.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==f||null==(l=f.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":f.isMiss?"MISS":f.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(f.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||g.delete(m.NEXT_CACHE_TAGS_HEADER),!f.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,x.getCacheControlHeader)(f.cacheControl)),await (0,u.sendResponse)(H,F,new Response(f.value.body,{headers:g,status:f.value.status||200})),null};M?await o(M):await _.withPropagatedContext(e.headers,()=>_.trace(d.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(M||t instanceof f.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:N})}),T)throw t;return await (0,u.sendResponse)(H,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__c79d9ff8._.js.map