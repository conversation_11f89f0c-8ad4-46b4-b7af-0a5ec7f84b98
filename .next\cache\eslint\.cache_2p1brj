[{"d:\\wecare (2)\\wecare\\ticketing-portal\\components\\dashboard\\header.tsx": "1", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\dashboard\\sidebar.tsx": "2", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\LogoutButton.tsx": "3", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\new-ticket-form.tsx": "4", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\ticket-filters.tsx": "5", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\ticket-list.tsx": "6", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\button.tsx": "7", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\card.tsx": "8", "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\input.tsx": "9", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\auth-new.ts": "10", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\auth.ts": "11", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\permissions.ts": "12", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\prisma.ts": "13", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\supabase.ts": "14", "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\utils.ts": "15", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\audit\\users\\route.ts": "16", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\auth\\login\\route.ts": "17", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\auth\\logout\\route.ts": "18", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\departments\\route.ts": "19", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\departments\\[id]\\route.ts": "20", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\route.ts": "21", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\test\\route.ts": "22", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\test\\send-ack\\route.ts": "23", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\inbound\\route.ts": "24", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\me\\route.ts": "25", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\projects\\route.ts": "26", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\route.ts": "27", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\[id]\\messages\\route.ts": "28", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\[id]\\route.ts": "29", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\users\\route.ts": "30", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\users\\[id]\\route.ts": "31", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\auth\\login\\page.tsx": "32", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\auth\\signup\\page.tsx": "33", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\departments\\DepartmentManager.tsx": "34", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\departments\\page.tsx": "35", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\email-config\\EmailConfigForm.tsx": "36", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\email-config\\page.tsx": "37", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\layout.tsx": "38", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\page.tsx": "39", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\reports\\page.tsx": "40", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\new\\page.tsx": "41", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\page.tsx": "42", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\MessageComposer.tsx": "43", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\page.tsx": "44", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\TicketControls.tsx": "45", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\CreateUserForm.tsx": "46", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\page.tsx": "47", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\UserAdminList.tsx": "48", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\UserAuditPanel.tsx": "49", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\layout.tsx": "50", "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\page.tsx": "51"}, {"size": 4143, "mtime": 1757577903000, "results": "52", "hashOfConfig": "53"}, {"size": 3540, "mtime": 1757586000000, "results": "54", "hashOfConfig": "53"}, {"size": 442, "mtime": 1757496304000, "results": "55", "hashOfConfig": "53"}, {"size": 6122, "mtime": 1757578114000, "results": "56", "hashOfConfig": "53"}, {"size": 6973, "mtime": 1757484850000, "results": "57", "hashOfConfig": "53"}, {"size": 7853, "mtime": 1757578196000, "results": "58", "hashOfConfig": "53"}, {"size": 1570, "mtime": 1757485228000, "results": "59", "hashOfConfig": "53"}, {"size": 1878, "mtime": 1757485262000, "results": "60", "hashOfConfig": "53"}, {"size": 794, "mtime": 1757485239000, "results": "61", "hashOfConfig": "53"}, {"size": 1261, "mtime": 1757578291000, "results": "62", "hashOfConfig": "53"}, {"size": 3246, "mtime": 1757485650000, "results": "63", "hashOfConfig": "53"}, {"size": 1167, "mtime": 1757583464000, "results": "64", "hashOfConfig": "53"}, {"size": 302, "mtime": 1757491314000, "results": "65", "hashOfConfig": "53"}, {"size": 3692, "mtime": 1757485634000, "results": "66", "hashOfConfig": "53"}, {"size": 1361, "mtime": 1757484448000, "results": "67", "hashOfConfig": "53"}, {"size": 1243, "mtime": 1757573552000, "results": "68", "hashOfConfig": "53"}, {"size": 925, "mtime": 1757491333000, "results": "69", "hashOfConfig": "53"}, {"size": 276, "mtime": 1757491347000, "results": "70", "hashOfConfig": "53"}, {"size": 2299, "mtime": 1757584243000, "results": "71", "hashOfConfig": "53"}, {"size": 2629, "mtime": 1757577724000, "results": "72", "hashOfConfig": "53"}, {"size": 2728, "mtime": 1757586025000, "results": "73", "hashOfConfig": "53"}, {"size": 3466, "mtime": 1757586476000, "results": "74", "hashOfConfig": "53"}, {"size": 3343, "mtime": 1757586977000, "results": "75", "hashOfConfig": "53"}, {"size": 11643, "mtime": 1757586138000, "results": "76", "hashOfConfig": "53"}, {"size": 418, "mtime": 1757491351000, "results": "77", "hashOfConfig": "53"}, {"size": 980, "mtime": 1757498461000, "results": "78", "hashOfConfig": "53"}, {"size": 6889, "mtime": 1757584313000, "results": "79", "hashOfConfig": "53"}, {"size": 1554, "mtime": 1757577828000, "results": "80", "hashOfConfig": "53"}, {"size": 8309, "mtime": 1757582821000, "results": "81", "hashOfConfig": "53"}, {"size": 4720, "mtime": 1757583149000, "results": "82", "hashOfConfig": "53"}, {"size": 5377, "mtime": 1757582920000, "results": "83", "hashOfConfig": "53"}, {"size": 4510, "mtime": 1757491391000, "results": "84", "hashOfConfig": "53"}, {"size": 5070, "mtime": 1757578413000, "results": "85", "hashOfConfig": "53"}, {"size": 4902, "mtime": 1757501723000, "results": "86", "hashOfConfig": "53"}, {"size": 1053, "mtime": 1757582838000, "results": "87", "hashOfConfig": "53"}, {"size": 11915, "mtime": 1757587034000, "results": "88", "hashOfConfig": "53"}, {"size": 1153, "mtime": 1757588417000, "results": "89", "hashOfConfig": "53"}, {"size": 1415, "mtime": 1757496919000, "results": "90", "hashOfConfig": "53"}, {"size": 6608, "mtime": 1757588447000, "results": "91", "hashOfConfig": "53"}, {"size": 11814, "mtime": 1757588355000, "results": "92", "hashOfConfig": "53"}, {"size": 7313, "mtime": 1757584257000, "results": "93", "hashOfConfig": "53"}, {"size": 8043, "mtime": 1758714119673, "results": "94", "hashOfConfig": "53"}, {"size": 1601, "mtime": 1757491676000, "results": "95", "hashOfConfig": "53"}, {"size": 5878, "mtime": 1757584756000, "results": "96", "hashOfConfig": "53"}, {"size": 5642, "mtime": 1757573542000, "results": "97", "hashOfConfig": "53"}, {"size": 5542, "mtime": 1757503523000, "results": "98", "hashOfConfig": "53"}, {"size": 972, "mtime": 1757573578000, "results": "99", "hashOfConfig": "53"}, {"size": 8293, "mtime": 1758714171894, "results": "100", "hashOfConfig": "53"}, {"size": 1691, "mtime": 1757582319000, "results": "101", "hashOfConfig": "53"}, {"size": 689, "mtime": 1757484250000, "results": "102", "hashOfConfig": "53"}, {"size": 167, "mtime": 1757485185000, "results": "103", "hashOfConfig": "53"}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "13pmm9g", {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\dashboard\\header.tsx", ["257"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\dashboard\\sidebar.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\LogoutButton.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\new-ticket-form.tsx", ["258", "259", "260"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\ticket-filters.tsx", ["261"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\tickets\\ticket-list.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\button.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\card.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\components\\ui\\input.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\auth-new.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\auth.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\permissions.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\prisma.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\supabase.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\lib\\utils.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\audit\\users\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\auth\\login\\route.ts", ["262"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\auth\\logout\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\departments\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\departments\\[id]\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\test\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\config\\test\\send-ack\\route.ts", ["263", "264"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\email\\inbound\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\me\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\projects\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\[id]\\messages\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\tickets\\[id]\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\users\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\api\\users\\[id]\\route.ts", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\auth\\login\\page.tsx", ["265"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\auth\\signup\\page.tsx", ["266"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\departments\\DepartmentManager.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\departments\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\email-config\\EmailConfigForm.tsx", ["267"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\email-config\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\layout.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\reports\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\new\\page.tsx", ["268", "269"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\MessageComposer.tsx", ["270"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\tickets\\[id]\\TicketControls.tsx", ["271", "272", "273"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\CreateUserForm.tsx", ["274"], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\page.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\UserAdminList.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\dashboard\\users\\UserAuditPanel.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\layout.tsx", [], [], "d:\\wecare (2)\\wecare\\ticketing-portal\\src\\app\\page.tsx", [], [], {"ruleId": "275", "severity": 1, "message": "276", "line": 18, "column": 26, "nodeType": null, "messageId": "277", "endLine": 18, "endColumn": 30}, {"ruleId": "275", "severity": 1, "message": "278", "line": 25, "column": 33, "nodeType": null, "messageId": "277", "endLine": 25, "endColumn": 40}, {"ruleId": "275", "severity": 1, "message": "279", "line": 41, "column": 27, "nodeType": null, "messageId": "277", "endLine": 41, "endColumn": 31}, {"ruleId": "275", "severity": 1, "message": "280", "line": 48, "column": 14, "nodeType": null, "messageId": "277", "endLine": 48, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "281", "line": 7, "column": 20, "nodeType": null, "messageId": "277", "endLine": 7, "endColumn": 29}, {"ruleId": "275", "severity": 1, "message": "280", "line": 26, "column": 12, "nodeType": null, "messageId": "277", "endLine": 26, "endColumn": 15}, {"ruleId": null, "message": "282", "line": 30, "column": 7, "severity": 1, "nodeType": null, "fix": "283"}, {"ruleId": "275", "severity": 1, "message": "284", "line": 32, "column": 14, "nodeType": null, "messageId": "277", "endLine": 32, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "280", "line": 35, "column": 14, "nodeType": null, "messageId": "277", "endLine": 35, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "280", "line": 31, "column": 14, "nodeType": null, "messageId": "277", "endLine": 31, "endColumn": 17}, {"ruleId": "275", "severity": 1, "message": "285", "line": 3, "column": 21, "nodeType": null, "messageId": "277", "endLine": 3, "endColumn": 28}, {"ruleId": "286", "severity": 1, "message": "287", "line": 60, "column": 6, "nodeType": "288", "endLine": 60, "endColumn": 8, "suggestions": "289"}, {"ruleId": "275", "severity": 1, "message": "284", "line": 102, "column": 14, "nodeType": null, "messageId": "277", "endLine": 102, "endColumn": 15}, {"ruleId": "275", "severity": 1, "message": "284", "line": 28, "column": 14, "nodeType": null, "messageId": "277", "endLine": 28, "endColumn": 15}, {"ruleId": "286", "severity": 1, "message": "290", "line": 34, "column": 6, "nodeType": "288", "endLine": 34, "endColumn": 12, "suggestions": "291"}, {"ruleId": "286", "severity": 1, "message": "292", "line": 58, "column": 6, "nodeType": "288", "endLine": 58, "endColumn": 45, "suggestions": "293"}, {"ruleId": "275", "severity": 1, "message": "284", "line": 81, "column": 14, "nodeType": null, "messageId": "277", "endLine": 81, "endColumn": 15}, {"ruleId": "286", "severity": 1, "message": "287", "line": 48, "column": 6, "nodeType": "288", "endLine": 48, "endColumn": 8, "suggestions": "294"}, "@typescript-eslint/no-unused-vars", "'user' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "'profile' is defined but never used. Allowed unused args must match /^_/u.", "'data' is defined but never used. Allowed unused args must match /^_/u.", "'err' is defined but never used.", "'useEffect' is defined but never used. Allowed unused vars must match /^_/u.", "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-var-requires').", {"range": "295", "text": "296"}, "'e' is defined but never used.", "'useMemo' is defined but never used. Allowed unused vars must match /^_/u.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'error'. Either include it or remove the dependency array.", "ArrayExpression", ["297"], "React Hook useEffect has a missing dependency: 'initial.departmentId'. Either include it or remove the dependency array.", ["298"], "React Hook useEffect has a missing dependency: 'assignedToId'. Either include it or remove the dependency array.", ["299"], ["300"], [1400, 1462], " ", {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "301", "fix": "307"}, "Update the dependencies array to be: [error]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [initial.departmentId, role]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [assignedToId, departmentId, initial.projectId, role]", {"range": "312", "text": "313"}, {"range": "314", "text": "309"}, [2091, 2093], "[error]", [1444, 1450], "[initial.departmentId, role]", [2381, 2420], "[assignedToId, departmentId, initial.projectId, role]", [1658, 1660]]