module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},29432,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},77341,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],77341);var b,c=a.i(84513);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(21751),a.i(75164),a.i(18970),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(30106),h=a.i(36984);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(20460);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},41763,a=>{"use strict";a.s(["normalizeAppPath",()=>c],41763);var b=a.i(32885);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},54451,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(21751);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},62212,a=>{a.n(a.i(66114))},15843,a=>{a.n(a.i(18357))},2316,a=>{"use strict";a.s(["getUserPermissions",()=>d,"hasPermission",()=>e]);var b=a.i(31191);let c=["DASHBOARD_VIEW","TICKETS_VIEW","TICKETS_UPDATE","TICKETS_ASSIGN","REPORTS_VIEW","DEPARTMENTS_MANAGE","PROJECTS_MANAGE","AUDIT_VIEW"];async function d(a){return new Set("ADMIN"===a.role?c:(await b.prisma.userPermission.findMany({where:{userId:a.id,organizationId:a.organizationId},select:{permission:!0}})).map(a=>a.permission))}function e(a,b){return a.has(b)}},96669,(a,b,c)=>{},99123,a=>{"use strict";a.s(["default",()=>i]);var b=a.i(7997),c=a.i(5246);a.i(70396);var d=a.i(73727),e=a.i(31191),f=a.i(14929),g=a.i(2316);function h(a){return Math.round(10*a)/10}async function i(){let a=await (0,c.cookies)(),i=a.get("auth-token")?.value,j=i?(0,f.verifyToken)(i):null;if(j||(0,d.redirect)("/auth/login"),"ADMIN"!==j.role){let a=await (0,g.getUserPermissions)({id:j.id,role:j.role,organizationId:j.organizationId});if(!(0,g.hasPermission)(a,"TICKETS_VIEW"))return(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,b.jsx)("div",{className:"max-w-3xl mx-auto bg-white rounded-lg shadow p-6",children:"Forbidden"})})}let k=(await e.prisma.userProject.findMany({where:{userId:j.id},select:{projectId:!0}})).map(a=>a.projectId),l=(await e.prisma.userDepartment.findMany({where:{userId:j.id},select:{departmentId:!0}})).map(a=>a.departmentId),m="ADMIN"===j.role?{organizationId:j.organizationId}:{organizationId:j.organizationId,projectId:{in:k.length?k:["__none__"]},departmentId:{in:l.length?l:["__none__"]}},[n,o,p,q,r,s,t]=await Promise.all([e.prisma.ticket.count({where:m}),e.prisma.ticket.count({where:{...m,status:"OPEN"}}),e.prisma.ticket.count({where:{...m,status:"IN_PROGRESS"}}),e.prisma.ticket.count({where:{...m,status:"CLOSED"}}),e.prisma.ticket.count({where:{...m,status:"CANCELLED"}}),e.prisma.ticket.count({where:{...m,status:"RESOLVED"}}),e.prisma.ticket.findMany({where:m,include:{createdBy:{select:{fullName:!0,email:!0}}},orderBy:{createdAt:"desc"},take:5})]),[u,v]=await Promise.all([e.prisma.ticket.findMany({where:{...m,OR:[{status:"RESOLVED"},{status:"CLOSED"}],resolvedAt:{not:null}},select:{createdAt:!0,resolvedAt:!0},orderBy:{resolvedAt:"desc"},take:200}),e.prisma.ticket.findMany({where:{...m,status:{in:["OPEN","IN_PROGRESS","WAITING_FOR_CUSTOMER","WAITING_FOR_AGENT"]}},select:{createdAt:!0},orderBy:{createdAt:"desc"},take:200})]),w=u.map(a=>(a.resolvedAt.getTime()-a.createdAt.getTime())/36e5).filter(a=>a>=0).sort((a,b)=>a-b),x=w.length?h(w.reduce((a,b)=>a+b,0)/w.length):0,y=w.length?h(w[Math.floor(w.length/2)]):0,z=Date.now(),A=v.map(a=>(z-a.createdAt.getTime())/36e5).filter(a=>a>=0),B=A.length?h(A.reduce((a,b)=>a+b,0)/A.length):0;return(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",j.fullName||j.email]}),(0,b.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Here is the latest on your tickets."})]}),(0,b.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[{label:"Open",value:o},{label:"WIP",value:p},{label:"Closed",value:q},{label:"Discarded",value:r},{label:"Total",value:n}].map(a=>(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("div",{className:"text-sm text-gray-500",children:a.label}),(0,b.jsx)("div",{className:"text-2xl font-bold",children:a.value})]},a.label))}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"TAT (Resolved/Closed)"}),(0,b.jsxs)("div",{className:"text-2xl font-bold",children:[x,"h"]}),(0,b.jsxs)("div",{className:"text-xs text-gray-500",children:["Median: ",y,"h"]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"Average age (Open/WIP)"}),(0,b.jsxs)("div",{className:"text-2xl font-bold",children:[B,"h"]}),(0,b.jsxs)("div",{className:"text-xs text-gray-500",children:["Active: ",v.length]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow p-4",children:[(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"Resolved (count)"}),(0,b.jsx)("div",{className:"text-2xl font-bold",children:s}),(0,b.jsxs)("div",{className:"text-xs text-gray-500",children:["Last ",u.length," used for TAT"]})]})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsx)("div",{className:"p-4 border-b",children:(0,b.jsx)("h2",{className:"font-semibold",children:"Recent Tickets"})}),(0,b.jsx)("ul",{className:"divide-y",children:0===t.length?(0,b.jsx)("li",{className:"p-4 text-sm text-gray-500",children:"No recent tickets in your departments."}):t.map(a=>(0,b.jsxs)("li",{className:"p-4 flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"font-medium text-blue-700",children:a.ticketNumber}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:a.title}),(0,b.jsx)("div",{className:"text-xs text-gray-500",children:a.createdBy.fullName||a.createdBy.email})]}),(0,b.jsx)("span",{className:"text-xs rounded-full px-2 py-1 bg-gray-100 text-gray-700",children:a.status})]},a.id))})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__603b8a0e._.js.map