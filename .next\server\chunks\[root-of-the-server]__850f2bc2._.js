module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},92321,(e,t,r)=>{},9262,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>N,"routeModule",()=>b,"serverHooks",()=>T,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>C],9262);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),o=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),p=e.i(47587),d=e.i(66012),c=e.i(70101),m=e.i(26937),x=e.i(10372),h=e.i(93695);e.i(52474);var f=e.i(220);e.s(["POST",()=>w,"runtime",()=>y],93358);var g=e.i(89171),v=e.i(15270),R=e.i(65800);let y="nodejs";async function w(t){try{let r,a=t.cookies.get("auth-token")?.value,n=a?(0,R.verifyToken)(a):null;if(!n)return g.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==n.role)return g.NextResponse.json({error:"Forbidden"},{status:403});let s=await t.json().catch(()=>null),o=String(s?.to||"").trim(),i=s?.ticketNumber?String(s.ticketNumber):`TKT-TEST-${Math.random().toString(36).slice(2,8).toUpperCase()}`;if(!o)return g.NextResponse.json({error:"Missing to"},{status:400});let l=await v.prisma.organization.findUnique({where:{id:n.organizationId},select:{name:!0,settings:!0}}),u=l?.settings?.emailConfig||{},p=u?.smtp||{};if(!p.host||!p.port||!p.fromEmail)return g.NextResponse.json({error:"SMTP configuration incomplete. Please set host, port, fromEmail (and auth if required)."},{status:400});try{r=(await e.A(9390)).default||await e.A(9390)}catch(e){return g.NextResponse.json({error:"nodemailer is not installed. Please run: npm install nodemailer"},{status:501})}let d=r.createTransport({host:p.host,port:Number(p.port),secure:!!p.secure,auth:p.user&&p.pass?{user:p.user,pass:p.pass}:void 0}),c=p.fromName||l?.name||"WeCare Support",m=p.fromEmail,x=`WeCare Ticket Acknowledgement — ${i}`,h=`Hello,

This is a test acknowledgement email from WeCare.
Ticket number: ${i}

We have received your request and will get back to you shortly.
Please reference ${i} in future communications.

— ${c}`,f=`<!doctype html><html><body style="font-family:system-ui,Segoe UI,Roboto,Arial,sans-serif;color:#0f172a"><p>Hello,</p><p>This is a <strong>test acknowledgement</strong> email from WeCare.</p><p><strong>Ticket number:</strong> ${i}</p><p>We have received your request and will get back to you shortly.<br/>Please reference <strong>${i}</strong> in future communications.</p><p>— ${c}</p></body></html>`,y=await d.sendMail({from:{name:c,address:m},to:o,subject:x,text:h,html:f});return g.NextResponse.json({ok:!0,messageId:y?.messageId})}catch(e){return console.error("send test ack error",e),g.NextResponse.json({error:"Internal error"},{status:500})}}var E=e.i(93358);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/email/config/test/send-ack/route",pathname:"/api/email/config/test/send-ack",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/email/config/test/send-ack/route.ts",nextConfigOutput:"export",userland:E}),{workAsyncStorage:k,workUnitAsyncStorage:C,serverHooks:T}=b;function N(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:C})}async function A(e,t,a){var g;let v="/api/email/config/test/send-ack/route";v=v.replace(/\/index$/,"")||"/";let R=await b.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:y,params:w,nextConfig:E,isDraftMode:k,prerenderManifest:C,routerServerContext:T,isOnDemandRevalidate:N,revalidateOnlyGenerated:A,resolvedPathname:j}=R,q=(0,o.normalizeAppPath)(v),P=!!(C.dynamicRoutes[q]||C.routes[j]);if(P&&!k){let e=!!C.routes[j],t=C.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let S=null;!P||b.isDev||k||(S="/index"===(S=j)?"/":S);let _=!0===b.isDev||!P,O=P&&!_,I=e.method||"GET",U=(0,s.getTracer)(),H=U.getActiveScopeSpan(),M={params:w,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=E.experimental)?void 0:g.cacheLife,isRevalidate:O,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,T)},sharedContext:{buildId:y}},$=new i.NodeNextRequest(e),D=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest($,(0,l.signalFromNodeResponse)(t));try{let o=async r=>b.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),i=async s=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!P)return await (0,d.sendResponse)($,D,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[x.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:N})},T),t}},h=await b.handleResponse({req:e,nextConfig:E,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:A,responseGenerator:u,waitUntil:a.waitUntil});if(!P)return null;if((null==h||null==(i=h.value)?void 0:i.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),k&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||g.delete(x.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,d.sendResponse)($,D,new Response(h.value.body,{headers:g,status:h.value.status||200})),null};H?await i(H):await U.withPropagatedContext(e.headers,()=>U.trace(u.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},i))}catch(t){if(H||t instanceof h.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:N})}),P)throw t;return await (0,d.sendResponse)($,D,new Response(null,{status:500})),null}}},9390,e=>{e.v(t=>Promise.all(["server/chunks/[externals]_path_e30b8067._.js","server/chunks/[root-of-the-server]__6ca3e263._.js"].map(t=>e.l(t))).then(()=>t(29508)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__850f2bc2._.js.map