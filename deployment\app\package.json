{"name": "ticketing-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma migrate reset --force && npm run db:seed", "db:studio": "npx prisma studio", "db:backup": "node scripts/backup.js", "db:restore": "node scripts/restore.js"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.15.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "@types/nodemailer": "^7.0.1", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.543.0", "next": "15.5.2", "next-auth": "^4.24.11", "nodemailer": "^7.0.6", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}