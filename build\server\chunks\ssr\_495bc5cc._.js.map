{"version": 3, "sources": ["turbopack:///[project]/components/ui/button.tsx", "turbopack:///[project]/components/ui/input.tsx", "turbopack:///[project]/src/app/dashboard/departments/DepartmentManager.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      default: \"bg-blue-600 text-white hover:bg-blue-700\",\n      destructive: \"bg-red-600 text-white hover:bg-red-700\",\n      outline: \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n      ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n      link: \"text-blue-600 underline-offset-4 hover:underline\",\n    }\n    \n    const sizes = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n", "import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\n\ntype Department = { id: string; name: string; description?: string | null }\n\nexport default function DepartmentManager() {\n  const [items, setItems] = useState<Department[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [savingId, setSavingId] = useState<string | null>(null)\n  const [newName, setNewName] = useState('')\n  const [newDesc, setNewDesc] = useState('')\n\n  const load = async () => {\n    setLoading(true)\n    setError('')\n    try {\n      const res = await fetch('/api/departments')\n      const data = await res.json()\n      if (!res.ok) throw new Error(data.error || 'Failed to load departments')\n      setItems(data.departments || [])\n    } catch (e: any) {\n      setError(e?.message || 'Failed to load')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => { load() }, [])\n\n  const create = async () => {\n    if (!newName.trim()) return\n    setSavingId('new')\n    setError('')\n    try {\n      const res = await fetch('/api/departments', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name: newName.trim(), description: newDesc.trim() || null }),\n      })\n      const data = await res.json()\n      if (!res.ok) throw new Error(data.error || 'Failed to create')\n      setNewName(''); setNewDesc('')\n      await load()\n    } catch (e: any) {\n      setError(e?.message || 'Failed to create department')\n    } finally {\n      setSavingId(null)\n    }\n  }\n\n  const save = async (id: string, name: string, description: string) => {\n    setSavingId(id)\n    setError('')\n    try {\n      const res = await fetch(`/api/departments/${id}`, {\n        method: 'PATCH',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ name, description }),\n      })\n      const data = await res.json()\n      if (!res.ok) throw new Error(data.error || 'Failed to update')\n      await load()\n    } catch (e: any) {\n      setError(e?.message || 'Failed to update department')\n    } finally {\n      setSavingId(null)\n    }\n  }\n\n  const remove = async (id: string) => {\n    if (!confirm('Delete this department? This cannot be undone.')) return\n    setSavingId(id)\n    setError('')\n    try {\n      const res = await fetch(`/api/departments/${id}`, { method: 'DELETE' })\n      const data = await res.json().catch(() => ({ success: res.ok }))\n      if (!res.ok) throw new Error(data.error || 'Failed to delete')\n      await load()\n    } catch (e: any) {\n      setError(e?.message || 'Failed to delete department')\n    } finally {\n      setSavingId(null)\n    }\n  }\n\n  if (loading) return <div className=\"p-4\">Loading departments...</div>\n  return (\n    <div className=\"bg-white rounded-lg shadow\">\n      <div className=\"p-4 border-b flex items-center justify-between\">\n        <h2 className=\"font-semibold\">Departments</h2>\n        <Button variant=\"outline\" onClick={load}>Refresh</Button>\n      </div>\n      <div className=\"p-4 space-y-4\">\n        {error && <div className=\"bg-red-50 text-red-700 border border-red-200 p-2 rounded\">{error}</div>}\n        <div className=\"grid gap-2 sm:grid-cols-[240px_1fr_auto] items-start\">\n          <Input placeholder=\"New department name\" value={newName} onChange={e=>setNewName(e.target.value)} />\n          <Input placeholder=\"Description (optional)\" value={newDesc} onChange={e=>setNewDesc(e.target.value)} />\n          <Button onClick={create} disabled={savingId==='new' || !newName.trim()}>{savingId==='new' ? 'Adding...' : 'Add'}</Button>\n        </div>\n        <div className=\"divide-y\">\n          {items.map((d) => (\n            <Row key={d.id} d={d} onSave={save} onDelete={remove} saving={savingId===d.id} />\n          ))}\n          {items.length === 0 && <div className=\"text-sm text-gray-500\">No departments found.</div>}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction Row({ d, onSave, onDelete, saving }: { d: Department, onSave: (id:string,n:string,desc:string)=>void, onDelete: (id:string)=>void, saving: boolean }) {\n  const [name, setName] = useState(d.name)\n  const [desc, setDesc] = useState(d.description || '')\n  const dirty = name !== d.name || (desc || '') !== (d.description || '')\n\n  return (\n    <div className=\"py-3 grid gap-2 sm:grid-cols-[240px_1fr_auto] items-center\">\n      <Input value={name} onChange={e=>setName(e.target.value)} />\n      <Input value={desc} onChange={e=>setDesc(e.target.value)} />\n      <div className=\"flex gap-2 justify-end\">\n        <Button size=\"sm\" variant=\"outline\" onClick={()=>onDelete(d.id)} disabled={saving}>Delete</Button>\n        <Button size=\"sm\" onClick={()=>onSave(d.id, name, desc)} disabled={saving || !dirty}>{saving ? 'Saving...' : 'Save'}</Button>\n      </div>\n    </div>\n  )\n}\n\n"], "names": [], "mappings": "8EAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAQA,IAAM,EAAS,EAAA,UAAgB,CAC7B,CAAC,WAAE,CAAS,SAAE,EAAU,SAAS,MAAE,EAAO,SAAS,CAAE,GAAG,EAAO,CAAE,IAoB7D,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAE,AAAF,EACT,AArBc,yRAsBd,AApBW,CACf,QAAS,2CACT,YAAa,yCACb,QAAS,uEACT,UAAW,8CACX,MAAO,wCACP,KAAM,kDACR,CAac,CAAC,EAAQ,CAXT,AAYR,CAXJ,QAAS,iBACT,GAAI,sBACJ,GAAI,uBACJ,KAAM,WACR,CAOW,CAAC,EAAK,CACX,GAEF,IAAK,EACJ,GAAG,CAAK,IAKjB,EAAO,WAAW,CAAG,sEC3CrB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAKA,IAAM,EAAQ,EAAA,UAAgB,CAC5B,CAAC,WAAE,CAAS,CAAE,MAAI,CAAE,GAAG,EAAO,CAAE,IAE5B,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAM,EACN,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,kUACA,GAEF,IAAK,EACJ,GAAG,CAAK,GAKjB,GAAM,WAAW,CAAG,uECnBpB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAIe,SAAS,IACtB,GAAM,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAe,EAAE,EAC7C,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACjC,CAAC,EAAO,EAAS,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAC7B,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAQ,AAAR,EAAwB,MAClD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IACjC,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAEjC,EAAO,UACX,GAAW,GACX,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,oBAClB,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,EAAK,KAAK,EAAI,8BAC3C,EAAS,EAAK,WAAW,EAAI,EAAE,CACjC,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,iBACzB,QAAU,CACR,GAAW,EACb,CACF,EAEA,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KAAQ,GAAO,EAAG,EAAE,EAE9B,IAAM,EAAS,UACb,GAAK,CAAD,CAAS,IAAI,IAAI,AACrB,EAAY,OACZ,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,mBAAoB,CAC1C,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,KAAM,EAAQ,IAAI,GAAI,YAAa,EAAQ,IAAI,IAAM,IAAK,EACnF,GACM,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,EAAK,KAAK,EAAI,oBAC3C,EAAW,IAAK,EAAW,IAC3B,MAAM,GACR,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,8BACzB,QAAU,CACR,EAAY,KACd,EACF,EAEM,EAAO,MAAO,EAAY,EAAc,KAC5C,EAAY,GACZ,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,CAAC,iBAAiB,EAAE,EAAA,CAAI,CAAE,CAChD,OAAQ,QACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,OAAM,aAAY,EAC3C,GACM,EAAO,MAAM,EAAI,IAAI,GAC3B,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,EAAK,KAAK,EAAI,mBAC3C,OAAM,GACR,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,8BACzB,QAAU,CACR,EAAY,KACd,CACF,EAEM,EAAS,MAAO,IACpB,GAAK,CAAD,OAAS,mDAAmD,AAChE,EAAY,GACZ,EAAS,IACT,GAAI,CACF,IAAM,EAAM,MAAM,MAAM,CAAC,iBAAiB,EAAE,EAAA,CAAI,CAAE,CAAE,OAAQ,QAAS,GAC/D,EAAO,MAAM,EAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAE,QAAS,EAAI,EAAE,CAAC,CAAC,EAC9D,GAAI,CAAC,EAAI,EAAE,CAAE,MAAM,AAAI,MAAM,EAAK,KAAK,EAAI,mBAC3C,OAAM,GACR,CAAE,MAAO,EAAQ,CACf,EAAS,GAAG,SAAW,8BACzB,QAAU,CACR,EAAY,KACd,EACF,SAEA,AAAI,EAAgB,CAAA,EAAA,EAAA,EAAP,CAAO,EAAC,MAAA,CAAI,UAAU,eAAM,2BAEvC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,yBAAgB,gBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,QAAS,WAAM,eAE3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0BACZ,GAAS,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,oEAA4D,IACrF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,YAAY,sBAAsB,MAAO,EAAS,SAAU,GAAG,EAAW,EAAE,MAAM,CAAC,KAAK,IAC/F,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,YAAY,yBAAyB,MAAO,EAAS,SAAU,GAAG,EAAW,EAAE,MAAM,CAAC,KAAK,IAClG,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAS,EAAQ,SAAqB,QAAX,GAAoB,CAAC,EAAQ,IAAI,YAAgB,QAAX,EAAmB,YAAc,WAE5G,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qBACZ,EAAM,GAAG,CAAC,AAAC,GACV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAe,EAAG,EAAG,OAAQ,EAAM,SAAU,EAAQ,OAAQ,IAAW,EAAE,EAAE,EAAnE,EAAE,EAAE,GAEE,IAAjB,EAAM,MAAM,EAAU,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCAAwB,kCAKxE,CAEA,SAAS,EAAI,GAAE,CAAC,QAAE,CAAM,UAAE,CAAQ,QAAE,CAAM,CAAmH,EAC3J,GAAM,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAE,IAAI,EACjC,CAAC,EAAM,EAAQ,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,EAAE,WAAW,EAAI,IAC5C,EAAQ,IAAS,EAAE,IAAI,EAAI,CAAC,GAAQ,EAAA,CAAE,IAAO,EAAD,AAAG,WAAW,EAAI,EAAA,CAAE,CAEtE,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uEACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,MAAO,EAAM,SAAU,GAAG,EAAQ,EAAE,MAAM,CAAC,KAAK,IACvD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,MAAO,EAAM,SAAU,GAAG,EAAQ,EAAE,MAAM,CAAC,KAAK,IACvD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,KAAK,QAAQ,UAAU,QAAS,IAAI,EAAS,EAAE,EAAE,EAAG,SAAU,WAAQ,WACnF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,KAAK,QAAS,IAAI,EAAO,EAAE,EAAE,CAAE,EAAM,GAAO,SAAU,GAAU,CAAC,WAAQ,EAAS,YAAc,cAIrH"}