{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/shared/lib/segment.ts", "turbopack:///[project]/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-status-code.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/redirect.ts", "turbopack:///[project]/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts", "turbopack:///[project]/node_modules/next/src/client/components/not-found.ts", "turbopack:///[project]/node_modules/next/src/client/components/forbidden.ts", "turbopack:///[project]/node_modules/next/src/client/components/unauthorized.ts", "turbopack:///[project]/node_modules/next/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/node_modules/next/src/server/lib/router-utils/is-postpone.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/node_modules/next/src/client/components/is-next-router-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/hooks-server-context.ts", "turbopack:///[project]/node_modules/next/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/node_modules/next/src/lib/framework/boundary-constants.tsx", "turbopack:///[project]/node_modules/next/src/lib/scheduler.ts", "turbopack:///[project]/node_modules/next/src/shared/lib/invariant-error.ts", "turbopack:///[project]/node_modules/next/src/server/app-render/dynamic-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.server.ts", "turbopack:///[project]/node_modules/next/src/client/components/unstable-rethrow.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.react-server.ts", "turbopack:///[project]/node_modules/next/src/client/components/unrecognized-action-error.ts", "turbopack:///[project]/node_modules/next/src/client/components/bailout-to-client-rendering.ts", "turbopack:///[project]/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(\n    public readonly route: string,\n    public readonly expression: string\n  ) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  route: string,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(route, expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(route, expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n\nexport function makeDevtoolsIOAwarePromise<T>(underlying: T): Promise<T> {\n  // in React DevTools if we resolve in a setTimeout we will observe\n  // the promise resolution as something that can suspend a boundary or root.\n  return new Promise<T>((resolve) => {\n    // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n    setTimeout(() => {\n      resolve(underlying)\n    }, 0)\n  })\n}\n", "const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */\nexport function warnOnSyncDynamicError(dynamicTracking: DynamicTrackingState) {\n  if (dynamicTracking.syncDynamicErrorWithStack) {\n    // the server did something sync dynamic, likely\n    // leading to an early termination of the prerender.\n    console.error(dynamicTracking.syncDynamicErrorWithStack)\n  }\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends Error {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport function unstable_isUnrecognizedActionError(): boolean {\n  throw new Error(\n    '`unstable_isUnrecognizedActionError` can only be used on the client.'\n  )\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "export class UnrecognizedActionError extends Error {\n  constructor(...args: ConstructorParameters<typeof Error>) {\n    super(...args)\n    this.name = 'UnrecognizedActionError'\n  }\n}\n\n/**\n * Check whether a server action call failed because the server action was not recognized by the server.\n * This can happen if the client and the server are not from the same deployment.\n *\n * Example usage:\n * ```ts\n * try {\n *   await myServerAction();\n * } catch (err) {\n *   if (unstable_isUnrecognizedActionError(err)) {\n *     // The client is from a different deployment than the server.\n *     // Reloading the page will fix this mismatch.\n *     window.alert(\"Please refresh the page and try again\");\n *     return;\n *   }\n * }\n * ```\n * */\nexport function unstable_isUnrecognizedActionError(\n  error: unknown\n): error is UnrecognizedActionError {\n  return !!(\n    error &&\n    typeof error === 'object' &&\n    error instanceof UnrecognizedActionError\n  )\n}\n", "import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../server/app-render/work-unit-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        throw new BailoutToCSRError(reason)\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n", "import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\nexport { unstable_isUnrecognizedActionError } from './unrecognized-action-error'\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify", "getSegmentValue", "Array", "isArray", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "TemporaryRedirect", "Error", "getStore", "isAction", "push", "replace", "PermanentRedirect", "HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "prefix", "httpStatus", "has", "notFound", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "unauthorized", "isHangingPromiseRejectionError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "err", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "constructor", "route", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "underlying", "resolve", "setTimeout", "isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "$$typeof", "BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "reason", "isNextRouterError", "DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "description", "StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "code", "METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "then", "NEXT_RUNTIME", "nextTick", "setImmediate", "r", "InvariantError", "message", "options", "name", "Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createRenderInBrowserAbortSignal", "delayUntilRuntimeStage", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "logDisallowedDynamicError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "warnOnSyncDynamicError", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "store", "workUnitStore", "forceDynamic", "forceStatic", "dynamicShouldError", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "NODE_ENV", "usedDynamic", "prerenderStore", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "controller", "abort", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "console", "workUnitAsyncStorage", "assertPostpone", "createPostponeReason", "isDynamicPostponeReason", "NEXT_PRERENDER_INTERRUPTED", "serverDynamic", "clientDynamic", "filter", "access", "map", "line", "AbortController", "cacheSignal", "inputReady", "runtimeStagePromise", "getRuntimeStagePromise", "workStore", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "hasMetadataRegex", "hasViewportRegex", "hasOutletRegex", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "dev", "hasReadableErrorStacks", "prelude", "result", "unstable_rethrow", "cause", "ReadonlyURLSearchParams", "unstable_isUnrecognizedActionError", "ReadonlyURLSearchParamsError", "URLSearchParams", "append", "delete", "sort", "UnrecognizedActionError", "args", "bailoutToClientRendering", "ServerInsertedHTMLContext", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "PathnameContext", "router", "AppRouterContext", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "segmentValue", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "selectedLayoutSegment"], "mappings": "6CAEO,SAASG,EAAeE,CAAe,EAE5C,MAAsB,MAAfA,CAAO,CAAC,EAAE,EAAYA,EAAQC,QAAQ,CAAC,IAChD,CAEO,SAASF,EAAuBC,CAAe,EACpD,OAAOA,EAAQE,UAAU,CAAC,MAAQF,AAAY,eAChD,CAEO,SAASH,EACdG,CAAgB,CAChBG,CAA2D,EAI3D,GAFsBH,CAElBI,CAF0BC,QAAQ,CAACT,GAEpB,CACjB,IAAMU,EAAmBC,KAAKC,SAAS,CAACL,GACxC,MAA4B,OAArBG,EACHV,EAAmB,IAAMU,EACzBV,CACN,CAEA,OAAOI,CACT,wIAGaL,mBAAmB,CAAA,kBAAnBA,GADAC,gBAAgB,CAAA,kBAAhBA,GAhBGC,4BAA4B,CAAA,kBAA5BA,GATAC,cAAc,CAAA,kBAAdA,GAKAC,sBAAsB,CAAA,kBAAtBA,KAoBT,IAAMH,EAAmB,WACnBD,EAAsB,4CC1B5B,SAASc,EAAgBT,CAAgB,EAC9C,OAAOU,MAAMC,OAAO,CAACX,GAAWA,CAAO,CAAC,EAAE,CAAGA,CAC/C,0EAFgBS,kBAAAA,qCAAAA,mUCFJG,qBAAAA,qCAAAA,KAAL,IAAKA,EAAAA,SAAAA,CAAAA,SAAAA,WAAAA,gHAAAA,mYCECC,mBAAmB,CAAA,kBAAnBA,GAEDC,YAAY,CAAA,kBAAZA,GAgBIC,eAAe,CAAA,kBAAfA,aApBmB,CAAA,CAAA,IAAA,GAEtBF,EAAsB,gBAE5B,IAAKC,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,6BAAAA,OAgBL,SAASC,EAAgBC,CAAc,EAC5C,GACmB,UAAjB,OAAOA,GACG,OAAVA,GACA,CAAE,CAAA,WAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAGT,IAAMA,EAASD,EAAMC,MAAM,CAACC,KAAK,CAAC,KAC5B,CAACC,EAAWC,EAAK,CAAGH,EACpBI,EAAcJ,EAAOK,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAGvCG,EAAaC,OAFJV,AAEWO,EAFJC,EAAE,CAAC,CAAC,IAI1B,OACEN,IAAcN,IACJ,YAATO,GAA+B,IAA/BA,KAAsBA,CAAS,CAAK,EACd,UAAvB,OAAOC,GACP,CAACO,MAAMF,IACPA,KAAcd,EAAAA,kBAElB,AAFoC,6XC3BpBiB,gBAAgB,CAAA,kBAAhBA,GA6EAC,8BAA8B,CAAA,kBAA9BA,GARAC,wBAAwB,CAAA,kBAAxBA,GARAC,uBAAuB,CAAA,kBAAvBA,GAhBAC,iBAAiB,CAAA,kBAAjBA,GAvBAC,QAAQ,CAAA,kBAARA,aArCmB,CAAA,CAAA,IAAA,OAM5B,CAAA,CAAA,IAAA,GAEDC,EAGEE,EAAQ,CAAA,CAAA,IAAA,GACRF,QAHN,OAAOC,GAGiB,CAGnB,EAFDE,KAJc,EAMJT,EACdU,CAAW,CACXnB,CAAkB,CAClBM,CAAqE,EAArEA,KAAAA,IAAAA,IAAAA,EAAiCd,EAAAA,kBAAkB,CAAC4B,iBAAAA,AAAiB,EAErE,IAAMxB,EAAQ,OAAA,cAA8B,CAA9B,AAAIyB,MAAM5B,EAAAA,mBAAmB,EAA7B,oBAAA,OAAA,mBAAA,gBAAA,CAA6B,GAE3C,OADAG,EAAMC,MAAM,CAAMJ,EAAAA,mBAAmB,CAAC,IAAGO,EAAK,IAAGmB,EAAI,IAAGb,EAAW,IAC5DV,CACT,CAcO,SAASkB,EAEdK,CAAW,CACXnB,CAAmB,IAFnB,EAISe,CAIT,OAJAf,OAAAA,GAAAA,EAASe,CAAAA,IAJkB,EAIlBA,CAAAA,EAA4B,AAA5BA,GAAAA,IAAAA,EAAAA,EAAoBO,QAAQ,EAAA,CAAA,CAAA,KAAA,EAA5BP,EAAgCQ,QAAQ,EAC7C7B,EAAAA,YAAY,CAAC8B,IAAI,CACjB9B,EAAAA,YAAY,CAAC+B,OAAO,AAAPA,EAEXhB,EAAiBU,EAAKnB,EAAMR,EAAAA,kBAAkB,CAAC4B,iBAAiB,CACxE,CAaO,SAASP,EAEdM,CAAW,CACXnB,CAAyC,EAEzC,MAFAA,KAFA,AAEAA,IAAAA,IAAAA,EAAqBN,EAAAA,YAAY,CAAC+B,EAFP,KAEOA,AAAO,EAEnChB,EAAiBU,EAAKnB,EAAMR,EAAAA,kBAAkB,CAACkC,iBAAiB,CACxE,CAUO,SAASd,EAAwBhB,CAAc,QACpD,AAAKD,CAAAA,EAAAA,CAAD,CAACA,eAAAA,AAAe,EAACC,GAIdA,EAAMC,GAJgB,GAIV,CAACC,KAAK,CAAC,KAAKI,KAAK,CAAC,EAAG,CAAC,GAAGC,IAAI,CAAC,KAJb,IAKtC,CAEO,SAASQ,EAAyBf,CAAoB,EAC3D,GAAI,CAACD,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAA7ByB,AAAJ,MAAU,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOzB,EAAMC,MAAM,CAACC,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,AACtC,CAEO,SAASY,EAA+Bd,CAAoB,EACjE,GAAI,CAACD,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,GACnB,KAD2B,CACrB,OAAA,cAAiC,CAAjC,AAAIyB,MAAM,wBAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgC,GAGxC,OAAOd,OAAOX,EAAMC,MAAM,CAACC,KAAK,CAAC,KAAKO,EAAE,CAAC,CAAC,GAC5C,4XClGasB,qBAAqB,CAAA,kBAArBA,GAQAC,8BAA8B,CAAA,kBAA9BA,GAuCGC,kCAAkC,CAAA,kBAAlCA,GAPAC,2BAA2B,CAAA,kBAA3BA,GAnBAC,yBAAyB,CAAA,kBAAzBA,KArBT,IAAMJ,EAAwB,CACnCK,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,EAEMC,EAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX,IAE/BC,EAAiC,2BAavC,SAASG,EACdnC,CAAc,EAEd,GACmB,UAAjB,OAAOA,GACPA,AAAU,UACV,CAAE,YAAYA,CAAAA,CAAI,EACM,UAAxB,AACA,OADOA,EAAMC,MAAM,CAEnB,OAAO,EAET,GAAM,CAAC0C,EAAQC,EAAW,CAAG5C,EAAMC,MAAM,CAACC,KAAK,CAAC,KAEhD,OACEyC,IAAWX,GACXO,EAAcM,GAAG,CAAClC,OAAOiC,GAE7B,CAEO,SAASV,EACdlC,CAA8B,EAG9B,OAAOW,OADYX,AACL4C,EADW3C,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,EAAE,CAE/C,CAEO,SAAS+B,EACdzB,CAAc,EAEd,OAAQA,GACN,KAAK,IACH,MAAO,cACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,WACT,SACE,MACJ,CACF,+TCtCgBsC,WAAAA,qCAAAA,KAFhB,IAAMC,EAAU,GAAEf,EAjBX,CAAA,CAAA,IAAA,EAiBWA,8BAA8B,CAAC,OAE1C,SAASc,IAEd,IAAM9C,EAAQ,OAAA,cAAiB,CAAbyB,AAAJ,MAAUsB,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAgB,EAG9B,OAFE/C,EAAkCC,MAAM,CAAG8C,EAEvC/C,CACR,sPCPO,SAASgD,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAIvB,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,0EAXgBuB,YAAAA,qCAAAA,KAFEhB,EAhBX,CAAA,CAAA,IAAA,EAgBWA,8BAA8B,GAAC,mPCG1C,SAASoB,IAEZ,MAAM,OAAA,cAEL,CAFK,AAAI3B,MACP,+GADG,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,0EAXgB2B,eAAAA,qCAAAA,KAFEpB,EAjBX,CAAA,CAAA,IAAA,EAiBWA,8BAA8B,GAAC,mPCpB1C,SAASqB,EACdG,CAAY,QAEZ,AAAmB,UAAf,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIvD,CAJwD,KAIlD,GAAKwD,CACxB,wIARgBJ,8BAA8B,CAAA,kBAA9BA,GA2EAC,0BAA0B,CAAA,kBAA1BA,GAxCAC,kBAAkB,CAAA,kBAAlBA,KAzBhB,IAAME,EAA4B,2BAElC,OAAMC,UAAqCjC,MAGzCkC,YACkBC,CAAa,CACbC,CAAkB,CAClC,CACA,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,8KAA8K,EAAED,EAAM,EAAE,CAAC,EAAA,IAAA,CAJhUA,KAAAA,CAAAA,EAAAA,IAAAA,CACAC,UAAAA,CAAAA,EAAAA,IAAAA,CAJF5D,MAAAA,CAASwD,CASzB,CACF,CAGA,IAAMK,EAAyB,IAAIC,QAS5B,SAASR,EACdS,CAAmB,CACnBJ,CAAa,CACbC,CAAkB,EAElB,GAAIG,EAAOC,OAAO,CAChB,CADkB,MACXC,QAAQC,MAAM,CAAC,IAAIT,EAA6BE,EAAOC,GACzD,EACL,IAAMO,EAAiB,IAAIF,QAAW,CAACG,EAAGF,KACxC,IAAMG,EAAiBH,EAAOI,IAAI,CAChC,KACA,IAAIb,EAA6BE,EAAOC,IAEtCW,EAAmBV,EAAuBW,GAAG,CAACT,GAClD,GAAIQ,EACFA,EAAiB5C,IAAI,CAAC0C,OACjB,CACL,CAHoB,GAGdI,EAAY,CAACJ,EAAe,CAClCR,EAAuBa,GAAG,CAACX,EAAQU,GACnCV,EAAOY,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUI,MAAM,CAAED,IAAK,AACzCH,CAAS,CAACG,EAAE,EAEhB,EACA,CAAEE,MAAM,CAAK,EAEjB,CACF,GAKA,OADAX,EAAeY,KAAK,CAACC,GACdb,CACT,CACF,CAEA,SAASa,IAAgB,CAElB,SAAS3B,EAA8B4B,CAAa,EAGzD,OAAO,IAAIhB,QAAW,AAACiB,IAErBC,WAAW,KACTD,EAAQD,EACV,EAAG,EACL,EACF,yGClFgBG,aAAAA,qCAAAA,KAFhB,IAAMC,EAA8BC,OAAOC,GAAG,CAAC,kBAExC,SAASH,EAAWrF,CAAU,EACnC,MACE,AAAiB,iBAAVA,GACG,OAAVA,GACAA,EAAMyF,QAAQ,GAAKH,CAEvB,uKCJaI,iBAAiB,CAAA,kBAAjBA,GASGC,mBAAmB,CAAA,kBAAnBA,KAZhB,IAAMC,EAAiB,kCAGhB,OAAMF,UAA0BjE,MAGrCkC,YAA4BkC,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZ5F,MAAAA,CAAS2F,CAIzB,CACF,CAGO,SAASD,EAAoBnC,CAAY,QAC9C,AAAmB,UAAf,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIvD,CAJwD,KAIlD,GAAK2F,CACxB,yGCRgBE,oBAAAA,qCAAAA,aART,CAAA,CAAA,IAAA,MAC6C,CAAA,CAAA,IAAA,GAO7C,SAASA,EACd9F,CAAc,EAEd,MAAOD,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACC,IAAUmC,CAAAA,EAAAA,EAAAA,yBAAAA,AAAyB,EAACnC,EAC7D,4XCba+F,kBAAkB,CAAA,kBAAlBA,GAQGC,oBAAoB,CAAA,kBAApBA,KAVhB,IAAMC,EAAqB,sBAEpB,OAAMF,UAA2BtE,MAGtCkC,YAA4BuC,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5BjG,MAAAA,CAAoCgG,CAIpC,CACF,CAEO,SAASD,EAAqBxC,CAAY,QAC/C,AACiB,UAAf,OAAOA,GACC,OAARA,CACA,CAAE,CAAA,WAAYA,GACQ,AADN,AAChB,UACA,OADOA,EAAIvD,MAAM,EAKZuD,EAAIvD,MAAM,GAAKgG,CACxB,6XCnBaE,qBAAqB,CAAA,kBAArBA,GAIGC,uBAAuB,CAAA,kBAAvBA,KANhB,IAAMC,EAA0B,yBAEzB,OAAMF,UAA8B1E,wBAApC,KAAA,IAAA,GAAA,IAAA,CACW6E,IAAAA,CAAOD,EACzB,CAEO,SAASD,EACdpG,CAAc,QAEd,AAAqB,UAAjB,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAJ4D,AAItDsG,GAJ0D,CAItD,GAAKD,CACxB,6XCdaE,sBAAsB,CAAA,kBAAtBA,GAEAC,oBAAoB,CAAA,kBAApBA,GACAC,yBAAyB,CAAA,kBAAzBA,GAFAC,sBAAsB,CAAA,kBAAtBA,KADN,IAAMH,EAAyB,6BACzBG,EAAyB,6BACzBF,EAAuB,2BACvBC,EAA4B,qMCyCzBE,cAAc,CAAA,kBAAdA,GAbHC,iBAAiB,CAAA,kBAAjBA,GAtBAC,kBAAkB,CAAA,kBAAlBA,GAgDGC,6BAA6B,CAAA,kBAA7BA,KAhDT,IAAMD,EAAqB,AAACE,IAOjC7C,QAAQiB,OAAO,GAAG6B,IAAI,CAAC,KAInB/D,QAAQiE,QAAQ,CAACH,EAErB,EACF,EAQaH,EAAoB,AAACG,IAI9BI,aAAaJ,EAEjB,EAOO,SAASJ,IACd,OAAO,IAAIzC,QAAeiB,AAAD,GAAayB,EAAkBzB,GAC1D,CAWO,SAAS2B,IAIZ,OAAO,IAAI5C,QAAQ,AAACkD,GAAMD,aAAaC,GAE3C,yGC/DaC,iBAAAA,qCAAAA,IAAN,OAAMA,UAAuB5F,MAClCkC,YAAY2D,CAAe,CAAEC,CAAsB,CAAE,CACnD,KAAK,CACF,eAAaD,CAAAA,CAAQrI,QAAQ,CAAC,KAAOqI,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DC,GAEF,IAAI,CAACC,IAAI,CAAG,gBACd,CACF,gCCYC,OAAA,cAAA,CAAA,EAAA,aAAA,kGA6VeC,QAAQ,CAAA,kBAARA,GA4XJC,YAAY,CAAA,kBAAZA,GApbIC,2CAA2C,CAAA,kBAA3CA,GArCAC,kCAAkC,CAAA,kBAAlCA,GAuLAC,mBAAmB,CAAA,kBAAnBA,GAkIAC,qBAAqB,CAAA,kBAArBA,GA5HAC,oBAAoB,CAAA,kBAApBA,GArXAC,0BAA0B,CAAA,kBAA1BA,GAUAC,4BAA4B,CAAA,kBAA5BA,GAmbAC,6BAA6B,CAAA,kBAA7BA,GAXAC,gCAAgC,CAAA,kBAAhCA,GA8TAC,sBAAsB,CAAA,kBAAtBA,GAhXAC,wBAAwB,CAAA,kBAAxBA,GA5WAC,qBAAqB,CAAA,kBAArBA,GAsSAC,iBAAiB,CAAA,kBAAjBA,GAwCAC,2BAA2B,CAAA,kBAA3BA,GAqTAC,yBAAyB,CAAA,kBAAzBA,GAtnBAC,yBAAyB,CAAA,kBAAzBA,GA6PAC,oBAAoB,CAAA,kBAApBA,GA4YAC,wBAAwB,CAAA,kBAAxBA,GA/jBAC,gCAAgC,CAAA,kBAAhCA,GAueAC,yBAAyB,CAAA,kBAAzBA,GA9cAC,+BAA+B,CAAA,kBAA/BA,GAuEAC,qCAAqC,CAAA,kBAArCA,GAgEHC,sCAAsC,CAAA,kBAAtCA,GAqPGC,qBAAqB,CAAA,kBAArBA,GA9PAC,sBAAsB,CAAA,kBAAtBA,+DA9TE,CAAA,CAAA,IAAA,QAEiB,CAAA,CAAA,IAAA,MACG,CAAA,CAAA,IAAA,OAI/B,CAAA,CAAA,IAAA,OAC0B,CAAA,CAAA,IAAA,OACE,CAAA,CAAA,IAAA,OAM5B,CAAA,CAAA,IAAA,OAC4B,CAAA,CAAA,IAAA,OACD,CAAA,CAAA,IAAA,OACH,CAAA,CAAA,IAAA,GAEzBC,EAAiD,YAAnC,OAAOC,EAAAA,OAAK,CAACC,iBAAiB,CAwC3C,SAAStB,EACduB,CAA2C,EAE3C,MAAO,wBACLA,EACAC,gBAAiB,EAAE,CACnBC,0BAA2B,IAC7B,CACF,CAEO,SAASxB,IACd,MAAO,CACLyB,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,mBAAmB,EACnBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASxB,EACdyB,CAAmC,MAE5BA,EAAP,OAAA,AAAuC,OAAhCA,EAAAA,EAAcP,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCO,EAAkClG,UAAU,AACrD,CASO,SAAS6E,EACdsB,CAAgB,CAChBC,CAAuE,CACvEpG,CAAkB,EAElB,GAAIoG,EACF,OAAQA,EAAc7J,IADL,AACS,EACxB,IAAK,QACL,IAAK,iBAML,IAAK,gBADH,MAUJ,CAMF,IAAI4J,EAAME,YAAY,GAAIF,EAAMG,WAAW,EAAE,AAE7C,GAAIH,EAAMI,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAIjE,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAE6D,EAAMpG,KAAK,CAAC,8EAA8E,EAAEC,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIoG,EACF,OAAQA,EAAc7J,IAAI,AADT,EAEf,IAAK,gBACH,OAAOuI,EACLqB,EAAMpG,KAAK,CACXC,EACAoG,EAAcI,eAAe,CAEjC,KAAK,mBACHJ,EAAcK,UAAU,CAAG,EAI3B,IAAM9G,EAAM,OAAA,cAEX,CAFW,IAAIuC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEiE,EAAMpG,KAAK,CAAC,iDAAiD,EAAEC,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHAmG,EAAMO,uBAAuB,CAAG1G,EAChCmG,EAAMQ,iBAAiB,CAAGhH,EAAIiH,KAAK,CAE7BjH,CAQV,EAEJ,CAQO,SAASqF,EACdhF,CAAkB,CAClBmG,CAAgB,CAChBY,CAAoC,EAGpC,IAAMpH,EAAM,OAAA,cAEX,CAFW,IAAIuC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEiE,EAAMpG,KAAK,CAAC,mDAAmD,EAAEC,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAOA,OALA+G,EAAeN,UAAU,CAAG,EAE5BN,EAAMO,uBAAuB,CAAG1G,EAChCmG,EAAMQ,iBAAiB,CAAGhH,EAAIiH,KAAK,CAE7BjH,CACR,CASO,SAASuF,EAAgCkB,CAA4B,EAC1E,OAAQA,EAAc7J,IAAI,EACxB,IAAK,QACL,IAAK,iBAML,IAAK,gBADH,MAiBJ,CACF,CAEA,SAASyK,EACPjH,CAAa,CACbC,CAAkB,CAClB+G,CAAoC,EAIpC,IAAM5K,EAAQ8K,EAFC,CAAC,MAAM,EAAElH,EAAM,mBAEgBiC,8CAFiD,EAAEhC,EAAW,CAAC,CAAC,EAI9G+G,EAAeG,UAAU,CAACC,KAAK,CAAChL,GAEhC,IAAMqK,EAAkBO,EAAeP,eAAe,CAClDA,GACFA,EAAgBb,YADG,GACY,CAAC5H,IAAI,CAAC,CAGnC6I,MAAOJ,EAAgBd,sBAAsB,CACzC,AAAI9H,QAAQgJ,KAAK,CACjBnJ,OACJuC,YACF,EAEJ,CAEO,SAAS+D,EACdhE,CAAa,CACbC,CAAkB,CAClBoH,CAAqB,CACrBL,CAAoC,EAEpC,IAAMP,EAAkBO,EAAeP,eAAe,CACtDQ,EAAoCjH,EAAOC,EAAY+G,GAKnDP,GACgD,MAAM,CAApDA,EAAgBZ,KADD,oBAC0B,EAC3CY,GAAgBZ,yBAAyB,CAAGwB,CAAAA,CAGlD,CAEO,SAASjC,EACdkC,CAA0B,EAI1BA,EAAaC,cAAc,EAAG,CAChC,CAYO,SAASxD,EACd/D,CAAa,CACbC,CAAkB,CAClBoH,CAAqB,CACrBL,CAAoC,EAGpC,GAAIQ,AAA4B,KADRR,EAAeG,UAAU,CAAC/G,MAAM,CACpCC,OAAO,CAAY,CAMrC4G,EAAoCjH,EAAOC,EAAY+G,GAKvD,IAAMP,EAAkBO,EAAeP,eAAe,CAClDA,GACgD,MAAM,CAApDA,EAAgBZ,KADD,oBAC0B,GAC3CY,EAAgBZ,yBAAyB,CAAGwB,CAAAA,CAGlD,CACA,MAAMH,EACJ,CAAC,MAAM,EAAElH,EAAM,iEAAiE,EAAEC,EAAW,CAAC,CAAC,CAEnG,CASO,SAASsF,EAAuBkB,CAAqC,EACtEA,EAAgBZ,yBAAyB,EAAE,AAG7C4B,QAAQrL,KAAK,CAACqK,EAAgBZ,yBAAyB,CAE3D,CAGO,IAAMR,EACXD,EASK,SAASvB,EAAS,QAAE5B,CAAM,OAAEjC,CAAK,CAAiB,EACvD,IAAMgH,EAAiBU,EAAAA,oBAAoB,CAAC5J,QAAQ,GAKpDiH,EAAqB/E,EAAOiC,EAH1B+E,GAA0C,GAGRP,eAHhBO,EAAexK,IAAI,CACjCwK,EAAeP,eAAe,CAC9B,KAER,CAEO,SAAS1B,EACd/E,CAAa,CACbC,CAAkB,CAClBwG,CAA4C,EAE5CkB,AAmIF,SAASA,GACP,GAAI,CAACnC,EACH,MAAM,KADU,EACV,cAEL,CAFK,AAAI3H,MACR,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,eAAA,EAEN,GAEJ,IAxIM4I,GACFA,EAAgBb,YADG,GACY,CAAC5H,IAAI,CAAC,CAGnC6I,MAAOJ,EAAgBd,sBAAsB,CACzC,AAAI9H,QAAQgJ,KAAK,MACjBnJ,aACJuC,CACF,GAGFwF,EAAAA,OAAK,CAACC,iBAAiB,CAACkC,EAAqB5H,EAAOC,GACtD,CAEA,SAAS2H,EAAqB5H,CAAa,CAAEC,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAED,EAAM,iEAAiE,EAAEC,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKW0E,EAAkB/E,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACgC,UAAhC,AACA,OADQA,EAAY8D,OAAO,EAEpBmE,EAAyBjI,EAXgD,AAWpC8D,CAXqC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASmE,EAAwB5F,CAAc,EAC7C,OACEA,EAAOxG,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnFwG,EAAOxG,QAAQ,CACb,gEAGN,CAEA,GAAoE,KAAhEoM,EAAuE,AAA/CD,EAAqB,MAAO,QACtD,MAAM,OAAA,cAEL,CAFK,AAAI/J,MACR,0FADI,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAGF,IAAMiK,EAA6B,6BAEnC,SAASZ,EAAgCxD,CAAe,EACtD,IAAMtH,EAAQ,OAAA,cAAkB,CAAlB,AAAIyB,MAAM6F,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADEtH,EAAcC,MAAM,CAAGyL,EAClB1L,CACT,CAMO,SAASwI,EACdxI,CAAc,EAEd,MACmB,UAAjB,OAAOA,GACPA,AAAU,UACTA,EAAcC,MAAM,GAAKyL,GAC1B,SAAU1L,GACV,YAAaA,GACbA,aAAiByB,KAErB,CAEO,SAASoG,EACd2B,CAAqC,EAErC,OAAOA,EAAgB1E,MAAM,CAAG,CAClC,CAEO,SAASiD,EACd4D,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAcnC,eAAe,CAAC5H,IAAI,IAAIgK,EAAcpC,eAAe,EAC5DmC,EAAcnC,eAAe,AACtC,CAEO,SAASnB,EACdmB,CAAqC,EAErC,OAAOA,EACJqC,MAAM,CACL,AAACC,GACyB,UAAxB,OAAOA,EAAOrB,KAAK,EAAiBqB,EAAOrB,KAAK,CAAC3F,MAAM,CAAG,GAE7DiH,GAAG,CAAC,CAAC,YAAElI,CAAU,OAAE4G,CAAK,CAAE,IACzBA,EAAQA,EACLvK,KAAK,CAAC,MACP,AAGCI,KAAK,CAAC,GACNuL,MAAM,CAAC,AAACG,KAEHA,EAAK3M,QAAQ,CAAC,uBAAuB,AAKrC2M,EAAK3M,QAAQ,CAAC,MAXoD,aAWjC,AAKjC2M,EAAK3M,QAAQ,CAAC,YAAY,CAM/BkB,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAEsD,EAAW;AAAG,EAAE4G,EAAAA,CAAO,EAEjE,CAcO,SAAStC,IACd,IAAM4C,EAAa,IAAIkB,gBAEvB,OADAlB,EAAWC,KAAK,CAAC,OAAA,cAA0C,CAA1C,IAAItF,EAAAA,iBAAiB,CAAC,qBAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAAyC,IACnDqF,EAAW/G,MAAM,AAC1B,CAOO,SAASkE,EACd+B,CAA4B,EAE5B,OAAQA,EAAc7J,IAAI,EACxB,IAAK,YACL,IAAK,oBACH,IAAM2K,EAAa,IAAIkB,gBAEvB,GAAIhC,EAAciC,WAAW,CAI3BjC,CAJ6B,CAIfiC,WAAW,CAACC,UAAU,GAAGnF,IAAI,CAAC,KAC1C+D,EAAWC,KAAK,EAClB,OACK,CAaL,IAAMoB,EAAsBC,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAACpC,GAC/CmC,EACFA,EAAoBpF,IAAI,CAAC,IACvBH,CAAAA,EAAAA,EAAAA,GAFqB,eAErBA,AAAkB,EAAC,IAAMkE,EAAWC,KAAK,KAG3CnE,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC,IAAMkE,EAAWC,KAAK,GAE7C,CAEA,OAAOD,EAAW/G,MAAM,AAC1B,KAAK,mBACL,IAAK,gBACL,IAAK,mBACL,IAAK,UACL,IAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAGJ,CACF,AAJa1C,CAMN,SAASwG,EACdjE,CAAkB,CAClB+G,CAAoC,EAEpC,IAAMP,EAAkBO,EAAeP,eAAe,CAClDA,GACFA,EAAgBb,YADG,GACY,CAAC5H,IAAI,CAAC,CACnC6I,MAAOJ,EAAgBd,sBAAsB,CACzC,AAAI9H,QAAQgJ,KAAK,MACjBnJ,aACJuC,CACF,EAEJ,CAEO,SAASqF,EAAsBrF,CAAkB,EACtD,IAAMyI,EAAYC,EAAAA,gBAAgB,CAAC7K,QAAQ,GACrCuI,EAAgBqB,EAAAA,oBAAoB,CAAC5J,QAAQ,GACnD,GAAI4K,GAAarC,EACf,OAAQA,EAAc7J,IADQ,AACJ,EACxB,IAAK,mBACL,IAAK,YAAa,CAChB,IAAMoM,EAAiBvC,EAAcwC,mBAAmB,CACpDD,GAAkBA,EAAeE,IAAI,CAAG,GAAG,AAI7CrD,EAAAA,OAAK,CAACsD,GAAG,CACPpJ,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChB0G,EAAc2C,YAAY,CAC1BN,EAAU1I,KAAK,CACfC,IAIN,KACF,CACA,IAAK,gBAAiB,CACpB,IAAM2I,EAAiBvC,EAAcwC,mBAAmB,CACxD,GAAID,GAAkBA,EAAeE,IAAI,CAAG,EAC1C,CAD6C,MACtC/D,EACL2D,EAAU1I,KAAK,CACfC,EACAoG,EAAcI,eAAe,EAGjC,KACF,CACA,IAAK,oBACH,MAAM,OAAA,cAEL,CAFK,IAAIhD,EAAAA,cAAc,CACtB,CAAC,EAAE,EAAExD,EAAW,uEAAuE,EAAEA,EAAW,+EAA+E,CAAC,EADhL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,QACL,IAAK,gBACH,MAAM,OAAA,cAEL,CAFK,IAAIwD,EAAAA,cAAc,CACtB,CAAC,EAAE,EAAExD,EAAW,iEAAiE,EAAEA,EAAW,+EAA+E,CAAC,EAD1K,oBAAA,OAAA,kBAAA,gBAAA,EAEN,EAOJ,CAEJ,CAEA,IAAMgJ,EAAmB,mCAkBnBE,EAA4D,AAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,+DAAErG,EAAAA,yBAAyB,CAAC,cAAc,CAAC,EAG9JwG,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAEzG,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzC2G,EAAmB,AAAIF,OAC3B,CAAC,UAAU,EAAEtG,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCyG,EAAiB,AAAIH,OAAO,CAAC,UAAU,EAAExG,EAAAA,oBAAoB,CAAC,QAAQ,CAAC,EAEtE,SAASsC,EACdwD,CAAoB,CACpBc,CAAsB,CACtBC,CAAyC,CACzCzB,CAAmC,EAEnC,IAAIuB,EAAeG,IAAI,CAACF,IAGjB,GAAIH,EAAiBK,IAAI,CAACF,GAHQ,AAGS,CAChDC,EAAkB1D,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAIuD,EAAiBI,IAAI,CAACF,GAAiB,CAChDC,EAAkBzD,kBAAkB,EAAG,EACvC,MACF,CAAO,GACLmD,EAA0DO,IAAI,CAC5DF,GAEF,CAIAC,EAAkBxD,iBAAiB,EAAG,EACtCwD,EAAkB3D,oBAAoB,CAAG,GACzC,MACF,MAAO,GAAImD,EAAiBS,IAAI,CAACF,GAAiB,CAGhDC,EAAkBxD,iBAAiB,EAAG,EACtC,MACF,KAMO,CANA,GAAI+B,EAAcnC,yBAAyB,CAAE,YAElD4D,EAAkBvD,aAAa,CAAClI,IAAI,CAClCgK,EAAcnC,yBAAyB,EAKzC,IAAMzJ,EAAQuN,AAUlB,SAASA,AACPjG,CAAe,CACf8F,CAAsB,EAOtB,IAAMpN,EAAQ,OAAA,UAnBuCsH,IAmBrB,CAAlB,AAAI7F,MAAM6F,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADAtH,EAAMyK,KAAK,CAAGzK,EAAMwH,IAAI,CAAG,KAAOF,EAAyB8F,EACpDpN,CACT,EAvBoB,CAAC,EAqB0BwN,CAAAA,IArBnB,CAqB8C,CArB5ClB,EAAU1I,KAAK,CAAC,2NAA2N,CAAC,CAC1MwJ,eAC5DC,EAAkBvD,aAAa,CAAClI,IAAI,CAAC5B,EAEvC,EACF,CAoBO,IAAK0H,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,4DAAAA,OAML,SAASe,EACd6D,CAAoB,CACpBtM,CAAY,EAEZqL,QAAQrL,KAAK,CAACA,GAETsM,EAAUoB,GAAG,EAAE,CACdpB,EAAUqB,sBAAsB,CAClCtC,CADoC,OAC5BrL,KAAK,CACX,CAAC,iIAAiI,EAAEsM,EAAU1I,KAAK,CAAC,2CAA2C,CAAC,EAGlMyH,QAAQrL,KAAK,CAAC,CAAC;0EACqD,EAAEsM,EAAU1I,KAAK,CAAC;qGACS,CAAC,EAGtG,CAEO,SAASgF,EACd0D,CAAoB,CACpBsB,CAAqB,CACrBP,CAAyC,CACzC1B,CAAmC,EAEnC,GAAIiC,IAAAA,EAA+B,CACjC,GAAIP,EAAkB3D,oBAAoB,CAIxC,CAJ0C,MAO5C,GAAIiC,EAAclC,yBAAyB,CAQzC,CAR2C,KAI3ChB,EACE6D,EACAX,EAAclC,yBAAyB,EAEnC,IAAItD,EAAAA,qBAAqB,CAMjC,IAAM2D,EAAgBuD,EAAkBvD,aAAa,CACrD,GAAIA,EAAchF,MAAM,CAAG,EAAG,CAC5B,IAAK,IAAID,EAAI,EAAGA,EAAIiF,EAAchF,MAAM,CAAED,IAAK,AAC7C4D,EAA0B6D,EAAWxC,CAAa,CAACjF,EAAE,CAGvD,OAAM,IAAIsB,EAAAA,qBAAqB,AACjC,CAMA,GAAIkH,EAAkBzD,kBAAkB,CAItC,CAJwC,KACxCyB,QAAQrL,KAAK,CACX,CAAC,OAAO,EAAEsM,EAAU1I,KAAK,CAAC,8QAA8Q,CAAC,EAErS,IAAIuC,EAAAA,qBAAqB,CAGjC,GAAIyH,GAAgC,GAOlC,MAHAvC,QAAQrL,KAAK,CACX,CAAC,OAAO,EAAEsM,EAAU1I,KAAK,CAAC,wGAAwG,CAAC,EAE/H,IAAIuC,EAAAA,qBAEd,AAFmC,MAGjC,CADK,GAEqC,IAAxCkH,EAAkBxD,iBAAiB,EACnCwD,EAAkB1D,kBAAkB,CAKpC,CAJA,KACA0B,QAAQrL,KAAK,CACX,CAAC,OAAO,EAAEsM,EAAU1I,KAAK,CAAC,8PAA8P,CAAC,EAErR,IAAIuC,EAAAA,qBAAqB,AAGrC,CAEO,SAASiC,EACdwC,CAA2C,CAC3CiD,CAAkB,SAElB,AAAIjD,EAAewB,mBAAmB,CAC7BxB,CAD+B,CAChBwB,mBAAmB,CAACpF,IAAI,CAAC,IAAM6G,GAEhDA,CACT,yGC70BgBC,mBAAAA,qCAAAA,AAAT,SAASA,EAAiB9N,CAAc,EAC7C,GACE8F,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAAC9F,IAClB2F,CAAAA,EAAAA,EAAAA,mBAAAA,AAAmB,EAAC3F,IACpBgG,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EAAChG,IACrBuI,CAAAA,EAAAA,EAAAA,iBAAAA,AAAiB,EAACvI,IAClBqF,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACrF,IACXqD,GAAAA,EAAAA,8BAAAA,AAA8B,EAACrD,GAE/B,KADA,CACMA,EAGJA,aAAiByB,OAAS,UAAWzB,GACvC8N,EAAiB9N,EAD6B,AACvB+N,KAAK,CAEhC,aAtB+C,CAAA,CAAA,IAAA,OACpB,CAAA,CAAA,IAAA,OACS,CAAA,CAAA,IAAA,OACF,CAAA,CAAA,IAAA,OACA,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,uPCCpC,OAAA,cAAA,CAAA,EAAA,aAAA,oCACYD,mBAAAA,qCAAAA,KAAN,IAAMA,EAGLzM,EAAQ,CAAA,CAAA,IAAA,GACRyM,MAHN,OAAO1M,GAGe,GAEhBC,KALY,GAKJ,8BACRyM,gBAAgB,6LCdV,OAAA,cAAA,CAAA,EAAA,aAAA,kGAwCLE,uBAAuB,CAAA,kBAAvBA,GALAlO,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAEZkD,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTF,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAFE7B,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EAA3BC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIRkC,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAVL6K,kCAAkC,CAAA,kBAAlCA,GAWPH,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,YALmB,CAAA,CAAA,IAAA,OACf,CAAA,CAAA,IAAA,OACJ,CAAA,CAAA,IAAA,OACC,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,OACI,CAAA,CAAA,IAAA,EAtCjC,OAAMI,UAAqCzM,MACzCkC,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAMqK,UAAgCG,gBAEpCC,QAAS,CACP,MAAM,IAAIF,CACZ,CAEAG,QAAS,CACP,MAAM,IAAIH,CACZ,CAEAvJ,KAAM,CACJ,MAAM,IAAIuJ,CACZ,CAEAI,MAAO,CACL,MAAM,IAAIJ,CACZ,CACF,CAEO,SAASD,IACd,MAAM,OAAA,cAEL,CAFK,AAAIxM,MACR,wEADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,6XChCa8M,uBAAuB,CAAA,kBAAvBA,GAyBGN,kCAAkC,CAAA,kBAAlCA,IAzBT,OAAMM,UAAgC9M,MAC3CkC,YAAY,GAAG6K,CAAyC,CAAE,CACxD,KAAK,IAAIA,GACT,IAAI,CAAChH,IAAI,CAAG,yBACd,CACF,CAoBO,SAASyG,EACdjO,CAAc,EAEd,MAAO,CAAC,CACNA,CAAAA,GACiB,UAAjB,OAAOA,GACPA,aAAiBuO,CAAAA,CAAsB,AAE3C,+TC7BgBE,2BAAAA,qCAAAA,aAJkB,CAAA,CAAA,IAAA,OACD,CAAA,CAAA,IAAA,OACI,CAAA,CAAA,IAAA,GAE9B,SAASA,EAAyB5I,CAAc,EACrD,IAAMyG,EAAYC,EAAAA,gBAAgB,CAAC7K,QAAQ,GAE3C,GAAI4K,MAAAA,EAAAA,KAAAA,EAAAA,EAAWnC,WAAW,CAAE,OAE5B,IAAMF,EAAgBqB,EAAAA,oBAAoB,CAAC5J,QAAQ,GAEnD,GAAIuI,EACF,OAAQA,EAAc7J,IAAI,AADT,EAEf,IAAK,YACL,IAAK,oBACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBACH,MAAM,OAAA,cAA6B,CAA7B,IAAIsF,EAAAA,iBAAiB,CAACG,GAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAA4B,EAQtC,CAEJ,6XCgQEmI,uBAAuB,CAAA,kBAAvBA,EAAAA,uBAAuB,EADvBlO,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EAtLZ4O,yBAAyB,CAAA,kBAAzBA,EAAAA,yBAAyB,EAkLzB1L,SAAS,CAAA,kBAATA,EAAAA,SAAS,EADTF,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EAIR7B,iBAAiB,CAAA,kBAAjBA,EAAAA,iBAAiB,EADjBC,QAAQ,CAAA,kBAARA,EAAAA,QAAQ,EADRkC,YAAY,CAAA,kBAAZA,EAAAA,YAAY,EANL6K,kCAAkC,CAAA,kBAAlCA,EAAAA,kCAAkC,EAWzCH,gBAAgB,CAAA,kBAAhBA,EAAAA,gBAAgB,EAtIFa,SAAS,CAAA,kBAATA,GA5DAC,WAAW,CAAA,kBAAXA,GAiCAC,SAAS,CAAA,kBAATA,GA9EAC,eAAe,CAAA,kBAAfA,GA6MAC,wBAAwB,CAAA,kBAAxBA,GA/BAC,yBAAyB,CAAA,kBAAzBA,GAtHdC,qBAAqB,CAAA,kBAArBA,EAAAA,qBAAqB,YAnGa,CAAA,CAAA,IAAA,OAK7B,CAAA,CAAA,IAAA,MAKA,CAAA,CAAA,IAAA,OACyB,CAAA,CAAA,IAAA,OACsB,CAAA,CAAA,IAAA,OACd,CAAA,CAAA,IAAA,OAuFjC,CAAA,CAAA,IAAA,OA2K4C,CAAA,CAAA,IAAA,GAhQ7C/F,EAGE7H,EAAQ,CAAA,CAAA,IAAA,GACR6H,WAHN,OAAO9H,GAGoB,CAwBtB,EAvBDE,KAJc,EA2BJwN,IACd,IAAM3P,EAAe+P,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,mBAAmB,EAK7CC,EAAuBC,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,IACnC,AAAKlQ,EAME,EANH,EAMO6O,EAAAA,MANQ,iBAMe,CAAC7O,GAH1B,KAIR,CAACA,EAAa,CAEkB,EAEjC,GAAM,0BAAEsP,CAAwB,CAAE,CAChCpN,EAAQ,CAAA,CAAA,IAAA,GAEVoN,EAAyB,oBAC3B,CAEA,OAAOW,CACT,CAoBO,SAASR,IAKd,OAJA1F,OAAAA,EAAAA,EAAwB,CAAxBA,gBAIOgG,CAAAA,EAAAA,EAAAA,UAAU,AAAVA,EAAWI,EAAAA,eAAe,CACnC,CA2BO,SAAST,IACd,IAAMU,EAASL,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACM,EAAAA,gBAAgB,EAC1C,GAAe,MAAM,CAAjBD,EACF,MAAM,OAAA,cAAwD,CAApD9N,AAAJ,MAAU,+CAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAuD,GAG/D,OAAO8N,CACT,CAoBO,SAASZ,IAGd,OAFAzF,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,cAEOgG,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACO,EAAAA,iBAAiB,CACrC,CAiEO,SAAST,EACdY,CAAqC,EAArCA,KAAAA,OAAAA,GAA2B,UAAA,EAE3B1G,OAAAA,EAAAA,EAAwB,CAAxBA,8BAEA,IAAMiH,EAAUjB,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACkB,EAAAA,mBAAmB,SAE9C,AAAKD,EAEET,AAtET,EAoEM,KAAU,EApEPA,EACPC,CAAuB,CACvBC,CAAwB,CACxBC,CAAY,CACZC,CAA0B,MAEtBC,EACJ,GAJAF,KAAAA,IAAAA,IAAAA,GAAQ,CAAA,EACRC,KAAAA,IAAAA,IAAAA,EAAwB,EAAA,AAAE,EAGtBD,EAEFE,EAAOJ,CAAI,CAAC,CAFH,CAEK,CAACC,EAAiB,KAC3B,KAGEI,EADP,IAAMA,EAAiBL,CAAI,CAAC,EAAE,CAC9BI,EAAOC,AAAuB,OAAvBA,EAAAA,EAAeC,QAAAA,AAAQ,EAAvBD,EAA2BvN,OAAOC,MAAM,CAACsN,EAAe,CAAC,EAAE,AACpE,CAEA,GAAI,CAACD,EAAM,OAAOD,EAClB,IAAM9Q,EAAU+Q,CAAI,CAAC,EAAE,CAEnBG,EAAezQ,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAACT,SAEnC,AAAI,CAACkR,GAAgBA,EAAahR,UAAU,CAACN,EAAAA,gBAAgB,EACpDkR,CADuD,EAIhEA,EAAYlO,IAAI,CAACsO,GAEVR,EACLK,EACAH,GACA,EACAE,GAEJ,EAqCsCK,EAAQE,UAAU,CAAET,GAFnC,IAGvB,CAqBO,SAASb,EACda,CAAqC,EAArCA,KAAAA,IAAAA,IAAAA,EAA2B,UAAA,EAE3B1G,MAAAA,CAAAA,EAAAA,EAAwB,CAAxBA,6BAEA,IAAMoH,EAAyBtB,EAA0BY,GAEzD,GAAI,CAACU,GAA4D,GAAG,CAArCA,EAAuBxL,MAAM,CAC1D,OAAO,KAGT,IAAMyL,EACiB,aAArBX,EACIU,CAAsB,CAAC,EAAE,CACzBA,CAAsB,CAACA,EAAuBxL,MAAM,CAAG,EAAE,CAI/D,OAAOyL,IAA0B5R,EAAAA,mBAAmB,CAChD,KACA4R,CACN", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}