{"version": 3, "sources": ["turbopack:///[project]/node_modules/next/src/server/web/spec-extension/adapters/request-cookies.ts", "turbopack:///[project]/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts", "turbopack:///[project]/node_modules/next/src/server/request/cookies.ts", "turbopack:///[project]/node_modules/next/src/server/web/spec-extension/adapters/headers.ts", "turbopack:///[project]/node_modules/next/src/server/request/headers.ts", "turbopack:///[project]/node_modules/next/src/server/request/draft-mode.ts", "turbopack:///[project]/node_modules/next/headers.js", "turbopack:///[project]/node_modules/safe-buffer/index.js", "turbopack:///[project]/node_modules/jws/lib/data-stream.js", "turbopack:///[project]/node_modules/ecdsa-sig-formatter/src/param-bytes-for-alg.js", "turbopack:///[project]/node_modules/ecdsa-sig-formatter/src/ecdsa-sig-formatter.js", "turbopack:///[project]/node_modules/buffer-equal-constant-time/index.js", "turbopack:///[project]/node_modules/jwa/index.js", "turbopack:///[project]/node_modules/jws/lib/tostring.js", "turbopack:///[project]/node_modules/jws/lib/sign-stream.js", "turbopack:///[project]/node_modules/jws/lib/verify-stream.js", "turbopack:///[project]/node_modules/jws/index.js", "turbopack:///[project]/node_modules/jsonwebtoken/decode.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/JsonWebTokenError.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/NotBeforeError.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/TokenExpiredError.js", "turbopack:///[project]/node_modules/ms/index.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/timespan.js", "turbopack:///[project]/node_modules/semver/internal/constants.js", "turbopack:///[project]/node_modules/semver/internal/debug.js", "turbopack:///[project]/node_modules/semver/internal/re.js", "turbopack:///[project]/node_modules/semver/internal/parse-options.js", "turbopack:///[project]/node_modules/semver/internal/identifiers.js", "turbopack:///[project]/node_modules/semver/classes/semver.js", "turbopack:///[project]/node_modules/semver/functions/parse.js", "turbopack:///[project]/node_modules/semver/functions/valid.js", "turbopack:///[project]/node_modules/semver/functions/clean.js", "turbopack:///[project]/node_modules/semver/functions/inc.js", "turbopack:///[project]/node_modules/semver/functions/diff.js", "turbopack:///[project]/node_modules/semver/functions/major.js", "turbopack:///[project]/node_modules/semver/functions/minor.js", "turbopack:///[project]/node_modules/semver/functions/patch.js", "turbopack:///[project]/node_modules/semver/functions/prerelease.js", "turbopack:///[project]/node_modules/semver/functions/compare.js", "turbopack:///[project]/node_modules/semver/functions/rcompare.js", "turbopack:///[project]/node_modules/semver/functions/compare-loose.js", "turbopack:///[project]/node_modules/semver/functions/compare-build.js", "turbopack:///[project]/node_modules/semver/functions/sort.js", "turbopack:///[project]/node_modules/semver/functions/rsort.js", "turbopack:///[project]/node_modules/semver/functions/gt.js", "turbopack:///[project]/node_modules/semver/functions/lt.js", "turbopack:///[project]/node_modules/semver/functions/eq.js", "turbopack:///[project]/node_modules/semver/functions/neq.js", "turbopack:///[project]/node_modules/semver/functions/gte.js", "turbopack:///[project]/node_modules/semver/functions/lte.js", "turbopack:///[project]/node_modules/semver/functions/cmp.js", "turbopack:///[project]/node_modules/semver/functions/coerce.js", "turbopack:///[project]/node_modules/semver/internal/lrucache.js", "turbopack:///[project]/node_modules/semver/classes/range.js", "turbopack:///[project]/node_modules/semver/classes/comparator.js", "turbopack:///[project]/node_modules/semver/functions/satisfies.js", "turbopack:///[project]/node_modules/semver/ranges/to-comparators.js", "turbopack:///[project]/node_modules/semver/ranges/max-satisfying.js", "turbopack:///[project]/node_modules/semver/ranges/min-satisfying.js", "turbopack:///[project]/node_modules/semver/ranges/min-version.js", "turbopack:///[project]/node_modules/semver/ranges/valid.js", "turbopack:///[project]/node_modules/semver/ranges/outside.js", "turbopack:///[project]/node_modules/semver/ranges/gtr.js", "turbopack:///[project]/node_modules/semver/ranges/ltr.js", "turbopack:///[project]/node_modules/semver/ranges/intersects.js", "turbopack:///[project]/node_modules/semver/ranges/simplify.js", "turbopack:///[project]/node_modules/semver/ranges/subset.js", "turbopack:///[project]/node_modules/semver/index.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/validateAsymmetricKey.js", "turbopack:///[project]/node_modules/jsonwebtoken/lib/psSupported.js", "turbopack:///[project]/node_modules/jsonwebtoken/verify.js", "turbopack:///[project]/node_modules/lodash.includes/index.js", "turbopack:///[project]/node_modules/lodash.isboolean/index.js", "turbopack:///[project]/node_modules/lodash.isinteger/index.js", "turbopack:///[project]/node_modules/lodash.isnumber/index.js", "turbopack:///[project]/node_modules/lodash.isplainobject/index.js", "turbopack:///[project]/node_modules/lodash.isstring/index.js", "turbopack:///[project]/node_modules/lodash.once/index.js", "turbopack:///[project]/node_modules/jsonwebtoken/sign.js", "turbopack:///[project]/node_modules/jsonwebtoken/index.js", "turbopack:///[project]/lib/prisma.ts", "turbopack:///[project]/node_modules/bcryptjs/index.js", "turbopack:///[project]/lib/auth-new.ts"], "sourcesContent": ["import { RequestCookies } from '../cookies'\n\nimport { ResponseCookies } from '../cookies'\nimport { ReflectAdapter } from './reflect'\nimport { workAsyncStorage } from '../../../app-render/work-async-storage.external'\nimport type { RequestStore } from '../../../app-render/work-unit-async-storage.external'\n\n/**\n * @internal\n */\nexport class ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      'Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\n// We use this to type some APIs but we don't construct instances directly\nexport type { ResponseCookies }\n\n// The `cookies()` API is a mix of request and response cookies. For `.get()` methods,\n// we want to return the request cookie if it exists. For mutative methods like `.set()`,\n// we want to return the response cookie.\nexport type ReadonlyRequestCookies = Omit<\n  RequestCookies,\n  'set' | 'clear' | 'delete'\n> &\n  Pick<ResponseCookies, 'set' | 'delete'>\n\nexport class RequestCookiesAdapter {\n  public static seal(cookies: RequestCookies): ReadonlyRequestCookies {\n    return new Proxy(cookies as any, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'clear':\n          case 'delete':\n          case 'set':\n            return ReadonlyRequestCookiesError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n}\n\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies')\n\nexport function getModifiedCookieValues(\n  cookies: ResponseCookies\n): ResponseCookie[] {\n  const modified: ResponseCookie[] | undefined = (cookies as unknown as any)[\n    SYMBOL_MODIFY_COOKIE_VALUES\n  ]\n  if (!modified || !Array.isArray(modified) || modified.length === 0) {\n    return []\n  }\n\n  return modified\n}\n\ntype SetCookieArgs =\n  | [key: string, value: string, cookie?: Partial<ResponseCookie>]\n  | [options: ResponseCookie]\n\nexport function appendMutableCookies(\n  headers: Headers,\n  mutableCookies: ResponseCookies\n): boolean {\n  const modifiedCookieValues = getModifiedCookieValues(mutableCookies)\n  if (modifiedCookieValues.length === 0) {\n    return false\n  }\n\n  // Return a new response that extends the response with\n  // the modified cookies as fallbacks. `res` cookies\n  // will still take precedence.\n  const resCookies = new ResponseCookies(headers)\n  const returnedCookies = resCookies.getAll()\n\n  // Set the modified cookies as fallbacks.\n  for (const cookie of modifiedCookieValues) {\n    resCookies.set(cookie)\n  }\n\n  // Set the original cookies as the final values.\n  for (const cookie of returnedCookies) {\n    resCookies.set(cookie)\n  }\n\n  return true\n}\n\ntype ResponseCookie = NonNullable<\n  ReturnType<InstanceType<typeof ResponseCookies>['get']>\n>\n\nexport class MutableRequestCookiesAdapter {\n  public static wrap(\n    cookies: RequestCookies,\n    onUpdateCookies?: (cookies: string[]) => void\n  ): ResponseCookies {\n    const responseCookies = new ResponseCookies(new Headers())\n    for (const cookie of cookies.getAll()) {\n      responseCookies.set(cookie)\n    }\n\n    let modifiedValues: ResponseCookie[] = []\n    const modifiedCookies = new Set<string>()\n    const updateResponseCookies = () => {\n      // TODO-APP: change method of getting workStore\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        workStore.pathWasRevalidated = true\n      }\n\n      const allCookies = responseCookies.getAll()\n      modifiedValues = allCookies.filter((c) => modifiedCookies.has(c.name))\n      if (onUpdateCookies) {\n        const serializedCookies: string[] = []\n        for (const cookie of modifiedValues) {\n          const tempCookies = new ResponseCookies(new Headers())\n          tempCookies.set(cookie)\n          serializedCookies.push(tempCookies.toString())\n        }\n\n        onUpdateCookies(serializedCookies)\n      }\n    }\n\n    const wrappedCookies = new Proxy(responseCookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          // A special symbol to get the modified cookie values\n          case SYMBOL_MODIFY_COOKIE_VALUES:\n            return modifiedValues\n\n          // TODO: Throw error if trying to set a cookie after the response\n          // headers have been set.\n          case 'delete':\n            return function (...args: [string] | [ResponseCookie]) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.delete(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n          case 'set':\n            return function (...args: SetCookieArgs) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.set(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    return wrappedCookies\n  }\n}\n\nexport function createCookiesWithMutableAccessCheck(\n  requestStore: RequestStore\n): ResponseCookies {\n  const wrappedCookies = new Proxy(requestStore.mutableCookies, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'delete':\n          return function (...args: [string] | [ResponseCookie]) {\n            ensureCookiesAreStillMutable(requestStore, 'cookies().delete')\n            target.delete(...args)\n            return wrappedCookies\n          }\n        case 'set':\n          return function (...args: SetCookieArgs) {\n            ensureCookiesAreStillMutable(requestStore, 'cookies().set')\n            target.set(...args)\n            return wrappedCookies\n          }\n\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  })\n  return wrappedCookies\n}\n\nexport function areCookiesMutableInCurrentPhase(requestStore: RequestStore) {\n  return requestStore.phase === 'action'\n}\n\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */\nfunction ensureCookiesAreStillMutable(\n  requestStore: RequestStore,\n  _callingExpression: string\n) {\n  if (!areCookiesMutableInCurrentPhase(requestStore)) {\n    // TODO: maybe we can give a more precise error message based on callingExpression?\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\nexport function responseCookiesToRequestCookies(\n  responseCookies: ResponseCookies\n): RequestCookies {\n  const requestCookies = new RequestCookies(new Headers())\n  for (const cookie of responseCookies.getAll()) {\n    requestCookies.set(cookie)\n  }\n  return requestCookies\n}\n", "import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Cache Components is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_CACHE_COMPONENTS\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n", "import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n          const error = new Error(\n            `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, cookies)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n          return makeHangingCookies(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`cookies`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // We need track dynamic access here eagerly to keep continuity with\n          // how cookies has worked in PPR without cacheComponents.\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // We track dynamic access here so we don't need to wrap the cookies\n          // in individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'prerender-runtime':\n          return delayUntilRuntimeStage(\n            workUnitStore,\n            makeUntrackedCookies(workUnitStore.cookies)\n          )\n        case 'private-cache':\n          if (process.env.__NEXT_CACHE_COMPONENTS) {\n            return makeUntrackedCookies(workUnitStore.cookies)\n          }\n\n          return makeUntrackedExoticCookies(workUnitStore.cookies)\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          let underlyingCookies: ReadonlyRequestCookies\n\n          if (areCookiesMutableInCurrentPhase(workUnitStore)) {\n            // We can't conditionally return different types here based on the context.\n            // To avoid confusion, we always return the readonly type here.\n            underlyingCookies =\n              workUnitStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n          } else {\n            underlyingCookies = workUnitStore.cookies\n          }\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedCookiesWithDevWarnings(\n                underlyingCookies,\n                workStore?.route\n              )\n            }\n\n            return makeUntrackedExoticCookiesWithDevWarnings(\n              underlyingCookies,\n              workStore?.route\n            )\n          } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedCookies(underlyingCookies)\n            }\n\n            return makeUntrackedExoticCookies(underlyingCookies)\n          }\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeHangingCookies(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticCookiesWithDevWarnings`, but just logging the\n// sync access without actually defining the cookies properties on the promise.\nfunction makeUntrackedCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingCookies)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...cookies()` or similar iteration')\n          break\n        }\n        case 'size':\n        case 'get':\n        case 'getAll':\n        case 'has':\n        case 'set':\n        case 'delete':\n        case 'clear':\n        case 'toString': {\n          warnForSyncAccess(route, `\\`cookies().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the cookies object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedCookies.set(underlyingCookies, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n", "import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n", "import {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `headers()` returns a Promise however you can still reference the properties of the underlying Headers instance\n * synchronously to facilitate migration. The `UnsafeUnwrappedHeaders` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `headers()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedHeaders` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `headers()` value can be awaited or you should call `headers()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedHeaders` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `headers()` will only return a Promise and you will not be able to access the underlying Headers instance\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedHeaders` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedHeaders = ReadonlyHeaders\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const callingExpression = 'headers'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"headers\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"headers\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedExoticHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"headers\" inside \"use cache: private\". Accessing \"headers\" inside a private cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"headers\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"headers\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-runtime':\n          return makeHangingHeaders(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`headers`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no cacheComponents)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how headers has worked in PPR without cacheComponents.\n          // TODO consider switching the semantic to throw on property access instead\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We are in a legacy static generation mode while prerendering\n          // We track dynamic access here so we don't need to wrap the headers in\n          // individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeadersWithDevWarnings(\n                workUnitStore.headers,\n                workStore?.route\n              )\n            }\n\n            return makeUntrackedExoticHeadersWithDevWarnings(\n              workUnitStore.headers,\n              workStore?.route\n            )\n          } else {\n            if (process.env.__NEXT_CACHE_COMPONENTS) {\n              return makeUntrackedHeaders(workUnitStore.headers)\n            }\n\n            return makeUntrackedExoticHeaders(workUnitStore.headers)\n          }\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeHangingHeaders(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: underlyingHeaders.append.bind(underlyingHeaders),\n    },\n    delete: {\n      value: underlyingHeaders.delete.bind(underlyingHeaders),\n    },\n    get: {\n      value: underlyingHeaders.get.bind(underlyingHeaders),\n    },\n    has: {\n      value: underlyingHeaders.has.bind(underlyingHeaders),\n    },\n    set: {\n      value: underlyingHeaders.set.bind(underlyingHeaders),\n    },\n    getSetCookie: {\n      value: underlyingHeaders.getSetCookie.bind(underlyingHeaders),\n    },\n    forEach: {\n      value: underlyingHeaders.forEach.bind(underlyingHeaders),\n    },\n    keys: {\n      value: underlyingHeaders.keys.bind(underlyingHeaders),\n    },\n    values: {\n      value: underlyingHeaders.values.bind(underlyingHeaders),\n    },\n    entries: {\n      value: underlyingHeaders.entries.bind(underlyingHeaders),\n    },\n    [Symbol.iterator]: {\n      value: underlyingHeaders[Symbol.iterator].bind(underlyingHeaders),\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  Object.defineProperties(promise, {\n    append: {\n      value: function append() {\n        const expression = `\\`headers().append(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.append.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    delete: {\n      value: function _delete() {\n        const expression = `\\`headers().delete(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.delete.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        const expression = `\\`headers().get(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.get.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    has: {\n      value: function has() {\n        const expression = `\\`headers().has(${describeNameArg(arguments[0])})\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.has.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    set: {\n      value: function set() {\n        const expression = `\\`headers().set(${describeNameArg(arguments[0])}, ...)\\``\n        syncIODev(route, expression)\n        return underlyingHeaders.set.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    getSetCookie: {\n      value: function getSetCookie() {\n        const expression = '`headers().getSetCookie()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.getSetCookie.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    forEach: {\n      value: function forEach() {\n        const expression = '`headers().forEach(...)`'\n        syncIODev(route, expression)\n        return underlyingHeaders.forEach.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    keys: {\n      value: function keys() {\n        const expression = '`headers().keys()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.keys.apply(underlyingHeaders, arguments as any)\n      },\n    },\n    values: {\n      value: function values() {\n        const expression = '`headers().values()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.values.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    entries: {\n      value: function entries() {\n        const expression = '`headers().entries()`'\n        syncIODev(route, expression)\n        return underlyingHeaders.entries.apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...headers()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingHeaders[Symbol.iterator].apply(\n          underlyingHeaders,\n          arguments as any\n        )\n      },\n    },\n  } satisfies HeadersExtensions)\n\n  return promise\n}\n\n// Similar to `makeUntrackedExoticHeadersWithDevWarnings`, but just logging the\n// sync access without actually defining the headers properties on the promise.\nfunction makeUntrackedHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route?: string\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(underlyingHeaders)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case Symbol.iterator: {\n          warnForSyncAccess(route, '`...headers()` or similar iteration')\n          break\n        }\n        case 'append':\n        case 'delete':\n        case 'get':\n        case 'has':\n        case 'set':\n        case 'getSetCookie':\n        case 'forEach':\n        case 'keys':\n        case 'values':\n        case 'entries': {\n          warnForSyncAccess(route, `\\`headers().${prop}\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the headers object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedHeaders.set(underlyingHeaders, proxiedPromise)\n\n  return proxiedPromise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'string' ? `'${arg}'` : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\ntype HeadersExtensions = {\n  [K in keyof ReadonlyHeaders]: unknown\n}\n", "import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\n/**\n * In this version of Next.js `draftMode()` returns a Promise however you can still reference the properties of the underlying draftMode object\n * synchronously to facilitate migration. The `UnsafeUnwrappedDraftMode` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `draftMode()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedDraftMode` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `draftMode()` value can be awaited or you should call `draftMode()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedDraftMode` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `draftMode()` will only return a Promise and you will not be able to access the underlying draftMode object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedDraftMode` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedDraftMode = DraftMode\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender-runtime':\n      // TODO(runtime-ppr): does it make sense to delay this? normally it's always microtasky\n      return delayUntilRuntimeStage(\n        workUnitStore,\n        createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n      )\n    case 'request':\n      return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store (or a runtime prerender),\n      // and if draft mode is enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      return createOrGetCachedDraftMode(null, workStore)\n\n    default:\n      return workUnitStore satisfies never\n  }\n}\n\nfunction createOrGetCachedDraftMode(\n  draftModeProvider: DraftModeProvider | null,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cacheKey = draftModeProvider ?? NullDraftMode\n  const cachedDraftMode = CachedDraftModes.get(cacheKey)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  let promise: Promise<DraftMode>\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      return createDraftModeWithDevWarnings(draftModeProvider, route)\n    }\n\n    promise = createExoticDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      return Promise.resolve(new DraftMode(draftModeProvider))\n    }\n\n    promise = createExoticDraftMode(draftModeProvider)\n  }\n\n  CachedDraftModes.set(cacheKey, promise)\n\n  return promise\n}\n\ninterface CacheLifetime {}\nconst NullDraftMode = {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createExoticDraftMode(\n  underlyingProvider: null | DraftModeProvider\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n  ;(promise as any).enable = instance.enable.bind(instance)\n  ;(promise as any).disable = instance.disable.bind(instance)\n\n  return promise\n}\n\nfunction createExoticDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  Object.defineProperty(promise, 'isEnabled', {\n    get() {\n      const expression = '`draftMode().isEnabled`'\n      syncIODev(route, expression)\n      return instance.isEnabled\n    },\n    enumerable: true,\n    configurable: true,\n  })\n\n  Object.defineProperty(promise, 'enable', {\n    value: function get() {\n      const expression = '`draftMode().enable()`'\n      syncIODev(route, expression)\n      return instance.enable.apply(instance, arguments as any)\n    },\n  })\n\n  Object.defineProperty(promise, 'disable', {\n    value: function get() {\n      const expression = '`draftMode().disable()`'\n      syncIODev(route, expression)\n      return instance.disable.apply(instance, arguments as any)\n    },\n  })\n\n  return promise\n}\n\n// Similar to `createExoticDraftModeWithDevWarnings`, but just logging the sync\n// access without actually defining the draftMode properties on the promise.\nfunction createDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'isEnabled':\n          warnForSyncAccess(route, `\\`draftMode().${prop}\\``)\n          break\n        case 'enable':\n        case 'disable': {\n          warnForSyncAccess(route, `\\`draftMode().${prop}()\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the draftMode object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  return proxiedPromise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()', this.enable)\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()', this.disable)\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string, constructorOpt: Function) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore?.phase === 'after') {\n      throw new Error(\n        `Route ${workStore.route} used \"${expression}\" inside \\`after\\`. The enabled status of draftMode can be read inside \\`after\\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"${expression}\" inside \"use cache\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, constructorOpt)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n\n        case 'prerender':\n        case 'prerender-runtime': {\n          const error = new Error(\n            `Route ${workStore.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n          )\n          return abortAndThrowOnSynchronousRequestDataAccess(\n            workStore.route,\n            expression,\n            error,\n            workUnitStore\n          )\n        }\n        case 'prerender-client':\n          const exportName = '`draftMode`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          workUnitStore.revalidate = 0\n\n          const err = new DynamicServerError(\n            `Route ${workStore.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n          )\n          workStore.dynamicUsageDescription = expression\n          workStore.dynamicUsageStack = err.stack\n\n          throw err\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n}\n", "module.exports.cookies = require('./dist/server/request/cookies').cookies\nmodule.exports.headers = require('./dist/server/request/headers').headers\nmodule.exports.draftMode = require('./dist/server/request/draft-mode').draftMode\n", "/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n", "/*global module, process*/\nvar Buffer = require('safe-buffer').Buffer;\nvar Stream = require('stream');\nvar util = require('util');\n\nfunction DataStream(data) {\n  this.buffer = null;\n  this.writable = true;\n  this.readable = true;\n\n  // No input\n  if (!data) {\n    this.buffer = Buffer.alloc(0);\n    return this;\n  }\n\n  // Stream\n  if (typeof data.pipe === 'function') {\n    this.buffer = Buffer.alloc(0);\n    data.pipe(this);\n    return this;\n  }\n\n  // Buffer or String\n  // or Object (assumedly a passworded key)\n  if (data.length || typeof data === 'object') {\n    this.buffer = data;\n    this.writable = false;\n    process.nextTick(function () {\n      this.emit('end', data);\n      this.readable = false;\n      this.emit('close');\n    }.bind(this));\n    return this;\n  }\n\n  throw new TypeError('Unexpected data type ('+ typeof data + ')');\n}\nutil.inherits(DataStream, Stream);\n\nDataStream.prototype.write = function write(data) {\n  this.buffer = Buffer.concat([this.buffer, Buffer.from(data)]);\n  this.emit('data', data);\n};\n\nDataStream.prototype.end = function end(data) {\n  if (data)\n    this.write(data);\n  this.emit('end', data);\n  this.emit('close');\n  this.writable = false;\n  this.readable = false;\n};\n\nmodule.exports = DataStream;\n", "'use strict';\n\nfunction getParamSize(keySize) {\n\tvar result = ((keySize / 8) | 0) + (keySize % 8 === 0 ? 0 : 1);\n\treturn result;\n}\n\nvar paramBytesForAlg = {\n\tES256: getParamSize(256),\n\tES384: getParamSize(384),\n\tES512: getParamSize(521)\n};\n\nfunction getParamBytesForAlg(alg) {\n\tvar paramBytes = paramBytesForAlg[alg];\n\tif (paramBytes) {\n\t\treturn paramBytes;\n\t}\n\n\tthrow new Error('Unknown algorithm \"' + alg + '\"');\n}\n\nmodule.exports = getParamBytesForAlg;\n", "'use strict';\n\nvar Buffer = require('safe-buffer').Buffer;\n\nvar getParamBytesForAlg = require('./param-bytes-for-alg');\n\nvar MAX_OCTET = 0x80,\n\tCLASS_UNIVERSAL = 0,\n\tPRIMITIVE_BIT = 0x20,\n\tTAG_SEQ = 0x10,\n\tTAG_INT = 0x02,\n\tENCODED_TAG_SEQ = (TAG_SEQ | PRIMITIVE_BIT) | (CLASS_UNIVERSAL << 6),\n\tENCODED_TAG_INT = TAG_INT | (CLASS_UNIVERSAL << 6);\n\nfunction base64Url(base64) {\n\treturn base64\n\t\t.replace(/=/g, '')\n\t\t.replace(/\\+/g, '-')\n\t\t.replace(/\\//g, '_');\n}\n\nfunction signatureAsBuffer(signature) {\n\tif (Buffer.isBuffer(signature)) {\n\t\treturn signature;\n\t} else if ('string' === typeof signature) {\n\t\treturn Buffer.from(signature, 'base64');\n\t}\n\n\tthrow new TypeError('ECDSA signature must be a Base64 string or a Buffer');\n}\n\nfunction derToJose(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\t// the DER encoded param should at most be the param size, plus a padding\n\t// zero, since due to being a signed integer\n\tvar maxEncodedParamLength = paramBytes + 1;\n\n\tvar inputLength = signature.length;\n\n\tvar offset = 0;\n\tif (signature[offset++] !== ENCODED_TAG_SEQ) {\n\t\tthrow new Error('Could not find expected \"seq\"');\n\t}\n\n\tvar seqLength = signature[offset++];\n\tif (seqLength === (MAX_OCTET | 1)) {\n\t\tseqLength = signature[offset++];\n\t}\n\n\tif (inputLength - offset < seqLength) {\n\t\tthrow new Error('\"seq\" specified length of \"' + seqLength + '\", only \"' + (inputLength - offset) + '\" remaining');\n\t}\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"r\"');\n\t}\n\n\tvar rLength = signature[offset++];\n\n\tif (inputLength - offset - 2 < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", only \"' + (inputLength - offset - 2) + '\" available');\n\t}\n\n\tif (maxEncodedParamLength < rLength) {\n\t\tthrow new Error('\"r\" specified length of \"' + rLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar rOffset = offset;\n\toffset += rLength;\n\n\tif (signature[offset++] !== ENCODED_TAG_INT) {\n\t\tthrow new Error('Could not find expected \"int\" for \"s\"');\n\t}\n\n\tvar sLength = signature[offset++];\n\n\tif (inputLength - offset !== sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", expected \"' + (inputLength - offset) + '\"');\n\t}\n\n\tif (maxEncodedParamLength < sLength) {\n\t\tthrow new Error('\"s\" specified length of \"' + sLength + '\", max of \"' + maxEncodedParamLength + '\" is acceptable');\n\t}\n\n\tvar sOffset = offset;\n\toffset += sLength;\n\n\tif (offset !== inputLength) {\n\t\tthrow new Error('Expected to consume entire buffer, but \"' + (inputLength - offset) + '\" bytes remain');\n\t}\n\n\tvar rPadding = paramBytes - rLength,\n\t\tsPadding = paramBytes - sLength;\n\n\tvar dst = Buffer.allocUnsafe(rPadding + rLength + sPadding + sLength);\n\n\tfor (offset = 0; offset < rPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, rOffset + Math.max(-rPadding, 0), rOffset + rLength);\n\n\toffset = paramBytes;\n\n\tfor (var o = offset; offset < o + sPadding; ++offset) {\n\t\tdst[offset] = 0;\n\t}\n\tsignature.copy(dst, offset, sOffset + Math.max(-sPadding, 0), sOffset + sLength);\n\n\tdst = dst.toString('base64');\n\tdst = base64Url(dst);\n\n\treturn dst;\n}\n\nfunction countPadding(buf, start, stop) {\n\tvar padding = 0;\n\twhile (start + padding < stop && buf[start + padding] === 0) {\n\t\t++padding;\n\t}\n\n\tvar needsSign = buf[start + padding] >= MAX_OCTET;\n\tif (needsSign) {\n\t\t--padding;\n\t}\n\n\treturn padding;\n}\n\nfunction joseToDer(signature, alg) {\n\tsignature = signatureAsBuffer(signature);\n\tvar paramBytes = getParamBytesForAlg(alg);\n\n\tvar signatureBytes = signature.length;\n\tif (signatureBytes !== paramBytes * 2) {\n\t\tthrow new TypeError('\"' + alg + '\" signatures must be \"' + paramBytes * 2 + '\" bytes, saw \"' + signatureBytes + '\"');\n\t}\n\n\tvar rPadding = countPadding(signature, 0, paramBytes);\n\tvar sPadding = countPadding(signature, paramBytes, signature.length);\n\tvar rLength = paramBytes - rPadding;\n\tvar sLength = paramBytes - sPadding;\n\n\tvar rsBytes = 1 + 1 + rLength + 1 + 1 + sLength;\n\n\tvar shortLength = rsBytes < MAX_OCTET;\n\n\tvar dst = Buffer.allocUnsafe((shortLength ? 2 : 3) + rsBytes);\n\n\tvar offset = 0;\n\tdst[offset++] = ENCODED_TAG_SEQ;\n\tif (shortLength) {\n\t\t// Bit 8 has value \"0\"\n\t\t// bits 7-1 give the length.\n\t\tdst[offset++] = rsBytes;\n\t} else {\n\t\t// Bit 8 of first octet has value \"1\"\n\t\t// bits 7-1 give the number of additional length octets.\n\t\tdst[offset++] = MAX_OCTET\t| 1;\n\t\t// length, base 256\n\t\tdst[offset++] = rsBytes & 0xff;\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = rLength;\n\tif (rPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\toffset += signature.copy(dst, offset, 0, paramBytes);\n\t} else {\n\t\toffset += signature.copy(dst, offset, rPadding, paramBytes);\n\t}\n\tdst[offset++] = ENCODED_TAG_INT;\n\tdst[offset++] = sLength;\n\tif (sPadding < 0) {\n\t\tdst[offset++] = 0;\n\t\tsignature.copy(dst, offset, paramBytes);\n\t} else {\n\t\tsignature.copy(dst, offset, paramBytes + sPadding);\n\t}\n\n\treturn dst;\n}\n\nmodule.exports = {\n\tderToJose: derToJose,\n\tjoseToDer: joseToDer\n};\n", "/*jshint node:true */\n'use strict';\nvar Buffer = require('buffer').Buffer; // browserify\nvar SlowBuffer = require('buffer').SlowBuffer;\n\nmodule.exports = bufferEq;\n\nfunction bufferEq(a, b) {\n\n  // shortcutting on type is necessary for correctness\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    return false;\n  }\n\n  // buffer sizes should be well-known information, so despite this\n  // shortcutting, it doesn't leak any information about the *contents* of the\n  // buffers.\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  var c = 0;\n  for (var i = 0; i < a.length; i++) {\n    /*jshint bitwise:false */\n    c |= a[i] ^ b[i]; // XOR\n  }\n  return c === 0;\n}\n\nbufferEq.install = function() {\n  Buffer.prototype.equal = SlowBuffer.prototype.equal = function equal(that) {\n    return bufferEq(this, that);\n  };\n};\n\nvar origBufEqual = Buffer.prototype.equal;\nvar origSlowBufEqual = SlowBuffer.prototype.equal;\nbufferEq.restore = function() {\n  Buffer.prototype.equal = origBufEqual;\n  SlowBuffer.prototype.equal = origSlowBufEqual;\n};\n", "var Buffer = require('safe-buffer').Buffer;\nvar crypto = require('crypto');\nvar formatEcdsa = require('ecdsa-sig-formatter');\nvar util = require('util');\n\nvar MSG_INVALID_ALGORITHM = '\"%s\" is not a valid algorithm.\\n  Supported algorithms are:\\n  \"HS256\", \"HS384\", \"HS512\", \"RS256\", \"RS384\", \"RS512\", \"PS256\", \"PS384\", \"PS512\", \"ES256\", \"ES384\", \"ES512\" and \"none\".'\nvar MSG_INVALID_SECRET = 'secret must be a string or buffer';\nvar MSG_INVALID_VERIFIER_KEY = 'key must be a string or a buffer';\nvar MSG_INVALID_SIGNER_KEY = 'key must be a string, a buffer or an object';\n\nvar supportsKeyObjects = typeof crypto.createPublicKey === 'function';\nif (supportsKeyObjects) {\n  MSG_INVALID_VERIFIER_KEY += ' or a KeyObject';\n  MSG_INVALID_SECRET += 'or a KeyObject';\n}\n\nfunction checkIsPublicKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.type !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.asymmetricKeyType !== 'string') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_VERIFIER_KEY);\n  }\n};\n\nfunction checkIsPrivateKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return;\n  }\n\n  if (typeof key === 'object') {\n    return;\n  }\n\n  throw typeError(MSG_INVALID_SIGNER_KEY);\n};\n\nfunction checkIsSecretKey(key) {\n  if (Buffer.isBuffer(key)) {\n    return;\n  }\n\n  if (typeof key === 'string') {\n    return key;\n  }\n\n  if (!supportsKeyObjects) {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key !== 'object') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (key.type !== 'secret') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n\n  if (typeof key.export !== 'function') {\n    throw typeError(MSG_INVALID_SECRET);\n  }\n}\n\nfunction fromBase64(base64) {\n  return base64\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction toBase64(base64url) {\n  base64url = base64url.toString();\n\n  var padding = 4 - base64url.length % 4;\n  if (padding !== 4) {\n    for (var i = 0; i < padding; ++i) {\n      base64url += '=';\n    }\n  }\n\n  return base64url\n    .replace(/\\-/g, '+')\n    .replace(/_/g, '/');\n}\n\nfunction typeError(template) {\n  var args = [].slice.call(arguments, 1);\n  var errMsg = util.format.bind(util, template).apply(null, args);\n  return new TypeError(errMsg);\n}\n\nfunction bufferOrString(obj) {\n  return Buffer.isBuffer(obj) || typeof obj === 'string';\n}\n\nfunction normalizeInput(thing) {\n  if (!bufferOrString(thing))\n    thing = JSON.stringify(thing);\n  return thing;\n}\n\nfunction createHmacSigner(bits) {\n  return function sign(thing, secret) {\n    checkIsSecretKey(secret);\n    thing = normalizeInput(thing);\n    var hmac = crypto.createHmac('sha' + bits, secret);\n    var sig = (hmac.update(thing), hmac.digest('base64'))\n    return fromBase64(sig);\n  }\n}\n\nvar bufferEqual;\nvar timingSafeEqual = 'timingSafeEqual' in crypto ? function timingSafeEqual(a, b) {\n  if (a.byteLength !== b.byteLength) {\n    return false;\n  }\n\n  return crypto.timingSafeEqual(a, b)\n} : function timingSafeEqual(a, b) {\n  if (!bufferEqual) {\n    bufferEqual = require('buffer-equal-constant-time');\n  }\n\n  return bufferEqual(a, b)\n}\n\nfunction createHmacVerifier(bits) {\n  return function verify(thing, signature, secret) {\n    var computedSig = createHmacSigner(bits)(thing, secret);\n    return timingSafeEqual(Buffer.from(signature), Buffer.from(computedSig));\n  }\n}\n\nfunction createKeySigner(bits) {\n return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    // Even though we are specifying \"RSA\" here, this works with ECDSA\n    // keys as well.\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign(privateKey, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify(publicKey, signature, 'base64');\n  }\n}\n\nfunction createPSSKeySigner(bits) {\n  return function sign(thing, privateKey) {\n    checkIsPrivateKey(privateKey);\n    thing = normalizeInput(thing);\n    var signer = crypto.createSign('RSA-SHA' + bits);\n    var sig = (signer.update(thing), signer.sign({\n      key: privateKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, 'base64'));\n    return fromBase64(sig);\n  }\n}\n\nfunction createPSSKeyVerifier(bits) {\n  return function verify(thing, signature, publicKey) {\n    checkIsPublicKey(publicKey);\n    thing = normalizeInput(thing);\n    signature = toBase64(signature);\n    var verifier = crypto.createVerify('RSA-SHA' + bits);\n    verifier.update(thing);\n    return verifier.verify({\n      key: publicKey,\n      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,\n      saltLength: crypto.constants.RSA_PSS_SALTLEN_DIGEST\n    }, signature, 'base64');\n  }\n}\n\nfunction createECDSASigner(bits) {\n  var inner = createKeySigner(bits);\n  return function sign() {\n    var signature = inner.apply(null, arguments);\n    signature = formatEcdsa.derToJose(signature, 'ES' + bits);\n    return signature;\n  };\n}\n\nfunction createECDSAVerifer(bits) {\n  var inner = createKeyVerifier(bits);\n  return function verify(thing, signature, publicKey) {\n    signature = formatEcdsa.joseToDer(signature, 'ES' + bits).toString('base64');\n    var result = inner(thing, signature, publicKey);\n    return result;\n  };\n}\n\nfunction createNoneSigner() {\n  return function sign() {\n    return '';\n  }\n}\n\nfunction createNoneVerifier() {\n  return function verify(thing, signature) {\n    return signature === '';\n  }\n}\n\nmodule.exports = function jwa(algorithm) {\n  var signerFactories = {\n    hs: createHmacSigner,\n    rs: createKeySigner,\n    ps: createPSSKeySigner,\n    es: createECDSASigner,\n    none: createNoneSigner,\n  }\n  var verifierFactories = {\n    hs: createHmacVerifier,\n    rs: createKeyVerifier,\n    ps: createPSSKeyVerifier,\n    es: createECDSAVerifer,\n    none: createNoneVerifier,\n  }\n  var match = algorithm.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);\n  if (!match)\n    throw typeError(MSG_INVALID_ALGORITHM, algorithm);\n  var algo = (match[1] || match[3]).toLowerCase();\n  var bits = match[2];\n\n  return {\n    sign: signerFactories[algo](bits),\n    verify: verifierFactories[algo](bits),\n  }\n};\n", "/*global module*/\nvar Buffer = require('buffer').Buffer;\n\nmodule.exports = function toString(obj) {\n  if (typeof obj === 'string')\n    return obj;\n  if (typeof obj === 'number' || Buffer.isBuffer(obj))\n    return obj.toString();\n  return JSON.stringify(obj);\n};\n", "/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\n\nfunction base64url(string, encoding) {\n  return Buffer\n    .from(string, encoding)\n    .toString('base64')\n    .replace(/=/g, '')\n    .replace(/\\+/g, '-')\n    .replace(/\\//g, '_');\n}\n\nfunction jwsSecuredInput(header, payload, encoding) {\n  encoding = encoding || 'utf8';\n  var encodedHeader = base64url(toString(header), 'binary');\n  var encodedPayload = base64url(toString(payload), encoding);\n  return util.format('%s.%s', encodedHeader, encodedPayload);\n}\n\nfunction jwsSign(opts) {\n  var header = opts.header;\n  var payload = opts.payload;\n  var secretOrKey = opts.secret || opts.privateKey;\n  var encoding = opts.encoding;\n  var algo = jwa(header.alg);\n  var securedInput = jwsSecuredInput(header, payload, encoding);\n  var signature = algo.sign(securedInput, secretOrKey);\n  return util.format('%s.%s', securedInput, signature);\n}\n\nfunction SignStream(opts) {\n  var secret = opts.secret||opts.privateKey||opts.key;\n  var secretStream = new DataStream(secret);\n  this.readable = true;\n  this.header = opts.header;\n  this.encoding = opts.encoding;\n  this.secret = this.privateKey = this.key = secretStream;\n  this.payload = new DataStream(opts.payload);\n  this.secret.once('close', function () {\n    if (!this.payload.writable && this.readable)\n      this.sign();\n  }.bind(this));\n\n  this.payload.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.sign();\n  }.bind(this));\n}\nutil.inherits(SignStream, Stream);\n\nSignStream.prototype.sign = function sign() {\n  try {\n    var signature = jwsSign({\n      header: this.header,\n      payload: this.payload.buffer,\n      secret: this.secret.buffer,\n      encoding: this.encoding\n    });\n    this.emit('done', signature);\n    this.emit('data', signature);\n    this.emit('end');\n    this.readable = false;\n    return signature;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nSignStream.sign = jwsSign;\n\nmodule.exports = SignStream;\n", "/*global module*/\nvar Buffer = require('safe-buffer').Buffer;\nvar DataStream = require('./data-stream');\nvar jwa = require('jwa');\nvar Stream = require('stream');\nvar toString = require('./tostring');\nvar util = require('util');\nvar JWS_REGEX = /^[a-zA-Z0-9\\-_]+?\\.[a-zA-Z0-9\\-_]+?\\.([a-zA-Z0-9\\-_]+)?$/;\n\nfunction isObject(thing) {\n  return Object.prototype.toString.call(thing) === '[object Object]';\n}\n\nfunction safeJsonParse(thing) {\n  if (isObject(thing))\n    return thing;\n  try { return JSON.parse(thing); }\n  catch (e) { return undefined; }\n}\n\nfunction headerFromJWS(jwsSig) {\n  var encodedHeader = jwsSig.split('.', 1)[0];\n  return safeJsonParse(Buffer.from(encodedHeader, 'base64').toString('binary'));\n}\n\nfunction securedInputFromJWS(jwsSig) {\n  return jwsSig.split('.', 2).join('.');\n}\n\nfunction signatureFromJWS(jwsSig) {\n  return jwsSig.split('.')[2];\n}\n\nfunction payloadFromJWS(jwsSig, encoding) {\n  encoding = encoding || 'utf8';\n  var payload = jwsSig.split('.')[1];\n  return Buffer.from(payload, 'base64').toString(encoding);\n}\n\nfunction isValidJws(string) {\n  return JWS_REGEX.test(string) && !!headerFromJWS(string);\n}\n\nfunction jwsVerify(jwsSig, algorithm, secretOrKey) {\n  if (!algorithm) {\n    var err = new Error(\"Missing algorithm parameter for jws.verify\");\n    err.code = \"MISSING_ALGORITHM\";\n    throw err;\n  }\n  jwsSig = toString(jwsSig);\n  var signature = signatureFromJWS(jwsSig);\n  var securedInput = securedInputFromJWS(jwsSig);\n  var algo = jwa(algorithm);\n  return algo.verify(securedInput, signature, secretOrKey);\n}\n\nfunction jwsDecode(jwsSig, opts) {\n  opts = opts || {};\n  jwsSig = toString(jwsSig);\n\n  if (!isValidJws(jwsSig))\n    return null;\n\n  var header = headerFromJWS(jwsSig);\n\n  if (!header)\n    return null;\n\n  var payload = payloadFromJWS(jwsSig);\n  if (header.typ === 'JWT' || opts.json)\n    payload = JSON.parse(payload, opts.encoding);\n\n  return {\n    header: header,\n    payload: payload,\n    signature: signatureFromJWS(jwsSig)\n  };\n}\n\nfunction VerifyStream(opts) {\n  opts = opts || {};\n  var secretOrKey = opts.secret||opts.publicKey||opts.key;\n  var secretStream = new DataStream(secretOrKey);\n  this.readable = true;\n  this.algorithm = opts.algorithm;\n  this.encoding = opts.encoding;\n  this.secret = this.publicKey = this.key = secretStream;\n  this.signature = new DataStream(opts.signature);\n  this.secret.once('close', function () {\n    if (!this.signature.writable && this.readable)\n      this.verify();\n  }.bind(this));\n\n  this.signature.once('close', function () {\n    if (!this.secret.writable && this.readable)\n      this.verify();\n  }.bind(this));\n}\nutil.inherits(VerifyStream, Stream);\nVerifyStream.prototype.verify = function verify() {\n  try {\n    var valid = jwsVerify(this.signature.buffer, this.algorithm, this.key.buffer);\n    var obj = jwsDecode(this.signature.buffer, this.encoding);\n    this.emit('done', valid, obj);\n    this.emit('data', valid);\n    this.emit('end');\n    this.readable = false;\n    return valid;\n  } catch (e) {\n    this.readable = false;\n    this.emit('error', e);\n    this.emit('close');\n  }\n};\n\nVerifyStream.decode = jwsDecode;\nVerifyStream.isValid = isValidJws;\nVerifyStream.verify = jwsVerify;\n\nmodule.exports = VerifyStream;\n", "/*global exports*/\nvar SignStream = require('./lib/sign-stream');\nvar VerifyStream = require('./lib/verify-stream');\n\nvar ALGORITHMS = [\n  'HS256', 'HS384', 'HS512',\n  'RS256', 'RS384', 'RS512',\n  'PS256', 'PS384', 'PS512',\n  'ES256', 'ES384', 'ES512'\n];\n\nexports.ALGORITHMS = ALGORITHMS;\nexports.sign = SignStream.sign;\nexports.verify = VerifyStream.verify;\nexports.decode = VerifyStream.decode;\nexports.isValid = VerifyStream.isValid;\nexports.createSign = function createSign(opts) {\n  return new SignStream(opts);\n};\nexports.createVerify = function createVerify(opts) {\n  return new VerifyStream(opts);\n};\n", "var jws = require('jws');\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n", "var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n", "var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;", "var JsonWebTokenError = require('./JsonWebTokenError');\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "var ms = require('ms');\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};", "'use strict'\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nconst SEMVER_SPEC_VERSION = '2.0.0'\n\nconst MAX_LENGTH = 256\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n/* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nconst MAX_SAFE_COMPONENT_LENGTH = 16\n\n// Max safe length for a build identifier. The max length minus 6 characters for\n// the shortest version with a build 0.0.0+BUILD.\nconst MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\nconst RELEASE_TYPES = [\n  'major',\n  'premajor',\n  'minor',\n  'preminor',\n  'patch',\n  'prepatch',\n  'prerelease',\n]\n\nmodule.exports = {\n  MAX_LENGTH,\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_SAFE_INTEGER,\n  RELEASE_TYPES,\n  SEMVER_SPEC_VERSION,\n  FLAG_INCLUDE_PRERELEASE: 0b001,\n  FLAG_LOOSE: 0b010,\n}\n", "'use strict'\n\nconst debug = (\n  typeof process === 'object' &&\n  process.env &&\n  process.env.NODE_DEBUG &&\n  /\\bsemver\\b/i.test(process.env.NODE_DEBUG)\n) ? (...args) => console.error('SEMVER', ...args)\n  : () => {}\n\nmodule.exports = debug\n", "'use strict'\n\nconst {\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_LENGTH,\n} = require('./constants')\nconst debug = require('./debug')\nexports = module.exports = {}\n\n// The actual regexps go on exports.re\nconst re = exports.re = []\nconst safeRe = exports.safeRe = []\nconst src = exports.src = []\nconst safeSrc = exports.safeSrc = []\nconst t = exports.t = {}\nlet R = 0\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nconst safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nconst makeSafeRegex = (value) => {\n  for (const [token, max] of safeRegexReplacements) {\n    value = value\n      .split(`${token}*`).join(`${token}{0,${max}}`)\n      .split(`${token}+`).join(`${token}{1,${max}}`)\n  }\n  return value\n}\n\nconst createToken = (name, value, isGlobal) => {\n  const safe = makeSafeRegex(value)\n  const index = R++\n  debug(name, index, value)\n  t[name] = index\n  src[index] = value\n  safeSrc[index] = safe\n  re[index] = new RegExp(value, isGlobal ? 'g' : undefined)\n  safeRe[index] = new RegExp(safe, isGlobal ? 'g' : undefined)\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ncreateToken('NUMERICIDENTIFIER', '0|[1-9]\\\\d*')\ncreateToken('NUMERICIDENTIFIERLOOSE', '\\\\d+')\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ncreateToken('NONNUMERICIDENTIFIER', `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`)\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ncreateToken('MAINVERSION', `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('MAINVERSIONLOOSE', `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n// Non-numberic identifiers include numberic identifiers but can be longer.\n// Therefore non-numberic identifiers must go first.\n\ncreateToken('PRERELEASEIDENTIFIER', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('PRERELEASEIDENTIFIERLOOSE', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ncreateToken('PRERELEASE', `(?:-(${src[t.PRERELEASEIDENTIFIER]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIER]})*))`)\n\ncreateToken('PRERELEASELOOSE', `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`)\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ncreateToken('BUILDIDENTIFIER', `${LETTERDASHNUMBER}+`)\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ncreateToken('BUILD', `(?:\\\\+(${src[t.BUILDIDENTIFIER]\n}(?:\\\\.${src[t.BUILDIDENTIFIER]})*))`)\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ncreateToken('FULLPLAIN', `v?${src[t.MAINVERSION]\n}${src[t.PRERELEASE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('FULL', `^${src[t.FULLPLAIN]}$`)\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ncreateToken('LOOSEPLAIN', `[v=\\\\s]*${src[t.MAINVERSIONLOOSE]\n}${src[t.PRERELEASELOOSE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('LOOSE', `^${src[t.LOOSEPLAIN]}$`)\n\ncreateToken('GTLT', '((?:<|>)?=?)')\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ncreateToken('XRANGEIDENTIFIERLOOSE', `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\\\*`)\ncreateToken('XRANGEIDENTIFIER', `${src[t.NUMERICIDENTIFIER]}|x|X|\\\\*`)\n\ncreateToken('XRANGEPLAIN', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:${src[t.PRERELEASE]})?${\n                     src[t.BUILD]}?` +\n                   `)?)?`)\n\ncreateToken('XRANGEPLAINLOOSE', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:${src[t.PRERELEASELOOSE]})?${\n                          src[t.BUILD]}?` +\n                        `)?)?`)\n\ncreateToken('XRANGE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAIN]}$`)\ncreateToken('XRANGELOOSE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ncreateToken('COERCEPLAIN', `${'(^|[^\\\\d])' +\n              '(\\\\d{1,'}${MAX_SAFE_COMPONENT_LENGTH}})` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`)\ncreateToken('COERCE', `${src[t.COERCEPLAIN]}(?:$|[^\\\\d])`)\ncreateToken('COERCEFULL', src[t.COERCEPLAIN] +\n              `(?:${src[t.PRERELEASE]})?` +\n              `(?:${src[t.BUILD]})?` +\n              `(?:$|[^\\\\d])`)\ncreateToken('COERCERTL', src[t.COERCE], true)\ncreateToken('COERCERTLFULL', src[t.COERCEFULL], true)\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ncreateToken('LONETILDE', '(?:~>?)')\n\ncreateToken('TILDETRIM', `(\\\\s*)${src[t.LONETILDE]}\\\\s+`, true)\nexports.tildeTrimReplace = '$1~'\n\ncreateToken('TILDE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('TILDELOOSE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ncreateToken('LONECARET', '(?:\\\\^)')\n\ncreateToken('CARETTRIM', `(\\\\s*)${src[t.LONECARET]}\\\\s+`, true)\nexports.caretTrimReplace = '$1^'\n\ncreateToken('CARET', `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('CARETLOOSE', `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ncreateToken('COMPARATORLOOSE', `^${src[t.GTLT]}\\\\s*(${src[t.LOOSEPLAIN]})$|^$`)\ncreateToken('COMPARATOR', `^${src[t.GTLT]}\\\\s*(${src[t.FULLPLAIN]})$|^$`)\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ncreateToken('COMPARATORTRIM', `(\\\\s*)${src[t.GTLT]\n}\\\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true)\nexports.comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ncreateToken('HYPHENRANGE', `^\\\\s*(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s+-\\\\s+` +\n                   `(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s*$`)\n\ncreateToken('HYPHENRANGELOOSE', `^\\\\s*(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s+-\\\\s+` +\n                        `(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s*$`)\n\n// Star ranges basically just allow anything at all.\ncreateToken('STAR', '(<|>)?=?\\\\s*\\\\*')\n// >=0.0.0 is like a star\ncreateToken('GTE0', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0\\\\s*$')\ncreateToken('GTE0PRE', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0-0\\\\s*$')\n", "'use strict'\n\n// parse out just the options we care about\nconst looseOption = Object.freeze({ loose: true })\nconst emptyOpts = Object.freeze({ })\nconst parseOptions = options => {\n  if (!options) {\n    return emptyOpts\n  }\n\n  if (typeof options !== 'object') {\n    return looseOption\n  }\n\n  return options\n}\nmodule.exports = parseOptions\n", "'use strict'\n\nconst numeric = /^[0-9]+$/\nconst compareIdentifiers = (a, b) => {\n  const anum = numeric.test(a)\n  const bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nconst rcompareIdentifiers = (a, b) => compareIdentifiers(b, a)\n\nmodule.exports = {\n  compareIdentifiers,\n  rcompareIdentifiers,\n}\n", "'use strict'\n\nconst debug = require('../internal/debug')\nconst { MAX_LENGTH, MAX_SAFE_INTEGER } = require('../internal/constants')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst parseOptions = require('../internal/parse-options')\nconst { compareIdentifiers } = require('../internal/identifiers')\nclass SemVer {\n  constructor (version, options) {\n    options = parseOptions(options)\n\n    if (version instanceof SemVer) {\n      if (version.loose === !!options.loose &&\n        version.includePrerelease === !!options.includePrerelease) {\n        return version\n      } else {\n        version = version.version\n      }\n    } else if (typeof version !== 'string') {\n      throw new TypeError(`Invalid version. Must be a string. Got type \"${typeof version}\".`)\n    }\n\n    if (version.length > MAX_LENGTH) {\n      throw new TypeError(\n        `version is longer than ${MAX_LENGTH} characters`\n      )\n    }\n\n    debug('SemVer', version, options)\n    this.options = options\n    this.loose = !!options.loose\n    // this isn't actually relevant for versions, but keep it so that we\n    // don't run into trouble passing this.options around.\n    this.includePrerelease = !!options.includePrerelease\n\n    const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL])\n\n    if (!m) {\n      throw new TypeError(`Invalid Version: ${version}`)\n    }\n\n    this.raw = version\n\n    // these are actually numbers\n    this.major = +m[1]\n    this.minor = +m[2]\n    this.patch = +m[3]\n\n    if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n      throw new TypeError('Invalid major version')\n    }\n\n    if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n      throw new TypeError('Invalid minor version')\n    }\n\n    if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n      throw new TypeError('Invalid patch version')\n    }\n\n    // numberify any prerelease numeric ids\n    if (!m[4]) {\n      this.prerelease = []\n    } else {\n      this.prerelease = m[4].split('.').map((id) => {\n        if (/^[0-9]+$/.test(id)) {\n          const num = +id\n          if (num >= 0 && num < MAX_SAFE_INTEGER) {\n            return num\n          }\n        }\n        return id\n      })\n    }\n\n    this.build = m[5] ? m[5].split('.') : []\n    this.format()\n  }\n\n  format () {\n    this.version = `${this.major}.${this.minor}.${this.patch}`\n    if (this.prerelease.length) {\n      this.version += `-${this.prerelease.join('.')}`\n    }\n    return this.version\n  }\n\n  toString () {\n    return this.version\n  }\n\n  compare (other) {\n    debug('SemVer.compare', this.version, this.options, other)\n    if (!(other instanceof SemVer)) {\n      if (typeof other === 'string' && other === this.version) {\n        return 0\n      }\n      other = new SemVer(other, this.options)\n    }\n\n    if (other.version === this.version) {\n      return 0\n    }\n\n    return this.compareMain(other) || this.comparePre(other)\n  }\n\n  compareMain (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    return (\n      compareIdentifiers(this.major, other.major) ||\n      compareIdentifiers(this.minor, other.minor) ||\n      compareIdentifiers(this.patch, other.patch)\n    )\n  }\n\n  comparePre (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    // NOT having a prerelease is > having one\n    if (this.prerelease.length && !other.prerelease.length) {\n      return -1\n    } else if (!this.prerelease.length && other.prerelease.length) {\n      return 1\n    } else if (!this.prerelease.length && !other.prerelease.length) {\n      return 0\n    }\n\n    let i = 0\n    do {\n      const a = this.prerelease[i]\n      const b = other.prerelease[i]\n      debug('prerelease compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  compareBuild (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    let i = 0\n    do {\n      const a = this.build[i]\n      const b = other.build[i]\n      debug('build compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  // preminor will bump the version up to the next minor release, and immediately\n  // down to pre-release. premajor and prepatch work the same way.\n  inc (release, identifier, identifierBase) {\n    if (release.startsWith('pre')) {\n      if (!identifier && identifierBase === false) {\n        throw new Error('invalid increment argument: identifier is empty')\n      }\n      // Avoid an invalid semver results\n      if (identifier) {\n        const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE])\n        if (!match || match[1] !== identifier) {\n          throw new Error(`invalid identifier: ${identifier}`)\n        }\n      }\n    }\n\n    switch (release) {\n      case 'premajor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor = 0\n        this.major++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'preminor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'prepatch':\n        // If this is already a prerelease, it will bump to the next version\n        // drop any prereleases that might already exist, since they are not\n        // relevant at this point.\n        this.prerelease.length = 0\n        this.inc('patch', identifier, identifierBase)\n        this.inc('pre', identifier, identifierBase)\n        break\n      // If the input is a non-prerelease version, this acts the same as\n      // prepatch.\n      case 'prerelease':\n        if (this.prerelease.length === 0) {\n          this.inc('patch', identifier, identifierBase)\n        }\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'release':\n        if (this.prerelease.length === 0) {\n          throw new Error(`version ${this.raw} is not a prerelease`)\n        }\n        this.prerelease.length = 0\n        break\n\n      case 'major':\n        // If this is a pre-major version, bump up to the same major version.\n        // Otherwise increment major.\n        // 1.0.0-5 bumps to 1.0.0\n        // 1.1.0 bumps to 2.0.0\n        if (\n          this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0\n        ) {\n          this.major++\n        }\n        this.minor = 0\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'minor':\n        // If this is a pre-minor version, bump up to the same minor version.\n        // Otherwise increment minor.\n        // 1.2.0-5 bumps to 1.2.0\n        // 1.2.1 bumps to 1.3.0\n        if (this.patch !== 0 || this.prerelease.length === 0) {\n          this.minor++\n        }\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'patch':\n        // If this is not a pre-release version, it will increment the patch.\n        // If it is a pre-release it will bump up to the same patch version.\n        // 1.2.0-5 patches to 1.2.0\n        // 1.2.0 patches to 1.2.1\n        if (this.prerelease.length === 0) {\n          this.patch++\n        }\n        this.prerelease = []\n        break\n      // This probably shouldn't be used publicly.\n      // 1.0.0 'pre' would become 1.0.0-0 which is the wrong direction.\n      case 'pre': {\n        const base = Number(identifierBase) ? 1 : 0\n\n        if (this.prerelease.length === 0) {\n          this.prerelease = [base]\n        } else {\n          let i = this.prerelease.length\n          while (--i >= 0) {\n            if (typeof this.prerelease[i] === 'number') {\n              this.prerelease[i]++\n              i = -2\n            }\n          }\n          if (i === -1) {\n            // didn't increment anything\n            if (identifier === this.prerelease.join('.') && identifierBase === false) {\n              throw new Error('invalid increment argument: identifier already exists')\n            }\n            this.prerelease.push(base)\n          }\n        }\n        if (identifier) {\n          // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n          // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n          let prerelease = [identifier, base]\n          if (identifierBase === false) {\n            prerelease = [identifier]\n          }\n          if (compareIdentifiers(this.prerelease[0], identifier) === 0) {\n            if (isNaN(this.prerelease[1])) {\n              this.prerelease = prerelease\n            }\n          } else {\n            this.prerelease = prerelease\n          }\n        }\n        break\n      }\n      default:\n        throw new Error(`invalid increment argument: ${release}`)\n    }\n    this.raw = this.format()\n    if (this.build.length) {\n      this.raw += `+${this.build.join('.')}`\n    }\n    return this\n  }\n}\n\nmodule.exports = SemVer\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = (version, options, throwErrors = false) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    if (!throwErrors) {\n      return null\n    }\n    throw er\n  }\n}\n\nmodule.exports = parse\n", "'use strict'\n\nconst parse = require('./parse')\nconst valid = (version, options) => {\n  const v = parse(version, options)\n  return v ? v.version : null\n}\nmodule.exports = valid\n", "'use strict'\n\nconst parse = require('./parse')\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n", "'use strict'\n\nconst parse = require('./parse.js')\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // If the main part has no difference\n    if (lowVersion.compareMain(highVersion) === 0) {\n      if (lowVersion.minor && !lowVersion.patch) {\n        return 'minor'\n      }\n      return 'patch'\n    }\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n", "'use strict'\n\nconst parse = require('./parse')\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compare = (a, b, loose) =>\n  new SemVer(a, loose).compare(new SemVer(b, loose))\n\nmodule.exports = compare\n", "'use strict'\n\nconst compare = require('./compare')\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n", "'use strict'\n\nconst compare = require('./compare')\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n", "'use strict'\n\nconst compareBuild = require('./compare-build')\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n", "'use strict'\n\nconst compareBuild = require('./compare-build')\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n", "'use strict'\n\nconst compare = require('./compare')\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n", "'use strict'\n\nconst compare = require('./compare')\nconst lt = (a, b, loose) => compare(a, b, loose) < 0\nmodule.exports = lt\n", "'use strict'\n\nconst compare = require('./compare')\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n", "'use strict'\n\nconst compare = require('./compare')\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n", "'use strict'\n\nconst compare = require('./compare')\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n", "'use strict'\n\nconst compare = require('./compare')\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n", "'use strict'\n\nconst eq = require('./eq')\nconst neq = require('./neq')\nconst gt = require('./gt')\nconst gte = require('./gte')\nconst lt = require('./lt')\nconst lte = require('./lte')\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst parse = require('./parse')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    // With includePrerelease option set, '1.2.3.4-rc' wants to coerce '2.3.4-rc', not '2.3.4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL]\n    let next\n    while ((next = coerceRtlRegex.exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    coerceRtlRegex.lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  const major = match[2]\n  const minor = match[3] || '0'\n  const patch = match[4] || '0'\n  const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : ''\n  const build = options.includePrerelease && match[6] ? `+${match[6]}` : ''\n\n  return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options)\n}\nmodule.exports = coerce\n", "'use strict'\n\nclass LRUCache {\n  constructor () {\n    this.max = 1000\n    this.map = new Map()\n  }\n\n  get (key) {\n    const value = this.map.get(key)\n    if (value === undefined) {\n      return undefined\n    } else {\n      // Remove the key from the map and add it to the end\n      this.map.delete(key)\n      this.map.set(key, value)\n      return value\n    }\n  }\n\n  delete (key) {\n    return this.map.delete(key)\n  }\n\n  set (key, value) {\n    const deleted = this.delete(key)\n\n    if (!deleted && value !== undefined) {\n      // If cache is full, delete the least recently used item\n      if (this.map.size >= this.max) {\n        const firstKey = this.map.keys().next().value\n        this.delete(firstKey)\n      }\n\n      this.map.set(key, value)\n    }\n\n    return this\n  }\n}\n\nmodule.exports = LRUCache\n", "'use strict'\n\nconst SPACE_CHARACTERS = /\\s+/g\n\n// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.formatted = undefined\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range.trim().replace(SPACE_CHARACTERS, ' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.formatted = undefined\n  }\n\n  get range () {\n    if (this.formatted === undefined) {\n      this.formatted = ''\n      for (let i = 0; i < this.set.length; i++) {\n        if (i > 0) {\n          this.formatted += '||'\n        }\n        const comps = this.set[i]\n        for (let k = 0; k < comps.length; k++) {\n          if (k > 0) {\n            this.formatted += ' '\n          }\n          this.formatted += comps[k].toString().trim()\n        }\n      }\n    }\n    return this.formatted\n  }\n\n  format () {\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = require('../internal/lrucache')\nconst cache = new LRU()\n\nconst parseOptions = require('../internal/parse-options')\nconst Comparator = require('./comparator')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = require('../internal/re')\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require('../internal/constants')\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\n// TODO build?\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n", "'use strict'\n\nconst ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = require('../internal/parse-options')\nconst { safeRe: re, t } = require('../internal/re')\nconst cmp = require('../functions/cmp')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst Range = require('./range')\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n", "'use strict'\n\nconst Range = require('../classes/range')\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst gt = require('../functions/gt')\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n", "'use strict'\n\nconst SemVer = require('../classes/semver')\nconst Comparator = require('../classes/comparator')\nconst { ANY } = Comparator\nconst Range = require('../classes/range')\nconst satisfies = require('../functions/satisfies')\nconst gt = require('../functions/gt')\nconst lt = require('../functions/lt')\nconst lte = require('../functions/lte')\nconst gte = require('../functions/gte')\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n", "'use strict'\n\n// Determine if version is greater than all the versions possible in the range.\nconst outside = require('./outside')\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n", "'use strict'\n\nconst outside = require('./outside')\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n", "'use strict'\n\nconst Range = require('../classes/range')\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n", "'use strict'\n\n// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n", "'use strict'\n\nconst Range = require('../classes/range.js')\nconst Comparator = require('../classes/comparator.js')\nconst { ANY } = Comparator\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n", "'use strict'\n\n// just pre-load all the stuff that index.js lazily exports\nconst internalRe = require('./internal/re')\nconst constants = require('./internal/constants')\nconst SemVer = require('./classes/semver')\nconst identifiers = require('./internal/identifiers')\nconst parse = require('./functions/parse')\nconst valid = require('./functions/valid')\nconst clean = require('./functions/clean')\nconst inc = require('./functions/inc')\nconst diff = require('./functions/diff')\nconst major = require('./functions/major')\nconst minor = require('./functions/minor')\nconst patch = require('./functions/patch')\nconst prerelease = require('./functions/prerelease')\nconst compare = require('./functions/compare')\nconst rcompare = require('./functions/rcompare')\nconst compareLoose = require('./functions/compare-loose')\nconst compareBuild = require('./functions/compare-build')\nconst sort = require('./functions/sort')\nconst rsort = require('./functions/rsort')\nconst gt = require('./functions/gt')\nconst lt = require('./functions/lt')\nconst eq = require('./functions/eq')\nconst neq = require('./functions/neq')\nconst gte = require('./functions/gte')\nconst lte = require('./functions/lte')\nconst cmp = require('./functions/cmp')\nconst coerce = require('./functions/coerce')\nconst Comparator = require('./classes/comparator')\nconst Range = require('./classes/range')\nconst satisfies = require('./functions/satisfies')\nconst toComparators = require('./ranges/to-comparators')\nconst maxSatisfying = require('./ranges/max-satisfying')\nconst minSatisfying = require('./ranges/min-satisfying')\nconst minVersion = require('./ranges/min-version')\nconst validRange = require('./ranges/valid')\nconst outside = require('./ranges/outside')\nconst gtr = require('./ranges/gtr')\nconst ltr = require('./ranges/ltr')\nconst intersects = require('./ranges/intersects')\nconst simplifyRange = require('./ranges/simplify')\nconst subset = require('./ranges/subset')\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n", "const semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '>=15.7.0');\n", "const semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '>=16.9.0');\n", "const ASYMMETRIC_KEY_DETAILS_SUPPORTED = require('./asymmetricKeyDetailsSupported');\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = require('./rsaPssKeyDetailsSupported');\n\nconst allowedAlgorithmsForKeys = {\n  'ec': ['ES256', 'ES384', 'ES512'],\n  'rsa': ['RS256', 'PS256', 'RS384', 'PS384', 'RS512', 'PS512'],\n  'rsa-pss': ['PS256', 'PS384', 'PS512']\n};\n\nconst allowedCurves = {\n  ES256: 'prime256v1',\n  ES384: 'secp384r1',\n  ES512: 'secp521r1',\n};\n\nmodule.exports = function(algorithm, key) {\n  if (!algorithm || !key) return;\n\n  const keyType = key.asymmetricKeyType;\n  if (!keyType) return;\n\n  const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n\n  if (!allowedAlgorithms) {\n    throw new Error(`Unknown key type \"${keyType}\".`);\n  }\n\n  if (!allowedAlgorithms.includes(algorithm)) {\n    throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(', ')}.`)\n  }\n\n  /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */\n  /* istanbul ignore next */\n  if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n    switch (keyType) {\n    case 'ec':\n      const keyCurve = key.asymmetricKeyDetails.namedCurve;\n      const allowedCurve = allowedCurves[algorithm];\n\n      if (keyCurve !== allowedCurve) {\n        throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n      }\n      break;\n\n    case 'rsa-pss':\n      if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n        const length = parseInt(algorithm.slice(-3), 10);\n        const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n\n        if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n        }\n\n        if (saltLength !== undefined && saltLength > length >> 3) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`)\n        }\n      }\n      break;\n    }\n  }\n}\n", "var semver = require('semver');\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n", "const JsonWebTokenError = require('./lib/JsonWebTokenError');\nconst NotBeforeError = require('./lib/NotBeforeError');\nconst TokenExpiredError = require('./lib/TokenExpiredError');\nconst decode = require('./decode');\nconst timespan = require('./lib/timespan');\nconst validateAsymmetricKey = require('./lib/validateAsymmetricKey');\nconst PS_SUPPORTED = require('./lib/psSupported');\nconst jws = require('jws');\nconst {KeyObject, createSecretKey, createPublicKey} = require(\"crypto\");\n\nconst PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst EC_KEY_ALGS = ['ES256', 'ES384', 'ES512'];\nconst RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  let done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== 'boolean') {\n    return done(new JsonWebTokenError('allowInvalidAsymmetricKeyTypes must be a boolean'));\n  }\n\n  const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  const parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  let decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  const header = decodedToken.header;\n  let getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    const hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n    }\n\n    if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n      try {\n        secretOrPublicKey = createPublicKey(secretOrPublicKey);\n      } catch (_) {\n        try {\n          secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === 'string' ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n        } catch (_) {\n          return done(new JsonWebTokenError('secretOrPublicKey is not valid key material'))\n        }\n      }\n    }\n\n    if (!options.algorithms) {\n      if (secretOrPublicKey.type === 'secret') {\n        options.algorithms = HS_ALGS;\n      } else if (['rsa', 'rsa-pss'].includes(secretOrPublicKey.asymmetricKeyType)) {\n        options.algorithms = RSA_KEY_ALGS\n      } else if (secretOrPublicKey.asymmetricKeyType === 'ec') {\n        options.algorithms = EC_KEY_ALGS\n      } else {\n        options.algorithms = PUB_KEY_ALGS\n      }\n    }\n\n    if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    if (header.alg.startsWith('HS') && secretOrPublicKey.type !== 'secret') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be a symmetric key when using ${header.alg}`)))\n    } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== 'public') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be an asymmetric key when using ${header.alg}`)))\n    }\n\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n      try {\n        validateAsymmetricKey(header.alg, secretOrPublicKey);\n      } catch (e) {\n        return done(e);\n      }\n    }\n\n    let valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    const payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      const audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      const target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      const match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      const invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      const signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_SAFE_INTEGER = 9007199254740991,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * A specialized version of `_.map` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the new mapped array.\n */\nfunction arrayMap(array, iteratee) {\n  var index = -1,\n      length = array ? array.length : 0,\n      result = Array(length);\n\n  while (++index < length) {\n    result[index] = iteratee(array[index], index, array);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.findIndex` and `_.findLastIndex` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {number} fromIndex The index to search from.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length,\n      index = fromIndex + (fromRight ? 1 : -1);\n\n  while ((fromRight ? index-- : ++index < length)) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.indexOf` without `fromIndex` bounds checks.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOf(array, value, fromIndex) {\n  if (value !== value) {\n    return baseFindIndex(array, baseIsNaN, fromIndex);\n  }\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return -1;\n}\n\n/**\n * The base implementation of `_.isNaN` without support for number objects.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.\n */\nfunction baseIsNaN(value) {\n  return value !== value;\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object),\n    nativeMax = Math.max;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  // Safari 9 makes `arguments.length` enumerable in strict mode.\n  var result = (isArray(value) || isArguments(value))\n    ? baseTimes(value.length, String)\n    : [];\n\n  var length = result.length,\n      skipIndexes = !!length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length &&\n    (typeof value == 'number' || reIsUint.test(value)) &&\n    (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * Checks if `value` is in `collection`. If `collection` is a string, it's\n * checked for a substring of `value`, otherwise\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * is used for equality comparisons. If `fromIndex` is negative, it's used as\n * the offset from the end of `collection`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=0] The index to search from.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {boolean} Returns `true` if `value` is found, else `false`.\n * @example\n *\n * _.includes([1, 2, 3], 1);\n * // => true\n *\n * _.includes([1, 2, 3], 1, 2);\n * // => false\n *\n * _.includes({ 'a': 1, 'b': 2 }, 1);\n * // => true\n *\n * _.includes('abcd', 'bc');\n * // => true\n */\nfunction includes(collection, value, fromIndex, guard) {\n  collection = isArrayLike(collection) ? collection : values(collection);\n  fromIndex = (fromIndex && !guard) ? toInteger(fromIndex) : 0;\n\n  var length = collection.length;\n  if (fromIndex < 0) {\n    fromIndex = nativeMax(length + fromIndex, 0);\n  }\n  return isString(collection)\n    ? (fromIndex <= length && collection.indexOf(value, fromIndex) > -1)\n    : (!!length && baseIndexOf(collection, value, fromIndex) > -1);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a string, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && objectToString.call(value) == stringTag);\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\n/**\n * Creates an array of the own enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects. See the\n * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * for more details.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keys(new Foo);\n * // => ['a', 'b'] (iteration order is not guaranteed)\n *\n * _.keys('hi');\n * // => ['0', '1']\n */\nfunction keys(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object) : baseKeys(object);\n}\n\n/**\n * Creates an array of the own enumerable string keyed property values of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property values.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.values(new Foo);\n * // => [1, 2] (iteration order is not guaranteed)\n *\n * _.values('hi');\n * // => ['h', 'i']\n */\nfunction values(object) {\n  return object ? baseValues(object, keys(object)) : [];\n}\n\nmodule.exports = includes;\n", "/**\n * lodash 3.0.3 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is classified as a boolean primitive or object.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isBoolean(false);\n * // => true\n *\n * _.isBoolean(null);\n * // => false\n */\nfunction isBoolean(value) {\n  return value === true || value === false ||\n    (isObjectLike(value) && objectToString.call(value) == boolTag);\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\nmodule.exports = isBoolean;\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is an integer.\n *\n * **Note:** This method is based on\n * [`Number.isInteger`](https://mdn.io/Number/isInteger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an integer, else `false`.\n * @example\n *\n * _.isInteger(3);\n * // => true\n *\n * _.isInteger(Number.MIN_VALUE);\n * // => false\n *\n * _.isInteger(Infinity);\n * // => false\n *\n * _.isInteger('3');\n * // => false\n */\nfunction isInteger(value) {\n  return typeof value == 'number' && value == toInteger(value);\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = isInteger;\n", "/**\n * lodash 3.0.3 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar numberTag = '[object Number]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Number` primitive or object.\n *\n * **Note:** To exclude `Infinity`, `-Infinity`, and `NaN`, which are classified\n * as numbers, use the `_.isFinite` method.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isNumber(3);\n * // => true\n *\n * _.isNumber(Number.MIN_VALUE);\n * // => true\n *\n * _.isNumber(Infinity);\n * // => true\n *\n * _.isNumber('3');\n * // => false\n */\nfunction isNumber(value) {\n  return typeof value == 'number' ||\n    (isObjectLike(value) && objectToString.call(value) == numberTag);\n}\n\nmodule.exports = isNumber;\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) ||\n      objectToString.call(value) != objectTag || isHostObject(value)) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return (typeof Ctor == 'function' &&\n    Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString);\n}\n\nmodule.exports = isPlainObject;\n", "/**\n * lodash 4.0.1 (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright 2012-2016 The Dojo Foundation <http://dojofoundation.org/>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright 2009-2016 <PERSON>, DocumentCloud and Investigative Reporters & Editors\n * Available under MIT license <https://lodash.com/license>\n */\n\n/** `Object#toString` result references. */\nvar stringTag = '[object String]';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @type Function\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `String` primitive or object.\n *\n * @static\n * @memberOf _\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n * @example\n *\n * _.isString('abc');\n * // => true\n *\n * _.isString(1);\n * // => false\n */\nfunction isString(value) {\n  return typeof value == 'string' ||\n    (!isArray(value) && isObjectLike(value) && objectToString.call(value) == stringTag);\n}\n\nmodule.exports = isString;\n", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308,\n    NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function() {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\n\n/**\n * Creates a function that is restricted to invoking `func` once. Repeat calls\n * to the function return the value of the first invocation. The `func` is\n * invoked with the `this` binding and arguments of the created function.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * var initialize = _.once(createApplication);\n * initialize();\n * initialize();\n * // => `createApplication` is invoked once\n */\nfunction once(func) {\n  return before(2, func);\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\n/**\n * Converts `value` to an integer.\n *\n * **Note:** This method is loosely based on\n * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted integer.\n * @example\n *\n * _.toInteger(3.2);\n * // => 3\n *\n * _.toInteger(Number.MIN_VALUE);\n * // => 0\n *\n * _.toInteger(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toInteger('3.2');\n * // => 3\n */\nfunction toInteger(value) {\n  var result = toFinite(value),\n      remainder = result % 1;\n\n  return result === result ? (remainder ? result - remainder : result) : 0;\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = once;\n", "const timespan = require('./lib/timespan');\nconst PS_SUPPORTED = require('./lib/psSupported');\nconst validateAsymmetricKey = require('./lib/validateAsymmetricKey');\nconst jws = require('jws');\nconst includes = require('lodash.includes');\nconst isBoolean = require('lodash.isboolean');\nconst isInteger = require('lodash.isinteger');\nconst isNumber = require('lodash.isnumber');\nconst isPlainObject = require('lodash.isplainobject');\nconst isString = require('lodash.isstring');\nconst once = require('lodash.once');\nconst { KeyObject, createSecretKey, createPrivateKey } = require('crypto')\n\nconst SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none'];\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nconst sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' },\n  allowInsecureKeySizes: { isValid: isBoolean, message: '\"allowInsecureKeySizes\" must be a boolean'},\n  allowInvalidAsymmetricKeyTypes: { isValid: isBoolean, message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'}\n};\n\nconst registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      const validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nconst options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nconst options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  const isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  const header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n    try {\n      secretOrPrivateKey = createPrivateKey(secretOrPrivateKey)\n    } catch (_) {\n      try {\n        secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === 'string' ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey)\n      } catch (_) {\n        return failure(new Error('secretOrPrivateKey is not valid key material'));\n      }\n    }\n  }\n\n  if (header.alg.startsWith('HS') && secretOrPrivateKey.type !== 'secret') {\n    return failure(new Error((`secretOrPrivateKey must be a symmetric key when using ${header.alg}`)))\n  } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n    if (secretOrPrivateKey.type !== 'private') {\n      return failure(new Error((`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`)))\n    }\n    if (!options.allowInsecureKeySizes &&\n      !header.alg.startsWith('ES') &&\n      secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n      secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n      return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    const invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  if (!options.allowInvalidAsymmetricKeyTypes) {\n    try {\n      validateAsymmetricKey(header.alg, secretOrPrivateKey);\n    } catch (error) {\n      return failure(error);\n    }\n  }\n\n  const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    const claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  const encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n          return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`))\n        }\n        callback(null, signature);\n      });\n  } else {\n    let signature = jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n    // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n    if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n      throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`)\n    }\n    return signature\n  }\n};\n", "module.exports = {\n  decode: require('./decode'),\n  verify: require('./verify'),\n  sign: require('./sign'),\n  JsonWebTokenError: require('./lib/JsonWebTokenError'),\n  NotBeforeError: require('./lib/NotBeforeError'),\n  TokenExpiredError: require('./lib/TokenExpiredError'),\n};\n", "import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as { prisma?: PrismaClient }\n\nexport const prisma =\n  globalForPrisma.prisma ?? new PrismaClient({\n    log: ['warn', 'error'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n\n", "/*\n Copyright (c) 2012 N<PERSON><PERSON> <nevins.bartolo<PERSON><EMAIL>>\n Copyright (c) 2012 <PERSON> <<EMAIL>>\n Copyright (c) 2025 <PERSON>z <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\nimport nodeCrypto from \"crypto\";\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return nodeCrypto.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nexport function setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nexport function genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nexport function hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nexport function compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nexport function compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nexport function getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nexport function getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nexport function truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nexport function encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nexport function decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\nexport default {\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n};\n", "import { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport jwt from 'jsonwebtoken'\n\nexport type AuthUser = {\n  id: string\n  email: string\n  fullName?: string | null\n  role: 'ADMIN' | 'MANAGER' | 'AGENT' | 'CUSTOMER'\n  organizationId: string\n}\n\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || 'dev-secret-change'\nconst JWT_EXPIRES_IN = '7d'\n\nexport async function verifyPassword(password: string, hash: string) {\n  return bcrypt.compare(password, hash)\n}\n\nexport function generateToken(payload: AuthUser) {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })\n}\n\nexport function verifyToken(token: string): AuthUser | null {\n  try {\n    return jwt.verify(token, JWT_SECRET) as AuthUser\n  } catch {\n    return null\n  }\n}\n\nexport async function signIn(email: string, password: string) {\n  const user = await prisma.user.findUnique({ where: { email } })\n  if (!user) return null\n\n  const valid = await verifyPassword(password, user.password)\n  if (!valid) return null\n\n  const authUser: AuthUser = {\n    id: user.id,\n    email: user.email,\n    fullName: user.fullName,\n    role: user.role as AuthUser['role'],\n    organizationId: user.organizationId,\n  }\n\n  const token = generateToken(authUser)\n  return { user: authUser, token }\n}\n\n"], "names": ["MutableRequestCookiesAdapter", "ReadonlyRequestCookiesError", "RequestCookiesAdapter", "appendMutableCookies", "areCookiesMutableInCurrentPhase", "createCookiesWithMutableAccessCheck", "getModifiedCookieValues", "responseCookiesToRequestCookies", "Error", "constructor", "callable", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "ReflectAdapter", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "modified", "Array", "isArray", "length", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "ResponseCookies", "returnedCookies", "getAll", "cookie", "set", "wrap", "onUpdateCookies", "responseCookies", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "workStore", "workAsyncStorage", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "wrappedCookies", "args", "add", "delete", "requestStore", "ensureCookiesAreStillMutable", "phase", "_callingExpression", "requestCookies", "RequestCookies", "createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_CACHE_COMPONENTS", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "message", "NODE_ENV", "callStackFrames", "stack", "split", "undefined", "callingExpression", "workUnitStore", "workUnitAsyncStorage", "isRequestAPICallableInsideAfter", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "dynamicShouldError", "StaticGenBailoutError", "type", "captureStackTrace", "invalidDynamicUsageError", "makeHangingCookies", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "delayUntilRuntimeStage", "makeUntrackedCookies", "trackDynamicDataInDynamicRender", "userspaceMutableCookies", "makeUntrackedCookiesWithDevWarnings", "makeUntrackedExoticCookiesWithDevWarnings", "throwForMissingRequestStore", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "promise", "makeHangingPromise", "renderSignal", "cachedCookies", "Promise", "resolve", "Object", "defineProperties", "iterator", "value", "bind", "polyfilledResponseCookiesIterator", "size", "clear", "polyfilledResponseCookiesClear", "makeDevtoolsIOAwarePromise", "expression", "syncIODev", "apply", "arguments", "call", "writable", "describeNameArg", "arg", "proxiedPromise", "warnForSyncAccess", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "createCookiesAccessError", "prefix", "map", "values", "returnable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReadonlyHeadersError", "lowercased", "toLowerCase", "original", "keys", "find", "o", "deleteProperty", "merge", "join", "from", "append", "existing", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "underlyingHeaders", "makeUntrackedExoticHeaders", "makeHangingHeaders", "makeUntrackedHeadersWithDevWarnings", "makeUntrackedExoticHeadersWithDevWarnings", "makeUntrackedHeaders", "CachedHeaders", "cachedHeaders", "getSetCookie", "_delete", "createHeadersAccessError", "draftMode", "createOrGetCachedDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "cache<PERSON>ey", "NullDraftMode", "cachedDraftMode", "CachedDraftModes", "isPrefetchRequest", "createDraftModeWithDevWarnings", "createExoticDraftModeWithDevWarnings", "DraftMode", "createExoticDraftMode", "underlyingProvider", "instance", "defineProperty", "isEnabled", "enumerable", "configurable", "enable", "disable", "provider", "_provider", "trackDynamicDraftMode", "createDraftModeAccessError", "constructorOpt", "abortAndThrowOnSynchronousRequestDataAccess", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack"], "mappings": "oLAsGaA,4BAA4B,CAAA,kBAA5BA,GA5FAC,2BAA2B,CAAA,kBAA3BA,GAwBAC,qBAAqB,CAAA,kBAArBA,GAoCGC,oBAAoB,CAAA,kBAApBA,GAwIAC,+BAA+B,CAAA,kBAA/BA,GA3BAC,mCAAmC,CAAA,kBAAnCA,GA9HAC,uBAAuB,CAAA,kBAAvBA,GA8KAC,+BAA+B,CAAA,kBAA/BA,aAnOe,CAAA,CAAA,IAAA,OAGA,CAAA,CAAA,IAAA,OACE,CAAA,CAAA,IAAA,EAM1B,OAAMN,UAAoCO,MAC/CC,aAAc,CACZ,KAAK,CACH,mJAEJ,CAEA,OAAcC,UAAW,CACvB,MAAM,IAAIT,CACZ,CACF,CAcO,MAAMC,EACX,OAAcS,KAAKC,CAAuB,CAA0B,CAClE,OAAO,IAAIC,MAAMD,EAAgB,CAC/BE,IAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EACxB,OAAQD,GACN,IAAK,QACL,IAAK,SACL,IAAK,MACH,OAAOf,EAA4BS,QAAQ,AAC7C,SACE,OAAOQ,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQC,EAAMC,EAC5C,CACF,CACF,EACF,CACF,CAEA,IAAME,EAA8BC,OAAOC,GAAG,CAAC,wBAExC,SAASf,EACdM,CAAwB,EAExB,IAAMU,EAA0CV,CAA0B,CACxEO,EACD,QACIG,AAAL,AAAI,GAAcC,MAAMC,GAAP,IAAc,CAACF,IAAiC,GAAG,CAAvBA,EAASG,MAAM,CAIrDH,EAHE,EAIX,AAJa,CAUN,SAASnB,EACduB,CAAgB,CAChBC,CAA+B,EAE/B,IAAMC,EAAuBtB,EAAwBqB,GACrD,GAAoC,GAAG,CAAnCC,EAAqBH,MAAM,CAC7B,OAAO,EAMT,IAAMI,EAAa,IAAIC,EAAAA,eAAe,CAACJ,GACjCK,EAAkBF,EAAWG,MAAM,GAGzC,IAAK,IAAMC,KAAUL,EACnBC,EAAWK,GAAG,CAACD,GAIjB,IAAK,IAAMA,EALgC,GAKtBF,EACnBF,EAAWK,GAAG,CAACD,GAGjB,KAJsC,EAI/B,CACT,CAMO,MAAMjC,EACX,OAAcmC,KACZvB,CAAuB,CACvBwB,CAA6C,CAC5B,CACjB,IAAMC,EAAkB,IAAIP,EAAAA,eAAe,CAAC,IAAIQ,SAChD,IAAK,IAAML,KAAUrB,EAAQoB,MAAM,GAAI,AACrCK,EAAgBH,GAAG,CAACD,GAGtB,IAAIM,EAAmC,EAAE,CACnCC,EAAkB,IAAIC,IACtBC,EAAwB,KAE5B,IAAMC,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GAO3C,GANIF,IACFA,EAAUG,KADG,aACe,EAAG,CAAA,EAIjCP,EAAiBQ,AADEV,EAAgBL,MAAM,GACbgB,MAAM,CAAC,AAACC,GAAMT,EAAgBU,GAAG,CAACD,EAAEE,IAAI,GAChEf,EAAiB,CACnB,IAAMgB,EAA8B,EAAE,CACtC,IAAK,IAAMnB,KAAUM,EAAgB,CACnC,IAAMc,EAAc,IAAIvB,EAAAA,eAAe,CAAC,IAAIQ,SAC5Ce,EAAYnB,GAAG,CAACD,GAChBmB,EAAkBE,IAAI,CAACD,EAAYE,QAAQ,GAC7C,CAEAnB,EAAgBgB,EAClB,CACF,EAEMI,EAAiB,IAAI3C,MAAMwB,EAAiB,CAChDvB,IAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EACxB,OAAQD,GAEN,KAAKG,EACH,OAAOoB,CAIT,KAAK,SACH,OAAO,SAAU,GAAGkB,CAAiC,EACnDjB,EAAgBkB,GAAG,CACE,UAAnB,OAAOD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACN,IAAI,EAEtD,GAAI,CAEF,OADApC,EAAO4C,MAAM,IAAIF,GACVD,CACT,QAAU,CACRd,GACF,CACF,CACF,KAAK,MACH,OAAO,SAAU,GAAGe,CAAmB,EACrCjB,EAAgBkB,GAAG,CACE,UAAnB,OAAOD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACN,IAAI,EAEtD,GAAI,CAEF,OADApC,EAAOmB,GAAG,IAAIuB,GACPD,CACT,QAAU,CACRd,GACF,CACF,CAEF,SACE,OAAOxB,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQC,EAAMC,EAC5C,CACF,CACF,GAEA,OAAOuC,CACT,CACF,CAEO,SAASnD,EACduD,CAA0B,EAE1B,IAAMJ,EAAiB,IAAI3C,MAAM+C,EAAajC,cAAc,CAAE,CAC5Db,IAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EACxB,OAAQD,GACN,IAAK,SACH,OAAO,SAAU,GAAGyC,CAAiC,EAGnD,OAFAI,EAA6BD,EAAc,oBAC3C7C,EAAO4C,MAAM,IAAIF,GACVD,CACT,CACF,KAAK,MACH,OAAO,SAAU,GAAGC,CAAmB,EAGrC,OAFAI,EAA6BD,EAAc,iBAC3C7C,EAAOmB,GAAG,IAAIuB,GACPD,CACT,CAEF,SACE,OAAOtC,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQC,EAAMC,EAC5C,CACF,CACF,GACA,OAAOuC,CACT,CAEO,SAASpD,EAAgCwD,CAA0B,EACxE,MAA8B,WAAvBA,EAAaE,KACtB,AAD2B,CAU3B,SAASD,EACPD,CAA0B,CAC1BG,CAA0B,EAE1B,GAAI,CAAC3D,EAAgCwD,GAEnC,MAAM,IAAI3D,CAEd,CAJsD,AAM/C,SAASM,EACd8B,CAAgC,EAEhC,IAAM2B,EAAiB,IAAIC,EAAAA,cAAc,CAAC,IAAI3B,SAC9C,IAAK,IAAML,KAAUI,EAAgBL,MAAM,GAAI,AAC7CgC,EAAe9B,GAAG,CAACD,GAErB,OAAO+B,CACT,wGClMgBE,8CAAAA,qCAAAA,odAzCO,CAAA,CAAA,IAAA,iIAEvB,IAAMC,EAAsC,CAAEC,QAAS,IAAK,EAGtDC,EACJ,AAAuB,mBAAhBC,EAAMD,KAAK,CACdC,EAAMD,KAAK,CACVE,AAAD,GAAgCA,EAKhCC,EAEFI,QAAQE,IAAI,CA0BT,EA5BgBL,OA4BPP,CA5BeQ,CA6B7BO,CAAoC,CA7BJ,CA+BhC,AA/BiCN,OA+B1B,SAASO,AAAgB,GAAGzB,CAAU,EAkBzCe,CAjDoD,CAgCtCS,EA/BhBL,GA+B8BnB,GAmBhC,CACF,CAnDYoB,AAKmBR,EAE7B,AAACW,CAyCkBG,EAhDJ,CAQb,GAAI,CACFX,EAAeL,EAASC,OAAO,CACjC,QAAU,CACRD,EAASC,OAAO,CAAG,IACrB,CACF,6BAP0E,6ECoC5DxD,UAAAA,qCAAAA,aAnDT,CAAA,CAAA,IAAA,OACwB,CAAA,CAAA,IAAA,OAIxB,CAAA,CAAA,IAAA,OAKA,CAAA,CAAA,IAAA,OAOA,CAAA,CAAA,IAAA,OAC+B,CAAA,CAAA,IAAA,OAI/B,CAAA,CAAA,IAAA,OACqD,CAAA,CAAA,IAAA,MACZ,CAAA,CAAA,IAAA,OACjB,CAAA,CAAA,IAAA,GA0BxB,SAASA,IACd,IAAM6E,EAAoB,UACpB9C,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GACrC6C,EAAgBC,EAAAA,oBAAoB,CAAC9C,QAAQ,GAEnD,GAAIF,EAAW,CACb,GACE+C,GACwB,AAAxBA,YAAc5B,KAAK,EACnB,CAAC8B,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,IAEhC,CADA,KACM,OAAA,cAGL,CAHK,AAAIpF,MAER,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,+BAD+B,0MAC0M,CAAC,EAF/P,oBAAA,OAAA,iBAAA,gBAAA,EAGN,GAGF,GAAIlD,EAAUmD,WAAW,CAIvB,CAJyB,MAIlBG,EAuGJ/F,EAAAA,qBAAqB,CAACS,CAvGSoF,GAuGL,CAAC,IAAI9B,EAAAA,cAAc,CAAC,IAAI3B,QAAQ,CAAC,MApGhE,GAAIK,EAAUuD,kBAAkB,CAC9B,CADgC,KAC1B,OAAA,cAEL,CAFK,IAAIC,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAExD,EAAUkD,KAAK,CAAC,iNAAiN,CAAC,EADvO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIH,EACF,OAAQA,EAAcU,IAAI,AADT,EAEf,IAAK,QACH,IAAMvB,EAAQ,OAAA,cAEb,CAFa,AAAIrE,MAChB,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,0UAA0U,CAAC,EADxV,oBAAA,OAAA,mBAAA,gBAAA,CAEd,EAGA,OAFArF,MAAM6F,iBAAiB,CAACxB,EAAOjE,GAC/B+B,EAAU2D,wBAAwB,GAAKzB,EACjCA,CACR,KAAK,iBACH,MAAM,OAAA,cAEL,CAFK,AAAIrE,MACR,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,mXAAmX,CAAC,EADzY,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,gBA2FXlD,EA1FkCA,EA2FlC2E,EA3F6C5B,EA6F7C,CAHoB,GAGd6B,EAAgBH,EAActG,EAFA,CAEG,CAACwG,GACxC,GAAIC,EACF,OAAOA,EAGT,IAJmB,AAIbC,EAAUC,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChCH,EAAeI,YAAY,CAC3B/E,EAAUkD,KAAK,CACf,eAIF,OAFAuB,EAAclF,GAAG,CAACoF,EAAgBE,GAE3BA,CAxGD,KAAK,mBACH,IAAMhB,EAAa,WACnB,OAAM,OAAA,cAEL,CAFK,IAAIC,EAAAA,cAAc,CACtB,CAAA,EAAGD,EAAW,0EAA0E,EAAEA,EAAW,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,gBAGH,MAAOE,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EACzB/D,EAAUkD,KAAK,CACfJ,EACAC,EAAciB,eAAe,CAEjC,KAAK,mBAGH,MAAOC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EACrCnB,EACA9C,EACA+C,EAEJ,KAAK,oBACH,MAAOmB,CAAAA,EAAAA,EAAAA,sBAAsB,AAAtBA,EACLnB,EACAoB,AAmFZ,SAASA,AACPf,CAAyC,EAEzC,IAAM4B,EAAgBP,EAActG,GAAG,CAACiF,GACxC,GAAI4B,EACF,OAAOA,EAGT,IAJmB,AAIbH,EAAUI,QAAQC,OAAO,CAAC9B,GAGhC,OAFAqB,EAAclF,GAAG,CAAC6D,EAAmByB,GAE9BA,CACT,EA/FiC9B,EAAc9E,OAAO,EAE9C,KAAK,gBAKH,OAAOqF,EAA2BP,EAAc9E,OAAO,CACzD,KAAK,UAkCD,MAjCFmG,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACrB,GAiCvBO,EA7BL7F,CAAAA,EAAAA,EAAAA,oBA6BgC2F,WA7BhC3F,AAA+B,EAACsF,GAIhCA,EAAcsB,WAJkC,YAIX,CAEnBtB,EAAc9E,OAAO,CA2B/C,CAEJ,CAGAuG,CAAAA,EAAAA,EAAAA,2BAA2B,AAA3BA,EAA4B1B,EAC9B,GAlJ+B,CAAA,CAAA,IAAA,GAyJ/B,IAAM2B,EAAgB,IAAIC,QAsC1B,SAASpB,EACPF,CAAyC,EAEzC,IAAM4B,EAAgBP,EAActG,GAAG,CAACiF,GACxC,GAAI4B,EACF,OAAOA,EAGT,IAJmB,AAIbH,EAAUI,QAAQC,OAAO,CAAC9B,GAoDhC,OAnDAqB,EAAclF,GAAG,CAAC6D,EAAmByB,GAErCM,OAAOC,gBAAgB,CAACP,EAAS,CAC/B,CAACpG,OAAO4G,QAAQ,CAAC,CAAE,CACjBC,MAAOlC,CAAiB,CAAC3E,OAAO4G,QAAQ,CAAC,CACrCjC,CAAiB,CAAC3E,OAAO4G,QAAQ,CAAC,CAACE,IAAI,CAACnC,GAMxCoC,EAAkCD,IAAI,CAACnC,EAC7C,EACAqC,KAAM,EANA,AACA,GAMJtH,IACSiF,EAAkBqC,IAAI,AAEjC,EACAtH,IAAK,CACHmH,MAAOlC,EAAkBjF,GAAG,CAACoH,IAAI,CAACnC,EACpC,EACA/D,OAAQ,CACNiG,MAAOlC,EAAkB/D,MAAM,CAACkG,IAAI,CAACnC,EACvC,EACA7C,IAAK,CACH+E,MAAOlC,EAAkB7C,GAAG,CAACgF,IAAI,CAACnC,EACpC,EACA7D,EApB2G,EAoBtG,CACH+F,MAAOlC,EAAkB7D,CApB4F,EAoBzF,CAACgG,IAAI,CAACnC,EACpC,EACApC,OAAQ,CACNsE,MAAOlC,EAAkBpC,MAAM,CAACuE,IAAI,CAACnC,EACvC,EACAsC,MAAO,CACLJ,MAEqC,YAAnC,OAAOlC,EAAkBsC,KAAK,CAE1BtC,EAAkBsC,KAAK,CAACH,IAAI,CAACnC,GAM7BuC,EAA+BJ,IAAI,CAACnC,EAAmByB,AAT8B,EAU7F,EACAjE,KANQ,AACA,IAKE,CACR0E,MAAOlC,EAAkBxC,QAAQ,CAAC2E,IAAI,CAACnC,EACzC,CACF,GAEOyB,CACT,CA4PA,SAASW,IAGP,OAAO,IAAI,CAACnG,MAAM,GACfsH,GAAG,CAAC,AAACrG,GAAM,CAACA,EAAEE,IAAI,CAAEF,EAAE,EACtBsG,MAAM,EACX,CAEA,IAhRiH,KAgRxGjB,EAEPkB,CAA2C,EAE3C,EAnR2H,EAmRtH,IAAMvH,KAAU,IAAI,CAACD,MAAM,GAAI,AAClC,IAAI,CAAC2B,MAAM,CAAC1B,EAAOkB,IAAI,EAEzB,OAAOqG,CACT,CAhC0BtF,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACnEkF,AAGF,SAASA,AACPvD,CAAyB,CACzB2C,CAAkB,EAElB,IAAMa,EAASxD,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIrF,MACT,CAAA,EAAG6I,EAAO,KAAK,EAAEb,EAAW,wHAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,kBAAA,aAEsD,CAAC,GAFvD,AAGH,CACJ,AADK,EAEP,4DAFqE,CAAC,2GCxfzDiB,cAAc,CAAA,kBAAdA,GApBAC,oBAAoB,CAAA,kBAApBA,aALkB,CAAA,CAAA,IAAA,EAKxB,OAAMA,UAA6BlJ,MACxCC,aAAc,CACZ,KAAK,CACH,qGAEJ,CAEA,OAAcC,UAAW,CACvB,MAAM,IAAIgJ,CACZ,CACF,CAUO,MAAMD,UAAuBnH,QAGlC7B,YAAYiB,CAA4B,CAAE,CAGxC,KAAK,GAEL,IAAI,CAACA,OAAO,CAAG,IAAIb,MAAMa,EAAS,CAChCZ,IAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EAIxB,GAAI,AAAgB,UAAU,OAAnBD,EACT,OAAOE,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQC,EAAMC,GAG1C,IAAM0I,EAAa3I,EAAK4I,WAAW,GAK7BC,EAAW/B,OAAOgC,IAAI,CAACpI,GAASqI,IAAI,CACxC,AAACC,GAAMA,EAAEJ,WAAW,KAAOD,GAI7B,GAAI,KAAoB,IAAbE,EAGX,OAHqC,AAG9B3I,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQ8I,EAAU5I,EAC9C,EACAiB,IAAInB,CAAM,CAAEC,CAAI,CAAEiH,CAAK,CAAEhH,CAAQ,EAC/B,GAAoB,UAAhB,AAA0B,OAAnBD,EACT,OAAOE,EAAAA,cAAc,CAACgB,GAAG,CAACnB,EAAQC,EAAMiH,EAAOhH,GAGjD,IAAM0I,EAAa3I,EAAK4I,WAAW,GAK7BC,EAAW/B,OAAOgC,IAAI,CAACpI,GAASqI,IAAI,CACxC,AAACC,GAAMA,EAAEJ,WAAW,KAAOD,GAI7B,OAAOzI,EAAAA,cAAc,CAACgB,GAAG,CAACnB,EAAQ8I,GAAY7I,EAAMiH,EAAOhH,EAC7D,EACAiC,IAAInC,CAAM,CAAEC,CAAI,EACd,GAAoB,UAAhB,OAAOA,EAAmB,OAAOE,EAAAA,cAAc,CAACgC,GAAG,CAACnC,EAAQC,GAEhE,IAAM2I,EAAa3I,EAAK4I,WAAW,GAK7BC,EAAW/B,OAAOgC,IAAI,CAACpI,GAASqI,IAAI,CACxC,AAACC,GAAMA,EAAEJ,WAAW,KAAOD,UAI7B,IAAI,CAAoB,IAAbE,GAGJ3I,EAAAA,IAH8B,OAAO,GAGvB,CAACgC,GAAG,CAACnC,EAAQ8I,EACpC,EACAI,eAAelJ,CAAM,CAAEC,CAAI,EACzB,GAAoB,UAAhB,OAAOA,EACT,OAAOE,EAAAA,cAAc,CAAC+I,cAAc,CAAClJ,EAAQC,GAE/C,IAAM2I,EAAa3I,EAAK4I,WAAW,GAK7BC,EAAW/B,OAAOgC,IAAI,CAACpI,GAASqI,IAAI,CACxC,AAACC,GAAMA,EAAEJ,WAAW,KAAOD,UAI7B,IAAI,CAAoB,IAAbE,GAGJ3I,EAAAA,IAH8B,OAAO,GAGvB,CAAC+I,cAAc,CAAClJ,EAAQ8I,EAC/C,CACF,EACF,CAMA,OAAclJ,KAAKe,CAAgB,CAAmB,CACpD,OAAO,IAAIb,MAAuBa,EAAS,CACzCZ,IAAIC,CAAM,CAAEC,CAAI,CAAEC,CAAQ,EACxB,OAAQD,GACN,IAAK,SACL,IAAK,SACL,IAAK,MACH,OAAO0I,EAAqBhJ,QAAQ,AACtC,SACE,OAAOQ,EAAAA,cAAc,CAACJ,GAAG,CAACC,EAAQC,EAAMC,EAC5C,CACF,CACF,EACF,CASQiJ,MAAMjC,CAAwB,CAAU,QAC9C,AAAI1G,MAAMC,OAAO,CAACyG,GAAeA,EAAMkC,GAAb,CAAiB,CAAC,MAErClC,CACT,CAQA,OAAcmC,KAAK1I,CAAsC,CAAW,QAClE,AAAIA,aAAmBY,QAAgBZ,CAAP,CAEzB,IAAI+H,EAAe/H,EAC5B,CAEO2I,OAAOlH,CAAY,CAAE8E,CAAa,CAAQ,CAC/C,IAAMqC,EAAW,IAAI,CAAC5I,OAAO,CAACyB,EAAK,CACX,UAApB,AAA8B,OAAvBmH,EACT,IAAI,CAAC5I,OAAO,CAACyB,EAAK,CAAG,CAACmH,EAAUrC,EAAM,CAC7B1G,MAAMC,OAAO,CAAC8I,GACvBA,EAAShH,IAAI,CAAC2E,CADoB,EAGlC,IAAI,CAACvG,OAAO,CAACyB,EAAK,CAAG8E,CAEzB,CAEOtE,OAAOR,CAAY,CAAQ,CAChC,OAAO,IAAI,CAACzB,OAAO,CAACyB,EAAK,AAC3B,CAEOrC,IAAIqC,CAAY,CAAiB,CACtC,IAAM8E,EAAQ,IAAI,CAACvG,OAAO,CAACyB,EAAK,QAChC,AAAI,AAAiB,SAAV8E,EAA8B,EAAP,EAAW,CAACiC,KAAK,CAACjC,GAE7C,IACT,CAEO/E,IAAIC,CAAY,CAAW,CAChC,OAAO,KAA8B,IAAvB,IAAI,CAACzB,OAAO,CAACyB,EAAK,AAClC,CAEOjB,IAAIiB,CAAY,CAAE8E,CAAa,CAAQ,CAC5C,IAAI,CAACvG,OAAO,CAACyB,EAAK,CAAG8E,CACvB,CAEOsC,QACLC,CAAkE,CAClEC,CAAa,CACP,CACN,IAAK,GAAM,CAACtH,EAAM8E,EAAM,GAAI,IAAI,CAACyC,OAAO,GAAI,AAC1CF,EAAW5B,IAAI,CAAC6B,EAASxC,EAAO9E,EAAM,IAAI,CAE9C,CAEA,CAAQuH,SAA6C,CACnD,IAAK,IAAM1F,KAAO8C,OAAOgC,IAAI,CAAC,IAAI,CAACpI,OAAO,EAAG,CAC3C,IAAMyB,EAAO6B,EAAI4E,WAAW,GAGtB3B,EAAQ,IAAI,CAACnH,GAAG,CAACqC,EAEvB,MAAM,CAACA,EAAM8E,EAAM,AACrB,CACF,CAEA,CAAQ6B,MAAgC,CACtC,IAAK,IAAM9E,KAAO8C,OAAOgC,IAAI,CAAC,IAAI,CAACpI,OAAO,EAAG,CAC3C,IAAMyB,EAAO6B,EAAI4E,WAAW,EAC5B,OAAMzG,CACR,CACF,CAEA,CAAQoG,QAAkC,CACxC,IAAK,IAAMvE,KAAO8C,OAAOgC,IAAI,CAAC,IAAI,CAACpI,OAAO,EAAG,CAG3C,IAAMuG,EAAQ,IAAI,CAACnH,GAAG,CAACkE,EAEvB,OAAMiD,CACR,CACF,CAEO,CAAC7G,OAAO4G,QAAQ,CAAC,EAAsC,CAC5D,OAAO,IAAI,CAAC0C,OAAO,EACrB,CACF,yGCzKgBhJ,UAAAA,qCAAAA,aA1DT,CAAA,CAAA,IAAA,OAIA,CAAA,CAAA,IAAA,OAKA,CAAA,CAAA,IAAA,OAMA,CAAA,CAAA,IAAA,OAC+B,CAAA,CAAA,IAAA,OAI/B,CAAA,CAAA,IAAA,OACqD,CAAA,CAAA,IAAA,MACZ,CAAA,CAAA,IAAA,OACjB,CAAA,CAAA,IAAA,GAmCxB,SAASA,IACd,IAAM+D,EAAoB,UACpB9C,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GACrC6C,EAAgBC,EAAAA,oBAAoB,CAAC9C,QAAQ,GAEnD,GAAIF,EAAW,CACb,GACE+C,GACwB,UAAxBA,EAAc5B,KAAK,EACnB,CAAC8B,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,IAEhC,CADA,KACM,OAAA,cAEL,CAFK,AAAIpF,MACR,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,yOAAyO,CAAC,EAD/P,oBAAA,OAAA,kBAAA,iBAAA,CAEN,GAGF,GAAIlD,EAAUmD,WAAW,CAIvB,CAJyB,MAIlB8E,EADmBnB,EAAAA,cAAc,CAAC9I,IAAI,CAAC,GACZgK,CADgBrI,QAAQ,CAAC,KAI7D,GAAIoD,EACF,OAAQA,EAAcU,IADL,AACS,EACxB,IAAK,QAAS,CACZ,IAAMvB,EAAQ,OAAA,cAEb,CAFiBrE,AAAJ,MACZ,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,0UAA0U,CAAC,EADxV,oBAAA,OAAA,mBAAA,gBAAA,CAEd,EAGA,OAFArF,MAAM6F,iBAAiB,CAACxB,EAAOnD,GAC/BiB,EAAU2D,wBAAwB,GAAKzB,EACjCA,CACR,CACA,IAAK,gBAAiB,CACpB,IAAMA,EAAQ,OAAA,cAEb,CAFa,AAAIrE,MAChB,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,gVAAgV,CAAC,EAD9V,oBAAA,OAAA,mBAAA,gBAAA,CAEd,EAGA,OAFArF,MAAM6F,iBAAiB,CAACxB,EAAOnD,GAC/BiB,EAAU2D,wBAAwB,GAAKzB,EACjCA,CACR,CACA,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,AAAIrE,MACR,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,mXAAmX,CAAC,EADzY,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAUJ,CAGF,GAAIlD,EAAUuD,kBAAkB,CAC9B,CADgC,KAC1B,OAAA,cAEL,CAFK,IAAIC,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAExD,EAAUkD,KAAK,CAAC,iNAAiN,CAAC,EADvO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAIH,EACF,OAAQA,EAAcU,IADL,AACS,EACxB,IAAK,YACL,IAAK,wBAmEXzD,EAlEkCA,EAmElC2E,EAnE6C5B,EAqE7C,CAHoB,GAGdwF,EAAgBD,EAAcnK,EAFA,CAEG,CAACwG,GACxC,GAAI4D,EACF,OAAOA,EAGT,IAAM1D,AAJa,EAIHC,GAAAA,EAAAA,kBAAAA,AAAkB,EAChCH,EAAeI,YAAY,CAC3B/E,EAAUkD,KAAK,CACf,eAIF,OAFAoF,EAAc/I,GAAG,CAACoF,EAAgBE,GAE3BA,CAhFD,KAAK,mBACH,IAAMhB,EAAa,WACnB,OAAM,OAAA,cAEL,CAFK,IAAIC,EAAAA,cAAc,CACtB,CAAA,EAAGD,EAAW,0EAA0E,EAAEA,EAAW,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,gBAKH,MAAOE,CAAAA,EAAAA,EAAAA,oBAAoB,AAApBA,EACL/D,EAAUkD,KAAK,CACfJ,EACAC,EAAciB,eAAe,CAEjC,KAAK,mBAKH,MAAOC,CAAAA,EAAAA,EAAAA,gCAAAA,AAAgC,EACrCnB,EACA9C,EACA+C,EAEJ,KAAK,UAuBD,MAtBFqB,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACrB,GAsBvBkF,EAA2BlF,EAAchE,OAAO,CAK7D,CAEJ,CAGAyF,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EAAC1B,EAC9B,GAjK+B,CAAA,CAAA,IAAA,GAoK/B,IAAMwF,EAAgB,IAAI5D,QAmC1B,SAASuD,EACPD,CAAkC,EAElC,IAAMO,EAAgBD,EAAcnK,GAAG,CAAC6J,GACxC,GAAIO,EACF,OAAOA,EAGT,IAJmB,AAIb1D,EAAUI,QAAQC,OAAO,CAAC8C,GAuChC,OAtCAM,EAAc/I,GAAG,CAACyI,EAAmBnD,GAErCM,OAAOC,gBAAgB,CAACP,EAAS,CAC/B6C,OAAQ,CACNpC,MAAO0C,EAAkBN,MAAM,CAACnC,IAAI,CAACyC,EACvC,EACAhH,OAAQ,CACNsE,MAAO0C,EAAkBhH,MAAM,CAACuE,IAAI,CAACyC,EACvC,EACA7J,IAAK,CACHmH,MAAO0C,EAAkB7J,GAAG,CAACoH,IAAI,CAACyC,EACpC,EACAzH,IAAK,CACH+E,MAAO0C,EAAkBzH,GAAG,CAACgF,IAAI,CAACyC,EACpC,EACAzI,IAAK,CACH+F,MAAO0C,EAAkBzI,GAAG,CAACgG,IAAI,CAACyC,EACpC,EACAQ,aAAc,CACZlD,MAAO0C,EAAkBQ,YAAY,CAACjD,IAAI,CAACyC,EAC7C,EACAJ,QAAS,CACPtC,MAAO0C,EAAkBJ,OAAO,CAACrC,IAAI,CAACyC,EACxC,EACAb,KAAM,CACJ7B,MAAO0C,EAAkBb,IAAI,CAAC5B,IAAI,CAACyC,EACrC,EACApB,OAAQ,CACNtB,MAAO0C,EAAkBpB,MAAM,CAACrB,IAAI,CAACyC,EACvC,EACAD,QAAS,CACPzC,MAAO0C,EAAkBD,OAAO,CAACxC,IAAI,CAACyC,EACxC,EACA,CAACvJ,OAAO4G,QAAQ,CAAC,CAAE,CACjBC,MAAO0C,CAAiB,CAACvJ,OAAO4G,QAAQ,CAAC,CAACE,IAAI,CAACyC,EACjD,CACF,GAEOnD,CACT,CAwM0BtD,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACnEmH,AAGF,SACExF,AADOwF,CACkB,CACzB7C,CAAkB,EAElB,IAAMa,EAASxD,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIrF,MACT,CAAA,EAAG6I,EAAO,KAAK,EAAEb,EAAW,wHAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,YAEsD,CAAC,GAC1D,AAHG,CAIP,AADK,EAEP,4DAFqE,CAAC,6CCtbtD8C,YAAAA,qCAAAA,aA7CT,CAAA,CAAA,IAAA,OAOA,CAAA,CAAA,IAAA,OAQA,CAAA,CAAA,IAAA,OACqD,CAAA,CAAA,IAAA,MACtB,CAAA,CAAA,IAAA,OACH,CAAA,CAAA,IAAA,OACJ,CAAA,CAAA,IAAA,GA0BxB,SAASA,IAEd,IAAM3I,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GACrC6C,EAAgBC,EAAAA,oBAAoB,CAAC9C,QAAQ,GAMnD,QAJI,CAACF,GAAa,CAAC+C,CAAAA,GAAe,AAChCyB,CAAAA,EAAAA,EAAAA,2BAAAA,AAA2B,EALH,AAKI1B,aAGtBC,EAAcU,IAAI,EACxB,IAAK,oBAEH,MAAOS,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAC3BnB,EACA6F,EAA2B7F,EAAc4F,SAAS,CAAE3I,GAExD,KAAK,UACH,OAAO4I,EAA2B7F,EAAc4F,SAAS,CAAE3I,EAE7D,KAAK,QACL,IAAK,gBACL,IAAK,iBAIH,IAAM6I,EAAoBC,CAAAA,EAAAA,EAAAA,iCAAAA,AAAiC,EACzD9I,EACA+C,GAGF,GAAI8F,EACF,OAAOD,EAA2BC,EAAmB7I,EAKzD,IANyB,CAMpB,YACL,IAAK,mBACL,IAAK,gBACL,IAAK,mBAEH,OAAO4I,EAA2B,KAAM5I,EAE1C,SACE,OAAO+C,CACX,CACF,CAEA,SAAS6F,EACPC,CAA2C,CAC3C7I,CAAgC,EAEhC,IAOI6E,EAPEkE,EAAWF,GAAqBG,EAChCC,EAAkBC,EAAiB/K,GAAG,CAAC4K,UAE7C,AAAIE,IAmBFpE,EAAU0E,AAYd,SAASA,AACPC,CAA4C,CAhCvB,CAkCrB,IAAMC,EAAW,IAAIH,EAAUE,GACzB3E,EAAUI,QAAQC,OAAO,CAACuE,GAYhC,OAVAtE,OAAOuE,cAAc,CAAC7E,EAAS,YAAa,CAC1C1G,QACSsL,EAASE,SAAS,CAE3BC,YAAY,EACZC,cAAc,CAChB,GACEhF,EAAgBiF,MAAM,CAAGL,EAASK,MAAM,CAACvE,IAAI,CAACkE,GAC9C5E,EAAgBkF,OAAO,CAAGN,EAASM,OAAO,CAACxE,IAAI,CAACkE,GAE3C5E,CACT,EA7BoCgE,GAGlCK,EAAiB3J,GAAG,CAACwJ,EAAUlE,GAExBA,EACT,GAzG+B,CAAA,CAAA,IAAA,GA4G/B,IAAMmE,EAAgB,CAAC,EACjBE,EAAmB,IAAIxE,OAyF7B,OAAM4E,EAMJxL,YAAYkM,CAAkC,CAAE,CAC9C,IAAI,CAACC,SAAS,CAAGD,CACnB,CACA,IAAIL,WAAY,QACS,AAAvB,MAA6B,CAAzB,IAAI,CAACM,SAAS,EACT,IAAI,CAACA,SAAS,CAACN,SAAS,AAGnC,CACOG,QAAS,CAGdI,EAAsB,uBAAwB,IAAI,CAACJ,MAAM,EAClC,MAAM,CAAzB,IAAI,CAACG,SAAS,EAChB,IAAI,CAACA,SAAS,CAACH,MAAM,EAEzB,CACOC,SAAU,CACfG,EAAsB,wBAAyB,IAAI,CAACH,OAAO,EACpC,AAAnB,MAAyB,KAArB,CAACE,SAAS,EAChB,IAAI,CAACA,SAAS,CAACF,OAAO,EAE1B,CACF,CAgDA,SAASG,EAAsBrE,CAAkB,CAAEuE,CAAwB,EACzE,IAAMpK,EAAYC,EAAAA,gBAAgB,CAACC,QAAQ,GACrC6C,EAAgBC,EAAAA,oBAAoB,CAAC9C,QAAQ,GAEnD,GAAIF,EAAW,CAGb,GAAI+C,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAe5B,KAAAA,AAAK,IAAK,QAC3B,CADoC,KAC9B,OAAA,cAEL,CAFK,AAAItD,MACR,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,OAAO,EAAE2C,EAAW,0MAA0M,CAAC,EADpP,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAI7F,EAAUuD,kBAAkB,CAC9B,CADgC,KAC1B,OAAA,cAEL,CAFK,IAAIC,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAExD,EAAUkD,KAAK,CAAC,8EAA8E,EAAE2C,EAAW,4HAA4H,CAAC,EAD7O,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAI9C,EACF,OAAQA,EAAcU,IADL,AACS,EACxB,IAAK,QACL,IAAK,gBAAiB,CACpB,IAAMvB,EAAQ,OAAA,cAEb,CAFa,AAAIrE,MAChB,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,OAAO,EAAE2C,EAAW,uNAAuN,CAAC,EADzP,oBAAA,OAAA,mBAAA,eAAA,EAEd,EAGA,OAFAhI,MAAM6F,iBAAiB,CAACxB,EAAOkI,GAC/BpK,EAAU2D,wBAAwB,GAAKzB,EACjCA,CACR,CACA,IAAK,iBACH,MAAM,OAAA,cAEL,CAFK,AAAIrE,MACR,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,OAAO,EAAE2C,EAAW,gQAAgQ,CAAC,EAD1S,oBAAA,OAAA,kBAAA,iBAAA,CAEN,EAEF,KAAK,YACL,IAAK,oBAAqB,CACxB,IAAM3D,EAAQ,OAAA,cAEb,CAFa,AAAIrE,MAChB,CAAC,MAAM,EAAEmC,EAAUkD,KAAK,CAAC,MAAM,EAAE2C,EAAW,+HAA+H,CAAC,EADhK,oBAAA,OAAA,mBAAA,gBAAA,CAEd,GACA,MAAOwE,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EAChDrK,EAAUkD,KAAK,CACf2C,EACA3D,EACAa,EAEJ,CACA,IAAK,mBACH,IAAMc,EAAa,aACnB,OAAM,OAAA,cAEL,CAFK,IAAIC,EAAAA,cAAc,CACtB,CAAA,EAAGD,EAAW,0EAA0E,EAAEA,EAAW,+EAA+E,CAAC,EADjL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,gBACH,MAAOE,CAAAA,EAAAA,EAAAA,oBAAAA,AAAoB,EACzB/D,EAAUkD,KAAK,CACf2C,EACA9C,EAAciB,eAAe,CAEjC,KAAK,mBACHjB,EAAcuH,UAAU,CAAG,EAE3B,IAAMC,EAAM,OAAA,cAEX,CAFW,IAAIC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAExK,EAAUkD,KAAK,CAAC,mDAAmD,EAAE2C,EAAW,6EAA6E,CAAC,EAD7J,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHA7F,EAAUyK,uBAAuB,CAAG5E,EACpC7F,EAAU0K,iBAAiB,CAAGH,EAAI5H,KAAK,CAEjC4H,CACR,KAAK,UACHnG,CAAAA,EAAAA,EAAAA,+BAAAA,AAA+B,EAACrB,EAIpC,CAEJ,CACF,CA5F0BxB,CAAAA,EAAAA,EAAAA,2CAAAA,AAA2C,EACnE4I,AAGF,SAASA,AACPjH,CAAyB,CACzB2C,CAAkB,EAElB,IAAMa,EAASxD,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,OAAA,cAIN,CAJM,AAAIrF,MACT,CAAA,EAAG6I,EAAO,KAAK,EAAEb,EAAW,0HAAE,CAAC,EAD1B,CAEH,CAAC,kBAFE,OAAA,mBAAA,cAEwD,CAAC,CAFzD,CAIP,CADI,CAAC,AAEP,mBCxSA,EAAO,OAAO,CAAC,OAAO,CAAG,EAAA,CAAA,CAAA,OAAyC,OAAO,CACzE,EAAO,IDqS8D,CAAC,ECrSxD,CAAC,OAAO,CAAG,EAAA,CAAA,CAAA,OAAyC,OAAO,CACzE,EAAO,OAAO,CAAC,SAAS,CAAG,EAAA,CAAA,CAAA,OAA4C,SAAS,kBCAhF,IAAI,EAAA,EAAA,CAAA,CAAA,KACA,EAAS,EAAO,MAAM,CAG1B,SAAS,EAAW,CAAG,CAAE,CAAG,EAC1B,IAAK,IAAI,KAAO,EACd,CAAG,CADgB,AACf,EAAI,CAAG,CAAG,CAAC,EAAI,AAEvB,CASA,SAAS,EAAY,CAAG,CAAE,CAAgB,CAAE,CAAM,EAChD,OAAO,EAAO,EAAK,EAAkB,EACvC,CAVI,EAAO,IAAI,EAAI,EAAO,KAAK,EAAI,EAAO,WAAW,EAAI,EAAO,eAAe,CAC7E,CAD+E,CACxE,OAAO,CAAG,GAGjB,EAAU,EAAQ,GAClB,EAAQ,MAAM,CAAG,GAOnB,EAAW,SAAS,CAAG,OAAO,MAAM,CAAC,EAAO,SAAS,EAGrD,EAAU,EAAQ,GAElB,EAAW,IAAI,CAAG,SAAU,CAAG,CAAE,CAAgB,CAAE,CAAM,EACvD,GAAmB,UAAf,AAAyB,OAAlB,EACT,MAAM,AAAI,UAAU,iCAEtB,OAAO,EAAO,EAAK,EAAkB,EACvC,EAEA,EAAW,KAAK,CAAG,SAAU,CAAI,CAAE,CAAI,CAAE,CAAQ,EAC/C,GAAoB,UAAU,AAA1B,OAAO,EACT,MAAM,AAAI,UAAU,6BAEtB,IAAI,EAAM,EAAO,GAUjB,YATa,IAAT,EACE,AAAoB,KADF,KACY,OAAvB,EACT,EAAI,IAAI,CAAC,EAAM,GAEf,EAAI,IAAI,CAAC,GAGX,EAAI,IAAI,CAAC,GAEJ,CACT,EAEA,EAAW,WAAW,CAAG,SAAU,CAAI,EACrC,GAAI,AAAgB,UAAU,OAAnB,EACT,MAAM,AAAI,UAAU,6BAEtB,OAAO,EAAO,EAChB,EAEA,EAAW,eAAe,CAAG,SAAU,CAAI,EACzC,GAAoB,UAAU,AAA1B,OAAO,EACT,MAAM,AAAI,UAAU,6BAEtB,OAAO,EAAO,UAAU,CAAC,EAC3B,mBC/DA,IAAI,EAAS,EAAA,CAAA,CAAA,OAAuB,MAAM,CACtC,EAAA,EAAA,CAAA,CAAA,OAGJ,SAAS,EAAW,CAAI,EAMtB,GALA,IAAI,CAAC,MAAM,CAAG,KACd,IAAI,CAAC,QAAQ,CAAG,GAChB,IAAI,CAAC,QAAQ,EAAG,EAGZ,CAAC,EAEH,IAFS,GACT,IAAI,CAAC,MAAM,CAAG,EAAO,KAAK,CAAC,GACpB,IAAI,CAIb,GAAyB,YAAY,AAAjC,OAAO,EAAK,IAAI,CAGlB,OAFA,IAAI,CAAC,MAAM,CAAG,EAAO,KAAK,CAAC,GAC3B,EAAK,IAAI,CAAC,IAAI,EACP,IAAI,CAKb,GAAI,EAAK,MAAM,EAAoB,AAAhB,UAA0B,OAAnB,EAQxB,OAPA,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,QAAQ,EAAG,EAChB,QAAQ,QAAQ,CAAC,CAAA,WACf,IAAI,CAAC,IAAI,CAAC,MAAO,GACjB,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,IAAI,CAAC,SACZ,CAAA,CAAE,IAAI,CAAC,IAAI,GACJ,IAAI,AAGb,OAAM,AAAI,UAAU,yBAA0B,OAAO,EAAO,IAC9D,CACA,AAnCI,EAAA,CAAA,CAAA,OAmCC,QAAQ,CAAC,EAAY,GAE1B,EAAW,SAAS,CAAC,KAAK,CAAG,SAAS,AAAM,CAAI,EAC9C,IAAI,CAAC,MAAM,CAAG,EAAO,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAE,EAAO,IAAI,CAAC,GAAM,EAC5D,IAAI,CAAC,IAAI,CAAC,OAAQ,EACpB,EAEA,EAAW,SAAS,CAAC,GAAG,CAAG,SAAS,AAAI,CAAI,EACtC,GACF,IAAI,CAAC,KAAK,CAAC,GACb,IAAI,CAAC,IAAI,CAAC,MAAO,GACjB,IAAI,CAAC,IAAI,CAAC,SACV,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,QAAQ,CAAG,EAClB,EAEA,EAAO,OAAO,CAAG,gCCpDjB,SAAS,EAAa,CAAO,EAE5B,MADa,CAAE,AACR,EADkB,GAAK,CAAC,IAAI,AAAC,EAAU,IAAM,CAErD,CAEA,EAJyD,EAIrD,EAJyD,AAItC,CACtB,AAL6D,MAKtD,EAAa,KACpB,MAAO,EAAa,KACpB,MAAO,EAAa,IACrB,EAWA,EAAO,OAAO,CATd,EASiB,OATR,AAAoB,CAAG,EAC/B,IAAI,EAAa,CAAgB,CAAC,EAAI,CACtC,GAAI,EACH,OAAO,CAGR,EAJgB,KAIV,AAAI,MAAM,sBAAwB,EAAM,IAC/C,gCClBA,IAAI,EAAS,EAAA,CAAA,CAAA,OAAuB,MAAM,CAEtC,EAAA,EAAA,CAAA,CAAA,OAiBJ,SAAS,EAAkB,CAAS,EACnC,GAAI,EAAO,QAAQ,CAAC,GACnB,OAAO,EADwB,AAEzB,GAAI,UAAa,OAAO,EAC9B,OAAO,EAAO,AAD2B,IACvB,CAAC,EAAW,SAG/B,OAAM,AAAI,UAAU,sDACrB,CAuFA,SAAS,EAAa,CAAG,CAAE,CAAK,CAAE,CAAI,EAErC,IADA,IAAI,EAAU,EACP,EAAQ,EAAU,GAAiC,AAAzB,EAA4B,GAAzB,CAAC,EAAQ,EAAQ,EACpD,EAAE,EAQH,OALgB,AACZ,CADe,CAAC,EAAQ,EAAQ,IAAI,CACzB,EACd,EAAE,EAGI,CACR,CAuDA,EAAO,OAAO,CAAG,CAChB,UAzJD,CAyJY,QAzJH,AAAU,CAAS,CAAE,CAAG,EAChC,EAAY,EAAkB,GAC9B,IAAI,EAAa,EAAoB,GAIjC,EAAwB,EAAa,EAErC,EAAc,EAAU,MAAM,CAE9B,EAAS,EACb,GA/BmB,AA+Bf,MAAS,CAAC,GA/Be,CA+BN,CACtB,IAD2B,EACrB,AAAI,MAAM,EAhC8B,OA+BF,YA/BqB,GAClE,SAkCA,IAAI,EAAY,CAAS,CAAC,IAAS,CAKnC,GAJmB,MAAf,IACH,EAAY,AADkB,CACT,AADU,CACT,EADY,EACZ,AAAS,EADd,AAId,EAAc,EAAS,EAC1B,MAAM,AAAI,GAD2B,GACrB,8BAAgC,EAAY,aAAe,CAAD,CAAe,CAAA,CAAM,CAAI,eAGpG,GA3CkB,IA2Cd,CAAS,CAAC,IA3Ce,AA2CN,CACtB,IAD2B,EACrB,AAAI,MAAM,MA5C+B,GA2CH,gCAI7C,IAAI,EAAU,CAAS,CAAC,IAAS,CAEjC,GAAI,EAAc,EAAS,EAAI,EAC9B,MAAM,AAAI,CAD6B,KACvB,4BAA8B,EAAU,aAAe,CAAD,CAAe,GAAS,CAAC,CAAI,eAGpG,GAAI,EAAwB,EAC3B,MAAM,AAAI,CAD0B,KACpB,4BAA8B,EAAU,cAAgB,EAAwB,mBAGjG,IAAI,EAAU,EAGd,GAFA,GAAU,MAEN,CAAS,CAAC,IAAS,CACtB,IAD2B,EACrB,AAAI,MAAM,SAD4B,gCAI7C,IAAI,EAAU,CAAS,CAAC,IAAS,CAEjC,GAAI,EAAc,IAAW,EAC5B,MAAM,AAAI,CAD2B,KACrB,4BAA8B,EAAU,iBAAmB,CAAD,CAAe,CAAA,CAAM,CAAI,KAGpG,GAAI,EAAwB,EAC3B,MAAM,AAAI,CAD0B,KACpB,4BAA8B,EAAU,cAAgB,EAAwB,mBAGjG,IAAI,EAAU,EAGd,GAAI,CAFJ,GAAU,CAAA,IAEK,EACd,MAAM,AAAI,KADiB,CACX,4CAA8C,CAAD,CAAe,CAAA,CAAM,CAAI,kBAGvF,IAAI,EAAW,EAAa,EAC3B,EAAW,EAAa,EAErB,EAAM,EAAO,WAAW,CAAC,EAAW,EAAU,EAAW,GAE7D,IAAK,EAAS,EAAG,EAAS,EAAU,EAAE,EACrC,CAAG,CAAC,EAAO,CADkC,AAC/B,EAEf,EAAU,IAAI,CAAC,EAAK,EAAQ,EAAU,KAAK,GAAG,CAAC,CAAC,EAAU,GAAI,EAAU,GAExE,EAAS,EAET,IAAK,IAAI,EAAI,EAAQ,EAAS,EAAI,EAAU,EAAE,EAC7C,CAAG,CAAC,EAAO,CAD0C,AACvC,EAOf,OAAO,AALP,EAAU,IAAI,CAAC,EAAK,EAAQ,EAAU,KAAK,GAAG,CAAC,CAAC,EAAU,GAAI,EAAU,GAGxE,EAhGO,CA+FP,EAAM,CACA,CADI,QAAQ,CAAC,AACH,SADG,EA9FjB,OAAO,CAAC,KAAM,IACd,OAAO,CAAC,MAAO,KACf,OAAO,CAAC,MAAO,IAgGlB,EAuEC,UAvDD,CAuDY,QAvDH,AAAU,CAAS,CAAE,CAAG,EAChC,EAAY,EAAkB,GAC9B,IAAI,EAAa,EAAoB,GAEjC,EAAiB,EAAU,MAAM,CACrC,GAAI,IAAgC,EAAb,CAAgB,CACtC,MAAU,AAAJ,UAAc,IAAM,EAAM,yBAAwC,EAAb,EAAiB,iBAAmB,EAAiB,KAGjH,IAAI,EAAW,EAAa,EAAW,EAAG,GACtC,EAAW,EAAa,EAAW,EAAY,EAAU,MAAM,EAC/D,EAAU,EAAa,EACvB,EAAU,EAAa,EAEvB,EAAU,EAAQ,EAAJ,AAAc,EAAI,EAAI,EAEpC,EAAc,EA5IH,IA8IX,EAAM,AA7IV,EA2I4B,AAEX,WAAW,CAAC,CAAC,EAAc,CA7I1B,CA6I8B,CAAC,CA5IjD,CA4IqD,GAEjD,EAAS,EA8Bb,OA7BA,CAAG,AA/Ia,CA+IZ,IAAS,CA9Ib,EA8IgB,CACZ,EAGH,CAAG,CAAC,GAlJK,CAkJI,CAAG,GAIhB,CAPgB,AAOb,AArJJ,CAqJK,IAAS,CAAG,IAEhB,AAvJS,CAuJN,CAAC,IAtJL,AAsJc,CAAa,CAFE,GAEZ,GAEjB,CAAG,CAAC,IAAS,GAAG,AAChB,CAAG,CAAC,IAAS,CAAG,EACZ,EAAW,GACd,AADiB,CACd,CAAC,IAAS,CAAG,EAChB,GAAU,EAAU,IAAI,CAAC,EAAK,EAAQ,EAAG,IAEzC,GAAU,EAAU,IAAI,CAAC,EAAK,EAAQ,EAAU,GAEjD,CAAG,CAAC,IAAS,GAAG,AAChB,CAAG,CAAC,IAAS,CAAG,EACZ,EAAW,GAAG,AACjB,CAAG,CAAC,IAAS,CAAG,EAChB,EAAU,IAAI,CAAC,EAAK,EAAQ,IAE5B,EAAU,IAAI,CAAC,EAAK,EAAQ,EAAa,GAGnC,CACR,CAKA,gCCxLA,IAAI,EAAS,EAAA,CAAA,CAAA,KAAkB,MAAM,CACjC,CADmC,CACtB,EAAA,CAAA,CAAA,KAAkB,GADiB,OACP,CAI7C,SAAS,EAAS,CAAC,CAAE,CAAC,EAGpB,GAAI,CAAC,EAAO,QAAQ,CAAC,IAAM,CAAC,EAAO,QAAQ,CAAC,IAOxC,AAP4C,EAO1C,MAAM,GAAK,EAAE,MAAM,CANvB,CAMyB,MANlB,EAWT,IAAK,IADD,EAAI,EACC,EAAI,EAAG,EAAI,EAAE,MAAM,CAAE,IAAK,AAEjC,GAAK,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAElB,CAFoB,MAAM,AAEb,IAAN,CACT,CAtBA,EAAO,OAAO,CAAG,EAwBjB,EAAS,OAAO,CAAG,WACjB,EAAO,SAAS,CAAC,KAAK,CAAG,EAAW,SAAS,CAAC,KAAK,CAAG,SAAe,AAAN,CAAU,EACvE,OAAO,EAAS,IAAI,CAAE,EACxB,CACF,EAEA,IAAI,EAAe,EAAO,SAAS,CAAC,KAAK,CACrC,EAAmB,EAAW,SAAS,CAAC,KAAK,AACjD,GAAS,OAAO,CAAG,WACjB,EAAO,SAAS,CAAC,KAAK,CAAG,EACzB,EAAW,SAAS,CAAC,KAAK,CAAG,CAC/B,mBCxCA,IAwII,EAxIA,EAAS,EAAA,CAAA,CAAA,OAAuB,MAAM,CACtC,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGA,EAAqB,oCACrB,EAA2B,mCAG3B,EAAuD,YAAlC,OAAO,EAAO,eAAe,CAMtD,SAAS,EAAiB,CAAG,EAC3B,IAAI,EAAO,QAAQ,CAAC,IAIhB,AAAe,EAJO,QAIG,OAAlB,IAIP,CAAC,GAIc,UAAf,AAAyB,OAJJ,AAId,GAIP,AAAoB,UAAU,OAAvB,EAAI,IAAI,EAIkB,UAAjC,AAA2C,OAApC,EAAI,iBAAiB,EAIN,YAAY,AAAlC,OAAO,EAAI,MAAM,EAfnB,MAAM,EAAU,EAkBpB,CAEA,SAAS,EAAkB,CAAG,EAC5B,IAAI,EAAO,QAAQ,CAAC,IAID,EAJO,QAIG,AAAzB,OAAO,GAIQ,UAAU,AAAzB,OAAO,EAIX,MAAM,EAnDqB,QAmDX,sCAClB,CA4BA,SAAS,EAAW,CAAM,EACxB,OAAO,EACJ,OAAO,CAAC,KAAM,IACd,OAAO,CAAC,MAAO,KACf,OAAO,CAAC,MAAO,IACpB,CAEA,SAAS,EAAS,CAAS,EAGzB,IAAI,EAAU,EAAI,CAFlB,EAAY,EAAU,QAAQ,EAAA,EAEF,MAAM,CAAG,EACrC,GAAgB,GAAG,CAAf,EACF,IAAK,IAAI,EAAI,EAAG,EAAI,EAAS,EAAE,EAAG,AAChC,GAAa,IAIjB,OAAO,EACJ,OAAO,CAAC,MAAO,KACf,OAAO,CAAC,KAAM,IACnB,CAEA,SAAS,EAAU,CAAQ,EACzB,IAAI,EAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,UAAW,GAEpC,OAAO,AAAI,UAAU,AADR,EAAK,MAAM,CAAC,IAAI,CAAC,EAAM,GAAU,KAAK,CAAC,KAAM,GAE5D,CAMA,SAAS,EAAe,CAAK,QAG3B,OAPsB,EAKF,CALK,CAClB,EAAO,QAAQ,CAAC,IAAuB,UAAf,OAAO,IAKpC,EAAQ,KAAK,SAAS,CAAC,EAAA,EAClB,CACT,CAEA,SAAS,EAAiB,CAAI,EAC5B,OAAO,SAAS,AAAK,CAAK,CAAE,CAAM,GAChC,AAlEJ,SAAS,AAAiB,CAAG,EAC3B,IAAI,EAAO,QAAQ,CAAC,IAIpB,EAJ0B,CAIP,UAAf,AAAyB,OAAlB,GAIX,GAAI,CAAC,GAID,AAAe,UAAU,OAJJ,AAId,GAIM,UAAU,CAAvB,EAAI,IAAI,EAIc,YAAtB,AAAkC,OAA3B,EAAI,MAAM,CAXnB,MAAM,EAAU,EAClB,EAaF,EA0CqB,GACjB,EAAQ,EAAe,GACvB,IAAI,EAAO,EAAO,UAAU,CAAC,MAAQ,EAAM,GAE3C,OAAO,GADI,EAAK,MAAM,AACJ,CADK,GAAQ,EAAK,MAAM,CAAC,SAAA,CAAS,CAEtD,CACF,CA3HI,IACF,GAA4B,aADN,KAEtB,GAAsB,kBA4HxB,IAAI,EAAkB,oBAAqB,EAAS,SAAS,AAAgB,CAAC,CAAE,CAAC,SAC/E,AAAI,EAAE,UAAU,GAAK,EAAE,UAAU,EAAE,AAI5B,EAAO,eAAe,CAAC,EAAG,EACnC,EAAI,SAAS,AAAgB,CAAC,CAAE,CAAC,EAK/B,OAJK,AAAD,IACF,EAAA,EAAA,CAAA,CAAA,GADgB,GAChB,EAGK,EAAY,EAAG,EACxB,EAEA,SAAS,EAAmB,CAAI,EAC9B,OAAO,SAAS,AAAO,CAAK,CAAE,CAAS,CAAE,CAAM,EAC7C,IAAI,EAAc,EAAiB,GAAM,EAAO,GAChD,OAAO,EAAgB,EAAO,IAAI,CAAC,GAAY,EAAO,IAAI,CAAC,GAC7D,CACF,CAEA,SAAS,EAAgB,CAAI,EAC5B,OAAO,SAAS,AAAK,CAAK,CAAE,CAAU,EACnC,EAAkB,GAClB,EAAQ,EAAe,GAGvB,IAAI,EAAS,EAAO,UAAU,CAAC,UAAY,GAE3C,OAAO,GADI,EAAO,MAAM,AACN,CADO,GAAQ,EAAO,IAAI,CAAC,EAAY,SAAA,CAAS,CAEpE,CACF,CAEA,SAAS,EAAkB,CAAI,EAC7B,OAAO,SAAS,AAAO,CAAK,CAAE,CAAS,CAAE,CAAS,EAChD,EAAiB,GACjB,EAAQ,EAAe,GACvB,EAAY,EAAS,GACrB,IAAI,EAAW,EAAO,YAAY,CAAC,UAAY,GAE/C,OADA,EAAS,MAAM,CAAC,GACT,EAAS,MAAM,CAAC,EAAW,EAAW,SAC/C,CACF,CAEA,SAAS,EAAmB,CAAI,EAC9B,OAAO,SAAS,AAAK,CAAK,CAAE,CAAU,EACpC,EAAkB,GAClB,EAAQ,EAAe,GACvB,IAAI,EAAS,EAAO,UAAU,CAAC,UAAY,GAM3C,OAAO,GALI,EAAO,MAAM,AAKN,CALO,GAAQ,EAAO,IAAI,CAAC,CAC3C,IAAK,EACL,QAAS,EAAO,SAAS,CAAC,qBAAqB,CAC/C,WAAY,EAAO,SAAS,CAAC,sBAAsB,AACrD,EAAG,SAAA,CAAS,CAEd,CACF,CAEA,SAAS,EAAqB,CAAI,EAChC,OAAO,SAAS,AAAO,CAAK,CAAE,CAAS,CAAE,CAAS,EAChD,EAAiB,GACjB,EAAQ,EAAe,GACvB,EAAY,EAAS,GACrB,IAAI,EAAW,EAAO,YAAY,CAAC,UAAY,GAE/C,OADA,EAAS,MAAM,CAAC,GACT,EAAS,MAAM,CAAC,CACrB,IAAK,EACL,QAAS,EAAO,SAAS,CAAC,qBAAqB,CAC/C,WAAY,EAAO,SAAS,CAAC,sBAC/B,AADqD,EAClD,EAAW,SAChB,CACF,CAEA,SAAS,EAAkB,CAAI,EAC7B,IAAI,EAAQ,EAAgB,GAC5B,OAAO,SAAS,EACd,IAAI,EAAY,EAAM,KAAK,CAAC,KAAM,WAElC,OADY,AACL,EADiB,SAAS,CAAC,EAAW,KAAO,EAEtD,CACF,CAEA,SAAS,EAAmB,CAAI,EAC9B,IAAI,EAAQ,EAAkB,GAC9B,OAAO,SAAS,AAAO,CAAK,CAAE,CAAS,CAAE,CAAS,EAGhD,OADa,AACN,EADY,EADnB,EAAY,EAAY,CACE,QADO,CAAC,EAAW,KAAO,GAAM,QAAQ,CAAC,UAC9B,EAEvC,CACF,CAEA,SAAS,IACP,OAAO,SAAS,EACd,MAAO,EACT,CACF,CAEA,SAAS,IACP,OAAO,SAAgB,AAAP,CAAY,CAAE,CAAS,EACrC,MAAqB,AAAd,MACT,CACF,CAEA,EAAO,OAAO,CAAG,SAAa,AAAJ,CAAa,EAerC,IAAI,EAAQ,EAAU,KAAK,CAAC,0CAC5B,GAAI,CAAC,EACH,MAAM,EA5PkB,QA4PR,gLAAuB,GACzC,IAAI,EAAO,CAAC,CAAK,CAAC,EAAE,EAAI,CAAK,CAAC,EAAA,AAAE,EAAE,WAAW,GACzC,EAAO,CAAK,CAAC,EAAE,CAEnB,MAAO,CACL,KArBoB,AAqBd,EApBN,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,KAAM,EACR,CAeuB,CAAC,EAAK,CAAC,GAC5B,OAAQ,CAfc,CACtB,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,KAAM,CACR,EAS2B,CAAC,EAAK,CAAC,EAClC,CACF,mBCxQA,IAAI,EAAS,EAAA,CAAA,CAAA,KAAkB,MAAM,AAErC,GAAO,OAAO,CAAG,SAAS,AAAS,CAAG,QACpC,AAAmB,UAAf,AACF,OADS,EACF,EACU,UAAf,OAAO,GAAoB,EAAO,QAAQ,CAAC,GACtC,EAAI,CAAX,OAAmB,GACd,KAAK,SAAS,CAAC,EACxB,mBCRA,IAAI,EAAS,EAAA,CAAA,CAAA,OAAuB,MAAM,CACtC,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEJ,SAAS,EAAU,CAAM,CAAE,CAAQ,EACjC,OAAO,EACJ,IAAI,CAAC,EAAQ,GACb,QAAQ,CAAC,UACT,OAAO,CAAC,KAAM,IACd,OAAO,CAAC,MAAO,KACf,OAAO,CAAC,MAAO,IACpB,CASA,SAAS,EAAQ,CAAI,EACnB,QALI,EAKA,EAAS,EAAK,MAAM,CACpB,EAAU,EAAK,OAAO,CACtB,EAAc,EAAK,MAAM,EAAI,EAAK,UAAU,CAC5C,EAAW,EAAK,QAAQ,CACxB,EAAO,EAAI,EAAO,GAAG,EACrB,GAZJ,EAAW,CAD6B,EAaY,IAZ7B,EADyB,CAa7B,IAXf,EAAgB,EAAU,EAWK,GAXa,IAAT,QAClB,EAAU,EAUY,GAVO,GAC3C,CADiC,CAC5B,MAAM,CAAC,QAAS,EAAe,IAUvC,EAAY,EAAK,IAAI,CAAC,EAAc,GACxC,OAAO,EAAK,MAAM,CAAC,QAAS,EAAc,EAC5C,CAEA,SAAS,EAAW,CAAI,EAEtB,IAAI,EAAe,IAAI,EADV,EAAK,MAAM,CACU,CADR,EAAK,UAAU,EAAE,EAAK,GAAG,EAEnD,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,MAAM,CAAG,EAAK,MAAM,CACzB,IAAI,CAAC,QAAQ,CAAG,EAAK,QAAQ,CAC7B,IAAI,CAAC,MAAM,CAAG,IAAI,CAAC,UAAU,CAAG,IAAI,CAAC,GAAG,CAAG,EAC3C,IAAI,CAAC,OAAO,CAAG,IAAI,EAAW,EAAK,OAAO,EAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAS,CAAA,WACpB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,EACzC,IAAI,CAAC,IAAI,GACb,CAAA,CAAE,IAAI,CAAC,IAAI,GAEX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAS,CAAA,WACrB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,EACxC,IAAI,CAAC,IAAI,GACb,CAAA,CAAE,IAAI,CAAC,IAAI,EACb,CACA,EAAK,QAAQ,CAAC,EAAY,GAE1B,EAAW,SAAS,CAAC,IAAI,CAAG,SAAS,EACnC,GAAI,CACF,IAAI,EAAY,EAAQ,CACtB,OAAQ,IAAI,CAAC,MAAM,CACnB,QAAS,IAAI,CAAC,OAAO,CAAC,MAAM,CAC5B,OAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAC1B,SAAU,IAAI,CAAC,QAAQ,AACzB,GAKA,OAJA,IAAI,CAAC,IAAI,CAAC,OAAQ,GAClB,IAAI,CAAC,IAAI,CAAC,OAAQ,GAClB,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,EAAG,EACT,CACT,CAAE,MAAO,EAAG,CACV,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,IAAI,CAAC,QAAS,GACnB,IAAI,CAAC,IAAI,CAAC,QACZ,CACF,EAEA,EAAW,IAAI,CAAG,EAElB,EAAO,OAAO,CAAG,mBC5EjB,IAAI,EAAS,EAAA,CAAA,CAAA,OAAuB,MAAM,CACtC,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAY,2DAahB,SAAS,EAAc,CAAM,EAC3B,IAAI,EAAgB,EAAO,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CARtB,EASA,EAAO,CATF,GASM,CAAC,EAAe,UAAU,QAAQ,CAAC,UARnE,GAJiD,CAI7C,mBAJG,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,AAIzB,GACX,OAAO,EACT,GAAI,CAAE,OAAO,KAAK,KAAK,CAAC,EAAQ,CAChC,MAAO,EAAG,CAAE,MAAkB,CAMhC,AANqB,CAYrB,SAAS,EAAiB,CAAM,EAC9B,OAAO,EAAO,KAAK,CAAC,IAAI,CAAC,EAAE,AAC7B,CAQA,SAAS,EAAW,CAAM,EACxB,OAAO,EAAU,IAAI,CAAC,IAAW,CAAC,CAAC,EAAc,EACnD,CAEA,SAAS,EAAU,CAAM,CAAE,CAAS,CAAE,CAAW,EAC/C,GAAI,CAAC,EAAW,CACd,IAAI,EAAM,AAAI,MAAM,6CAEpB,OADA,EAAI,IAAI,CAAG,oBACL,CACR,CAEA,IAAI,EAAY,EADhB,EAAS,EAAS,IAEd,EAAmC,AAzBhC,EAAO,GAwBmB,EAxBd,CAAC,IAAK,CAyBN,EAzBS,IAAI,CAAC,KA2BjC,OADW,AACJ,EADQ,GACH,MAAM,CAAC,EAAc,EAAW,EAC9C,CAEA,SAAS,EAAU,CAAM,CAAE,CAAI,EAI7B,GAHA,EAAO,GAAQ,CAAC,EAGZ,CAAC,EAFL,EAAS,EAAS,IAGhB,CADc,MACP,KAET,IA9B8B,IA8B1B,EAAS,EAAc,AA9BW,GAgCtC,GAAI,CAAC,EACH,OAAO,KAET,IAAI,GAlCJ,EAAW,GAAY,EAkCT,KAjCV,EAAU,AAiCe,EAjCR,KAAK,CAAC,IAAI,CAAC,EAAE,CAC3B,EAAO,IAAI,CAAC,EAAS,UAAU,QAAQ,CAAC,IAoC/C,OAHmB,QAAf,EAAO,GAAG,EAAc,EAAK,IAAA,AAAI,IACnC,EAAU,KAAK,KAAK,CAAC,EAAS,EAAK,SAAQ,EAEtC,CACL,OAAQ,EACR,QAAS,EACT,UAAW,EAAiB,EAC9B,CACF,CAEA,SAAS,EAAa,CAAI,EAGxB,IAAI,EAAe,IAAI,EADL,CADlB,EAAO,GAAQ,EAAC,CAEkB,CADX,MAAM,EAAE,EAAK,SAAS,EAAE,EAAK,GAAG,EAEvD,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,SAAS,CAAG,EAAK,SAAS,CAC/B,IAAI,CAAC,QAAQ,CAAG,EAAK,QAAQ,CAC7B,IAAI,CAAC,MAAM,CAAG,IAAI,CAAC,SAAS,CAAG,IAAI,CAAC,GAAG,CAAG,EAC1C,IAAI,CAAC,SAAS,CAAG,IAAI,EAAW,EAAK,SAAS,EAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAS,CAAA,WACpB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,EAC3C,IAAI,CAAC,MAAM,GACf,CAAA,CAAE,IAAI,CAAC,IAAI,GAEX,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAS,CAAA,WACvB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,EACxC,IAAI,CAAC,MAAM,GACf,CAAA,CAAE,IAAI,CAAC,IAAI,EACb,CACA,EAAK,QAAQ,CAAC,EAAc,GAC5B,EAAa,SAAS,CAAC,MAAM,CAAG,SAAS,EACvC,GAAI,CACF,IAAI,EAAQ,EAAU,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE,IAAI,CAAC,SAAS,CAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EACxE,EAAM,EAAU,IAAI,CAAC,SAAS,CAAC,MAAM,CAAE,IAAI,CAAC,QAAQ,EAKxD,OAJA,IAAI,CAAC,IAAI,CAAC,OAAQ,EAAO,GACzB,IAAI,CAAC,IAAI,CAAC,OAAQ,GAClB,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,EAAG,EACT,CACT,CAAE,MAAO,EAAG,CACV,IAAI,CAAC,QAAQ,EAAG,EAChB,IAAI,CAAC,IAAI,CAAC,QAAS,GACnB,IAAI,CAAC,IAAI,CAAC,QACZ,CACF,EAEA,EAAa,MAAM,CAAG,EACtB,EAAa,OAAO,CAAG,EACvB,EAAa,MAAM,CAAG,EAEtB,EAAO,OAAO,CAAG,mBCtHjB,IAAI,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MASJ,GAAQ,UAAU,CAPD,CACf,CAMmB,OANV,QAAS,QAClB,QAAS,QAAS,QAClB,QAAS,QAAS,QAClB,QAAS,QAAS,QACnB,CAGD,EAAQ,IAAI,CAAG,EAAW,IAAI,CAC9B,EAAQ,MAAM,CAAG,EAAa,MAAM,CACpC,EAAQ,MAAM,CAAG,EAAa,MAAM,CACpC,EAAQ,OAAO,CAAG,EAAa,OAAO,CACtC,EAAQ,UAAU,CAAG,SAAoB,AAAX,CAAe,EAC3C,OAAO,IAAI,EAAW,EACxB,EACA,EAAQ,YAAY,CAAG,SAAS,AAAa,CAAI,EAC/C,OAAO,IAAI,EAAa,EAC1B,mBCrBA,IAAI,EAAA,EAAA,CAAA,CAAA,OAEJ,EAAO,OAAO,CAAG,SAAU,CAAG,CAAE,CAAO,EACrC,EAAU,GAAW,CAAC,EACtB,IAAI,EAAU,EAAI,MAAM,CAAC,EAAK,GAC9B,GAAI,CAAC,EAAW,OAAF,AAAS,KACvB,IAAI,EAAU,EAAQ,OAAO,CAG7B,GAAsB,UAAU,AAA7B,OAAO,EACR,GAAI,CACF,IAAI,EAAM,KAAK,KAAK,CAAC,EACV,QAAR,GAA+B,UAAf,AAAyB,OAAlB,IACxB,EAAU,CAAA,CAEd,CAAE,MAAO,EAAG,CAAE,OAMhB,AAAI,CAAqB,MAAb,AAAmB,QAAX,CACX,CACL,OAAQ,EAAQ,MAAM,CACtB,QAAS,EACT,UAAW,EAAQ,SAAS,AAC9B,EAEK,CACT,mBC7BA,IAAI,EAAoB,SAAU,CAAO,CAAE,CAAK,EAC9C,MAAM,IAAI,CAAC,IAAI,CAAE,GACd,MAAM,iBAAiB,EAAE,AAC1B,MAAM,iBAAiB,CAAC,IAAI,CAAE,IAAI,CAAC,WAAW,EAEhD,IAAI,CAAC,IAAI,CAAG,oBACZ,IAAI,CAAC,OAAO,CAAG,EACX,IAAO,IAAI,CAAC,KAAK,CAAG,CAAA,CAC1B,EAEA,EAAkB,SAAS,CAAG,OAAO,MAAM,CAAC,MAAM,SAAS,EAC3D,EAAkB,SAAS,CAAC,WAAW,CAAG,EAE1C,EAAO,OAAO,CAAG,mBCbjB,IAAI,EAAA,EAAA,CAAA,CAAA,OAEA,EAAiB,SAAU,CAAO,CAAE,CAAI,EAC1C,EAAkB,IAAI,CAAC,IAAI,CAAE,GAC7B,IAAI,CAAC,IAAI,CAAG,iBACZ,IAAI,CAAC,IAAI,CAAG,CACd,EAEA,EAAe,SAAS,CAAG,OAAO,MAAM,CAAC,EAAkB,SAAS,EAEpE,EAAe,SAAS,CAAC,WAAW,CAAG,EAEvC,EAAO,OAAO,CAAG,mBCZjB,IAAI,EAAA,EAAA,CAAA,CAAA,OAEA,EAAoB,SAAU,CAAO,CAAE,CAAS,EAClD,EAAkB,IAAI,CAAC,IAAI,CAAE,GAC7B,IAAI,CAAC,IAAI,CAAG,oBACZ,IAAI,CAAC,SAAS,CAAG,CACnB,EAEA,EAAkB,SAAS,CAAG,OAAO,MAAM,CAAC,EAAkB,SAAS,EAEvE,EAAkB,SAAS,CAAC,WAAW,CAAG,EAE1C,EAAO,OAAO,CAAG,mBCkJjB,SAAS,EAAO,CAAE,CAAE,CAAK,CAAE,CAAC,CAAE,CAAI,EAEhC,OAAO,KAAK,KAAK,CAAC,EAAK,GAAK,IAAM,GADnB,AAC2B,GADlB,AAAI,CACa,KAAY,IAAM,EAAA,CAC7D,AAD+D,CAvI/D,EAAO,OAAO,CAAG,SAAU,CAAG,CAAE,CAAO,EACrC,EAAU,GAAW,CAAC,EACtB,IA8Ge,EAAE,IAxBb,EAtFA,EAAO,OAAO,EAClB,GAAa,WAAT,GAAqB,EAAI,MAAM,CAAG,EAC7B,CADgC,IAmB5B,EAlBE,CAkBC,CAEhB,IAAI,CADJ,GAAM,OAAO,EAAA,EACL,MAAM,CAAG,GAAA,GAAK,AAGtB,IAAI,EAAQ,mIAAmI,IAAI,CACjJ,GAEF,GAAK,CAAD,EAGJ,IAHY,AAGR,EAAI,WAAW,CAAK,CAAC,EAAE,EAE3B,OAAQ,AADG,CAAC,CAAK,CAAC,EAAE,EAAI,IAAA,CAAI,CAAE,WAAW,IAEvC,IAAK,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAzDE,IAAI,KAyDC,CACT,GADa,EACR,QACL,IAAK,OACL,IAAK,IACH,OA9DE,IAAI,GA8DC,CACT,GADa,EACR,OACL,IAAK,MACL,IAAK,IACH,aAAO,CACT,GADa,EACR,QACL,IAAK,OACL,IAAK,MACL,IAAK,KACL,IAAK,IACH,OAAO,AA1EL,IA0ES,AA1EL,EA2ER,KAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,OAjFE,AAiFK,IAAI,AAjFL,CAkFR,KAAK,UACL,IAAK,SACL,IAAK,OACL,IAAK,MACL,IAAK,IACH,WAAO,CACT,GADa,EACR,eACL,IAAK,cACL,IAAK,QACL,IAAK,OACL,IAAK,KACH,OAAO,CACT,SACE,KACJ,EADW,OAvEI,CACR,GAAa,WAAT,GAAqB,SAAS,GACvC,GAD6C,KACtC,EAAQ,IAAI,CA4GrB,AAAI,CADA,CA3GoB,CA2GZ,KAAK,CACJ,EADO,CACJ,AADK,EA3GW,OAAO,IA6G9B,EAAO,EAAI,OAAO,CAAG,OAE1B,QACK,CADI,CACG,EADA,AACI,OAAO,AAAG,QAE1B,OACK,EADI,AACG,EAAI,CADJ,KACc,CAAH,SAEvB,OACK,EADI,AACG,EAAI,CADJ,KACc,CAAH,SAEpB,EAAK,MArCZ,AAAI,GADQ,KAAK,CACJ,EADO,CAAC,AADL,AAEA,EAFE,AAjF8B,WAoFvC,KAAK,KAAK,CAAC,EA5Gd,GA4GmB,CA5Gf,GA4GoB,IAE1B,QACK,CADI,GAAG,CACF,KAAK,CAAC,KAAK,GAAK,IAE1B,OACK,EADI,GAAG,AACF,KAAK,CAAC,KAAK,EAAK,IAE1B,OACK,EADI,GACC,AADE,KACG,CAAC,EAxHd,GAwHmB,EAAK,IAEvB,EAAK,IA/FoC,CAEhD,MAAM,AAAI,MACR,wDACE,KAAK,SAAS,CAAC,GAErB,mBCrCA,IAAI,EAAA,EAAA,CAAA,CAAA,OAEJ,EAAO,OAAO,CAAG,SAAU,CAAI,CAAE,CAAG,EAClC,IAAI,EAAY,GAAO,KAAK,KAAK,CAAC,KAAK,GAAG,GAAK,KAE/C,GAAoB,UAAhB,OAAO,EAAmB,CAC5B,IAAI,EAAe,EAAG,GACtB,GAAI,KAAwB,IAAjB,EACT,OADuC,AAGzC,OAAO,KAAK,KAAK,CAAC,EAAY,EAAe,IAC/C,CAAO,GAAoB,UAAU,AAA1B,OAAO,EAChB,OAAO,EAAY,CAKvB,+BCUA,GAAO,OAAO,CAAG,CACf,WAtBiB,IAuBjB,0BAlBgC,GAmBhC,sBAf4B,IAgB5B,SAhByC,QARlB,OAAO,gBAAgB,EACrB,EAA3B,eAwBE,SAxBsB,KASF,CACpB,QACA,WACA,QACA,WACA,QACA,WACA,aACD,CAQC,oBA7B0B,QA8B1B,wBAAyB,EACzB,WAAY,CACd,gCC1BA,EAAO,OAAO,CAPO,EAOJ,QAPf,OAAO,SACP,QAAQ,GAAG,EACX,QAAQ,GAAG,CAAC,UAAU,EACtB,cAAc,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EACvC,CAAC,GAAG,IAAS,QAAQ,KAAK,CAAC,YAAa,GACxC,KAAO,+BCNX,GAAM,2BACJ,CAAyB,uBACzB,CAAqB,YACrB,CAAU,CACX,CAAA,EAAA,CAAA,CAAA,OACK,EAAA,EAAA,CAAA,CAAA,OAIA,EAAK,CAHX,EAAU,EAAO,OAAO,CAAG,EAAC,EAGT,EAAE,CAAG,EAAE,CACpB,EAAS,EAAQ,MAAM,CAAG,EAAE,CAC5B,EAAM,EAAQ,GAAG,CAAG,EAAE,CACtB,EAAU,EAAQ,OAAO,CAAG,EAAE,CAC9B,EAAI,EAAQ,CAAC,CAAG,CAAC,EACnB,EAAI,EAEF,EAAmB,eAQnB,EAAwB,CAC5B,CAAC,MAAO,EAAE,CACV,CAAC,MAAO,EAAW,CACnB,CAAC,EAAkB,EAAsB,CAC1C,CAWK,EAAc,CAAC,EAAM,EAAO,KAChC,IAAM,EAAO,CAVQ,AAAD,IACpB,IAAK,GAAM,CAAC,EAAO,EAAI,GAAI,EACzB,EAAQ,EACL,KAAK,CAAC,CAAA,EAAG,EAAM,CAAC,CAAC,EAAE,CAF0B,GAEtB,CAAC,CAAA,EAAG,EAAM,GAAG,EAAE,EAAI,CAAC,CAAC,EAC5C,KAAK,CAAC,CAAA,EAAG,EAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA,EAAG,EAAM,GAAG,EAAE,EAAI,CAAC,CAAC,EAEjD,OAAO,EACT,EAG6B,GACrB,EAAQ,IACd,EAAM,EAAM,EAAO,GACnB,CAAC,CAAC,EAAK,CAAG,EACV,CAAG,CAAC,EAAM,CAAG,EACb,CAAO,CAAC,EAAM,CAAG,EACjB,CAAE,CAAC,EAAM,CAAG,IAAI,OAAO,EAAO,EAAW,SAAM,GAC/C,CAAM,CAAC,EAAM,CAAG,IAAI,OAAO,EAAM,EAAW,SAAM,EACpD,EAQA,EAAY,oBAAqB,eACjC,EAAY,yBAA0B,QAMtC,EAAY,uBAAwB,CAAC,aAAa,EAAE,EAAiB,CAAC,CAAC,EAKvE,EAAY,cAAe,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,AACpC,IADwC,CACvC,EAAE,CAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,AAC5B,IADgC,CAAC,AAChC,EAAE,CAAG,AAAP,CAAQ,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,AAFW,EAI7D,CAHmB,CAGP,mBAAoB,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,AACzC,IAD6C,CAC5C,EAAE,CAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,AACjC,IADqC,CAAC,AACrC,EAAE,CAAJ,AAAO,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAFY,AAEX,EAO5D,CARwB,CAQZ,uBAAwB,CAAC,GAAG,EAAE,CAAG,CAAC,EAAE,oBAAoB,CAAC,CACpE,CAAC,EAAE,CAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAE/B,EAAY,4BAA6B,CAAC,GAAG,EAAE,CAAG,CAAC,EAAE,oBAAoB,CAAC,CACzE,CAAC,EAAE,CAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAMpC,EAAY,aAAc,CAAC,KAAK,EAAE,CAAG,CAAC,EAAE,oBAAoB,CAAC,CAC5D,MAAM,EAAE,CAAG,CAAC,EAAE,oBAAoB,CAAC,CAAC,IAAI,CAAC,EAE1C,EAAY,kBAAmB,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,yBAAyB,CAAC,CACvE,MAAM,EAAE,CAAG,CAAC,EAAE,yBAAyB,CAAC,CAAC,IAAI,CAAC,EAK/C,EAAY,kBAAmB,CAAA,EAAG,EAAiB,CAAC,CAAC,EAMrD,EAAY,QAAS,CAAC,OAAO,EAAE,CAAG,CAAC,EAAE,eAAe,CAAC,CACpD,MAAM,EAAE,CAAG,CAAC,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,EAWrC,EAAY,YAAa,CAAC,EAAE,EAAE,CAAG,CAAC,EAAE,WAAW,CAAC,CAAA,EAC7C,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EACpB,CAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAEjB,EAAY,OAAQ,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAK3C,EAAY,aAAc,CAAC,QAAQ,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAA,EACzD,CAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,EACzB,CAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAEjB,EAAY,QAAS,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,EAE7C,EAAY,OAAQ,gBAKpB,EAAY,wBAAyB,CAAA,EAAG,CAAG,CAAC,EAAE,sBAAsB,CAAC,CAAC,QAAQ,CAAC,EAC/E,EAAY,mBAAoB,CAAA,EAAG,CAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,EAErE,EAAY,cAAe,CAAC,SAAS,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,AAC3C,CAD4C,OACrC,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAChC,AADiC,CAAC,CAAC,GACpC,GAAQ,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,AACjC,CADkC,CAAC,EAChC,CAAJ,CAAM,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,EACxB,CAAG,CAAC,EAAE,KAAK,CAAC,CAAC,KAAC,CAAC,AAJ6B,EAOjE,CAFmB,AAJA,CAIC,AAER,IAFY,CAAC,cAEO,CAAC,SAAS,EAAE,CAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,AAChD,CADiD,OAC1C,EAAE,CAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,AACtC,CADuC,CAAC,GACzC,GAAQ,EAAE,CAAG,CAAC,EAAE,qBAAqB,CAAC,CAAC,AACtC,CADuC,CAAC,EACrC,CAAJ,CAAM,CAAG,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,EAC7B,CAAG,CAAC,EAAE,KAAK,CAAC,CAAC,KAAC,CAAC,AAJkC,EAO3E,CAFwB,AAJA,CAIC,AAEb,IAFiB,CAAC,IAER,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EACjE,EAAY,cAAe,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAI3E,EAAY,cAAe,GAAG,eAChB,EAAY,EACX,eAAa,EAAE,EACf,KAFqC,EAAE,UAE1B,EAAE,EAA0B,GADA,CACI,CAAC,AAFN,EAGvD,AAF4D,CAA9C,AAA+C,CAEjD,EADE,OACQ,CAAA,EAAG,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,YAAY,CAAC,EACzD,EAAY,aAAc,CAAG,CAAC,EAAE,WAAW,CAAC,CAC9B,CAAC,GAAG,EAAE,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAC3B,CAAC,GAAG,EAAE,CAAG,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CACtB,CAAC,YAAY,CAAC,EAC5B,EAAY,YAAa,CAAG,CAAC,EAAE,MAAM,CAAC,EAAE,GACxC,EAAY,gBAAiB,CAAG,CAAC,EAAE,UAAU,CAAC,EAAE,GAIhD,EAAY,YAAa,WAEzB,EAAY,YAAa,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAC1D,EAAQ,gBAAgB,CAAG,MAE3B,EAAY,QAAS,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAA,EAAG,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EACjE,EAAY,aAAc,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAA,EAAG,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAI3E,EAAY,YAAa,WAEzB,EAAY,YAAa,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,GAC1D,EAAQ,gBAAgB,CAAG,MAE3B,EAAY,QAAS,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAA,EAAG,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EACjE,EAAY,aAAc,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAA,EAAG,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAG3E,EAAY,kBAAmB,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,EAC9E,EAAY,aAAc,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAG,CAAC,EAAE,SAAS,CAAC,CAAC,KAAK,CAAC,EAIxE,EAAY,iBAAkB,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,IAAI,CAAC,CACjD,KAAK,EAAE,CAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,GACpD,EAAQ,qBAAqB,CAAG,SAMhC,EAAY,cAAe,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,AAEnC,CAFoC,UAEnC,EAAE,CAAG,CAAC,EAAE,WAAW,CAAC,CAAC,MAAC,CAFc,AAEb,EAG5C,CAJmB,AAEA,CAEP,AAJQ,AAEA,KAAK,CAAC,GAFG,CAAC,GACX,MAGa,CAAC,MAAM,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,AAExC,CAFyC,UAExC,EAAE,CAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC,MAAC,CAAC,AAFa,EAMnE,CAHwB,AAFA,CAAC,AAEA,AAGb,KAHkB,CAAC,CAGX,EALc,CAAC,GACX,aAMxB,EAAY,OAAQ,6BACpB,EAAY,UAAW,6DC3NvB,IAAM,EAAc,OAAO,MAAM,CAAC,CAAE,OAAO,CAAK,GAC1C,EAAY,OAAO,MAAM,CAAC,CAAE,GAYlC,EAAO,OAAO,CAXO,EAWJ,CAVV,AAAL,EAII,AAAmB,EAJnB,KAAU,GAImB,OAAtB,EACF,EAGF,EAPE,gCCLX,IAAM,EAAU,WACV,EAAqB,CAAC,EAAG,KAC7B,IAAM,EAAO,EAAQ,IAAI,CAAC,GACpB,EAAO,EAAQ,IAAI,CAAC,GAO1B,OALI,GAAQ,IACV,EADgB,EACZ,CAAC,AACL,IAAI,CAAC,CAGA,IAAM,EAAI,EACZ,GAAQ,CAAC,EAAQ,CAAC,EAClB,GAAQ,CAAC,EAAQ,EAClB,EAAI,EAAI,CAAC,EACT,CACN,CAIA,GAAO,OAAO,CAAG,oBACf,EACA,oBAJ0B,CAAC,EAAG,IAAM,EAAmB,EAAG,EAK5D,gCCtBA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,YAAE,CAAU,kBAAE,CAAgB,CAAE,CAAA,EAAA,CAAA,CAAA,OAChC,CAAE,OAAQ,CAAE,GAAE,CAAC,CAAE,CAAA,EAAA,CAAA,CAAA,MAEjB,EAAA,EAAA,CAAA,CAAA,OACA,oBAAE,CAAkB,CAAE,CAAA,EAAA,CAAA,CAAA,MAC5B,OAAM,EACJ,YAAa,CAAO,CAAE,CAAO,CAAE,CAG7B,GAFA,EAAU,EAAa,GAEnB,aAAmB,EACrB,GAAsB,CAAC,CAAC,CADK,CACG,KAAK,GAAjC,EAAQ,KAAK,EACe,CAAC,CAAC,EAAQ,iBAAiB,EAAE,CAA3D,EAAQ,iBAAiB,CACzB,OAAO,OAEP,EAAU,EAAQ,OAAO,MAEtB,GAAI,AAAmB,UAAU,OAAtB,EAChB,MAAM,AAAI,UAAU,CAAC,6CAA6C,EAAE,OAAO,EAAQ,EAAE,CAAC,EAGxF,GAAI,EAAQ,MAAM,CAAG,EACnB,MAAM,AAAI,IADqB,MAE7B,CAAC,uBAAuB,EAAE,EAAW,WAAW,CAAC,EAIrD,EAAM,SAAU,EAAS,GACzB,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,EAAQ,KAAK,CAG5B,IAAI,CAAC,iBAAiB,CAAG,CAAC,CAAC,EAAQ,iBAAiB,CAEpD,IAAM,EAAI,EAAQ,IAAI,GAAG,KAAK,CAAC,EAAQ,KAAK,CAAG,CAAE,CAAC,EAAE,KAAK,CAAC,CAAG,CAAE,CAAC,EAAE,IAAI,CAAC,EAEvE,GAAI,CAAC,EACH,CADM,KACA,AAAI,UAAU,CAAC,iBAAiB,EAAE,EAAA,CAAS,EAUnD,GAPA,IAAI,CAAC,GAAG,CAAG,EAGX,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,CAAC,EAAE,CAClB,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,CAAC,EAAE,CAClB,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,CAAC,EAAE,CAEd,IAAI,CAAC,KAAK,CAAG,GAAoB,IAAI,CAAC,KAAK,CAAG,EAChD,CADmD,KACzC,AAAJ,UAAc,yBAGtB,GAAI,IAAI,CAAC,KAAK,CAAG,GAAoB,IAAI,CAAC,KAAK,CAAG,EAChD,CADmD,KAC7C,AAAI,UAAU,yBAGtB,GAAI,IAAI,CAAC,KAAK,CAAG,GAAoB,IAAI,CAAC,KAAK,CAAG,EAChD,CADmD,KAC7C,AAAI,UAAU,yBAIjB,CAAC,CAAC,EAAE,CAGP,CAHS,GAGL,CAAC,UAAU,CAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,AAAC,IACrC,GAAI,WAAW,IAAI,CAAC,GAAK,CACvB,IAAM,EAAM,CAAC,EACb,GAAI,GAAO,GAAK,EAAM,EACpB,OAAO,CAEX,CACA,OAAO,AAJmC,CAK5C,GAVA,IAAI,CAAC,UAAU,CAAG,EAAE,CAatB,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAO,EAAE,CACxC,IAAI,CAAC,MAAM,EACb,CAEA,QAAU,CAKR,OAJA,IAAI,CAAC,OAAO,CAAG,CAAA,EAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAA,CAAE,CACtD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAC1B,IAAI,CAAC,OAAO,EAAI,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAA,CAAA,AAAM,EAE1C,IAAI,CAAC,OAAO,AACrB,CAEA,UAAY,CACV,OAAO,IAAI,CAAC,OAAO,AACrB,CAEA,QAAS,CAAK,CAAE,CAEd,GADA,EAAM,iBAAkB,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,OAAO,CAAE,GAChD,CAAC,CAAC,aAAiB,CAAA,CAAM,CAAG,CAC9B,GAAqB,UAAjB,OAAO,GAAsB,IAAU,IAAI,CAAC,OAAO,CACrD,CADuD,MAChD,EAET,EAAQ,IAAI,EAAO,EAAO,IAAI,CAAC,OAAO,CACxC,QAEI,AAAJ,EAAU,OAAO,GAAK,IAAI,CAAC,OAAO,CACzB,CAD2B,CAI7B,IAAI,CAAC,WAAW,CAAC,IAAU,IAAI,CAAC,UAAU,CAAC,EACpD,CAEA,YAAa,CAAK,CAAE,CAKlB,OAJI,AAAE,CAAD,YAAkB,IACrB,EAD2B,AACnB,GADsB,CAClB,EAAO,EAAO,IAAI,CAAC,QAAO,EAItC,EAAmB,IAAI,CAAC,KAAK,CAAE,EAAM,KAAK,GAC1C,EAAmB,IAAI,CAAC,KAAK,CAAE,EAAM,KAAK,GAC1C,EAAmB,IAAI,CAAC,KAAK,CAAE,EAAM,KAAK,CAE9C,CAEA,WAAY,CAAK,CAAE,CAMjB,GALI,AAAE,CAAD,YAAkB,IACrB,EAAQ,AADmB,GAAG,CAClB,EAAO,EAAO,IAAI,CAAC,QAAO,EAIpC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAI,CAAC,EAAM,UAAU,CAAC,MAAM,CACpD,CADsD,MAC/C,CAAC,EACH,GAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAI,EAAM,UAAU,CAAC,MAAM,CAC3D,CAD6D,MACtD,EACF,GAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAI,CAAC,EAAM,UAAU,CAAC,MAAM,CAC5D,CAD8D,MACvD,EAGT,IAAI,EAAI,EACR,EAAG,CACD,IAAM,EAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CACtB,EAAI,EAAM,UAAU,CAAC,EAAE,CAE7B,GADA,EAAM,qBAAsB,EAAG,EAAG,QACxB,IAAN,QAAyB,IAAN,EACrB,KADsC,EAC/B,EACF,QAAU,IAAN,EACT,KAD0B,EACnB,EACF,GAAU,SAAN,EAAiB,AAC1B,OAAO,CAAC,OACH,GAAI,IAAM,EACf,CADkB,aAGlB,OAAO,EAAmB,EAAG,EAEjC,OAAS,EAAE,EAAE,AACf,CAEA,aAAc,CAAK,CAAE,CACf,AAAE,CAAD,YAAkB,IACrB,EAD2B,AACnB,GADsB,CAClB,EAAO,EAAO,IAAI,CAAC,QAAO,EAGxC,IAAI,EAAI,EACR,EAAG,CACD,IAAM,EAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CACjB,EAAI,EAAM,KAAK,CAAC,EAAE,CAExB,GADA,EAAM,gBAAiB,EAAG,EAAG,QACnB,IAAN,QAAyB,IAAN,EACrB,KADsC,EAC/B,EACF,GAAI,AAAM,WAAW,AAC1B,OAAO,EACF,GAAI,AAAM,WAAW,AAC1B,OAAO,CAAC,OACH,GAAI,IAAM,EACf,CADkB,aAGlB,OAAO,EAAmB,EAAG,EAEjC,OAAS,EAAE,EAAE,AACf,CAIA,IAAK,CAAO,CAAE,CAAU,CAAE,CAAc,CAAE,CACxC,GAAI,EAAQ,UAAU,CAAC,OAAQ,CAC7B,GAAI,CAAC,GAAiC,KAAnB,EAA0B,AAC3C,MAAM,AAAI,MAAM,mDAGlB,GAAI,EAAY,CACd,IAAM,EAAQ,CAAC,CAAC,EAAE,EAAA,CAAY,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAG,CAAE,CAAC,EAAE,eAAe,CAAC,CAAG,CAAE,CAAC,EAAE,UAAU,CAAC,EAClG,GAAI,CAAC,GAAS,CAAK,CAAC,EAAE,GAAK,EACzB,MAAM,AAAI,IAD2B,EACrB,CAAC,oBAAoB,EAAE,EAAA,CAAY,CAEvD,CACF,CAEA,OAAQ,GACN,IAAK,WACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAG,EACzB,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,GAAG,CAAC,MAAO,EAAY,GAC5B,KACF,KAAK,WACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAG,EACzB,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,GAAG,CAAC,MAAO,EAAY,GAC5B,KACF,KAAK,WAIH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAG,EACzB,IAAI,CAAC,GAAG,CAAC,QAAS,EAAY,GAC9B,IAAI,CAAC,GAAG,CAAC,MAAO,EAAY,GAC5B,KAGF,KAAK,aAC4B,GAAG,CAA9B,IAAI,CAAC,UAAU,CAAC,MAAM,EACxB,IAAI,CAAC,GAAG,CAAC,QAAS,EAAY,GAEhC,IAAI,CAAC,GAAG,CAAC,MAAO,EAAY,GAC5B,KACF,KAAK,UACH,GAA+B,GAAG,CAA9B,IAAI,CAAC,UAAU,CAAC,MAAM,CACxB,MAAM,AAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAE3D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAG,EACzB,KAEF,KAAK,SAMc,IAAf,IAAI,CAAC,KAAK,EACK,IAAf,IAAI,CAAC,KAAK,EACiB,GAC3B,CADA,IAAI,CAAC,UAAU,CAAC,MAAM,GAEtB,IAAI,CAAC,KAAK,GAEZ,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,UAAU,CAAG,EAAE,CACpB,KACF,KAAK,SAKC,AAAe,QAAX,CAAC,KAAK,MAAU,IAAI,CAAC,UAAU,CAAC,MAAM,AAAK,GAAG,AACpD,IAAI,CAAC,KAAK,GAEZ,IAAI,CAAC,KAAK,CAAG,EACb,IAAI,CAAC,UAAU,CAAG,EAAE,CACpB,KACF,KAAK,QAKC,AAA2B,GAAG,KAA1B,CAAC,UAAU,CAAC,MAAM,EACxB,IAAI,CAAC,KAAK,GAEZ,IAAI,CAAC,UAAU,CAAG,EAAE,CACpB,KAGF,KAAK,MAAO,CACV,IAAM,EAAO,UAAO,GAEpB,GAA+B,GAAG,CAA9B,IAAI,CAAC,GAF6B,IAAI,GAEvB,CAAC,MAAM,CACxB,IAAI,CAAC,UAAU,CAAG,CAAC,EAAK,KACnB,CACL,IAAI,EAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAC9B,KAAO,EAAE,GAAK,EAAG,CACmB,UAA9B,AAAwC,OAAjC,IAAI,CAAC,UAAU,CAAC,EAAE,GAC3B,IAAI,CAAC,UAAU,CAAC,EAAE,GAClB,EAAI,CAAC,GAGT,GAAU,CAAC,IAAP,EAAU,CAEZ,GAAI,IAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAA2B,IAAnB,EAC9C,CADwE,KAClE,AAAI,MAAM,yDAElB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EACvB,CACF,CACA,GAAI,EAAY,CAGd,IAAI,EAAa,CAAC,EAAY,EAAK,EACZ,IAAnB,GAA0B,CAC5B,EAAa,CAAC,EAAW,EAEgC,GAAG,CAA1D,EAAmB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAE,GACrC,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAC7B,IAAI,CAAC,UAAU,CAAG,CAAA,EAGpB,IAAI,CAAC,UAAU,CAAG,CAEtB,CACA,KACF,CACA,QACE,MAAM,AAAI,MAAM,CAAC,4BAA4B,EAAE,EAAA,CAAS,CAC5D,CAKA,OAJA,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,MAAM,GAClB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CACrB,IAAI,CAAC,GAAG,EAAI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAA,CAAA,AAAM,EAEjC,IAAI,AACb,CACF,CAEA,EAAO,OAAO,CAAG,gCC5TjB,IAAM,EAAA,EAAA,CAAA,CAAA,OAeN,EAAO,OAAO,CAdA,CAAC,CAcE,CAdO,EAAS,GAAc,CAAK,IAClD,GAAI,aAAmB,EACrB,MAD6B,CACtB,EAET,GAAI,CACF,OAAO,IAAI,EAAO,EAAS,EAC7B,CAAE,MAAO,EAAI,CACX,GAAI,CAAC,EACH,OAAO,IADS,AAGlB,OAAM,CACR,CACF,gCCbA,IAAM,EAAA,EAAA,CAAA,CAAA,MAKN,GAAO,OAAO,CAJA,CAAC,CAIE,CAJO,KACtB,IAAM,EAAI,EAAM,EAAS,GACzB,OAAO,EAAI,EAAE,OAAO,CAAG,IACzB,gCCJA,IAAM,EAAA,EAAA,CAAA,CAAA,MAKN,GAAO,OAAO,CAJA,CAAC,CAIE,CAJO,KACtB,IAAM,EAAI,EAAM,EAAQ,IAAI,GAAG,OAAO,CAAC,SAAU,IAAK,GACtD,OAAO,EAAI,EAAE,OAAO,CAAG,IACzB,gCCJA,IAAM,EAAA,EAAA,CAAA,CAAA,OAkBN,EAAO,OAAO,CAhBF,CAAC,CAgBI,CAhBK,EAAS,EAAS,EAAY,KACzB,UAArB,AAA+B,OAAvB,IACV,EAAiB,EACjB,EAAa,EACb,OAAU,GAGZ,GAAI,CACF,OAAO,IAAI,EACT,aAAmB,EAAS,EAAQ,OAAO,CAAG,EAC9C,GACA,GAAG,CAAC,EAAS,EAAY,GAAgB,OAC7C,AADoD,CAClD,MAAO,EAAI,CACX,OAAO,IACT,CACF,gCCjBA,IAAM,EAAA,EAAA,CAAA,CAAA,OAyDN,EAAO,OAAO,CAvDD,CAAC,CAuDG,CAvDO,KACtB,IAAM,EAAK,EAAM,EAAU,MAAM,GAC3B,EAAK,EAAM,EAAU,MAAM,GAC3B,EAAa,EAAG,OAAO,CAAC,GAE9B,GAAmB,GAAG,CAAlB,EACF,OAAO,KAGT,IAAM,EAAW,EAAa,EACxB,EAAc,EAAW,EAAK,EAC9B,EAAa,EAAW,EAAK,EAC7B,EAAa,CAAC,CAAC,EAAY,UAAU,CAAC,MAAM,CAGlD,GAAI,AAFgB,EAAW,UAAU,CAAC,MAAM,EAE/B,CAAC,EAAY,CAQ5B,GAAI,CAAC,EAAW,KAAK,EAAI,CAAC,EAAW,KAAK,CACxC,CAD0C,KACnC,QAIT,GAA4C,GAAG,CAA3C,EAAW,WAAW,CAAC,UACzB,AAAI,EAAW,KAAK,EAAI,CAAC,EAAW,KAAK,CAChC,CADkC,OAGpC,OAEX,CAGA,IAAM,EAAS,EAAa,MAAQ,UAEpC,AAAI,EAAG,KAAK,GAAK,EAAG,KAAK,CAChB,CADkB,CACT,QAGd,EAAG,KAAK,GAAK,EAAG,KAAK,CAChB,CADkB,CACT,QAGd,EAAG,KAAK,GAAK,EAAG,KAAK,CAChB,CADkB,CACT,QAIX,YACT,gCCvDA,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADA,CAAC,CACE,CADC,IAAU,IAAI,EAAO,EAAG,GAAO,KAAK,+BCDtD,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADA,CAAC,CACE,CADC,IAAU,IAAI,EAAO,EAAG,GAAO,KAAK,+BCDtD,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADA,CAAC,CACE,CADC,IAAU,IAAI,EAAO,EAAG,GAAO,KAAK,+BCDtD,IAAM,EAAA,EAAA,CAAA,CAAA,MAKN,GAAO,OAAO,CAJK,CAAC,CAIH,CAJY,KAC3B,IAAM,EAAS,EAAM,EAAS,GAC9B,OAAQ,GAAU,EAAO,UAAU,CAAC,MAAM,CAAI,EAAO,UAAU,CAAG,IACpE,gCCJA,IAAM,EAAA,EAAA,CAAA,CAAA,MAIN,GAAO,OAAO,CAHE,CAAC,CAGA,CAHG,EAAG,IACrB,IAAI,EAAO,EAAG,GAAO,OAAO,CAAC,IAAI,EAAO,EAAG,kCCF7C,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADG,CAAC,CACD,CADI,EAAG,IAAU,EAAQ,EAAG,EAAG,iCCDhD,IAAM,EAAA,EAAA,CAAA,CAAA,MAEN,GAAO,OAAO,CADO,CAAC,CACL,CADQ,IAAM,EAAQ,EAAG,GAAG,iCCD7C,IAAM,EAAA,EAAA,CAAA,CAAA,MAMN,GAAO,OAAO,CALO,CAAC,CAKL,CALQ,EAAG,KAC1B,IAAM,EAAW,IAAI,EAAO,EAAG,GACzB,EAAW,IAAI,EAAO,EAAG,GAC/B,OAAO,EAAS,OAAO,CAAC,IAAa,EAAS,YAAY,CAAC,EAC7D,gCCLA,IAAM,EAAA,EAAA,CAAA,CAAA,MAEN,GAAO,OAAO,CADD,CAAC,CACG,CADG,IAAU,EAAK,IAAI,CAAC,CAAC,EAAG,IAAM,EAAa,EAAG,EAAG,kCCDrE,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADA,CAAC,CACE,CADI,IAAU,EAAK,IAAI,CAAC,CAAC,EAAG,IAAM,EAAa,EAAG,EAAG,kCCDtE,IAAM,EAAA,EAAA,CAAA,CAAA,MAEN,GAAO,OAAO,CADH,CAAC,CACK,CADF,EAAG,IAAU,EAAQ,EAAG,EAAG,GAAS,gCCDnD,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADH,CAAC,CACK,CADF,EAAG,IAAiC,EAAvB,EAAQ,EAAG,EAAG,iCCD1C,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADH,CAAC,CACK,CADF,EAAG,IAAmC,IAAzB,EAAQ,EAAG,EAAG,iCCD1C,IAAM,EAAA,EAAA,CAAA,CAAA,MAEN,GAAO,OAAO,CADF,CAAC,CACI,CADD,EAAG,IAAmC,IAAzB,EAAQ,EAAG,EAAG,iCCD3C,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADF,CAAC,CACI,CADD,EAAG,IAAU,EAAQ,EAAG,EAAG,IAAU,gCCDrD,IAAM,EAAA,EAAA,CAAA,CAAA,OAEN,EAAO,OAAO,CADF,CAAC,CACI,CADD,EAAG,IAAkC,GAAxB,EAAQ,EAAG,EAAG,iCCD3C,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OA8CN,EAAO,OAAO,CA5CF,CAAC,CA4CI,CA5CD,EAAI,EAAG,KACrB,OAAQ,GACN,IAAK,MAOH,MANiB,UAAb,AAAuB,OAAhB,IACT,EAAI,EAAE,OAAA,AAAO,EAEX,AAAa,UAAU,OAAhB,IACT,EAAI,EAAE,OAAA,AAAO,EAER,IAAM,CAEf,KAAK,MAOH,MANiB,UAAb,AAAuB,OAAhB,IACT,EAAI,EAAE,OAAO,AAAP,EAES,UAAb,AAAuB,OAAhB,IACT,EAAI,EAAE,OAAA,AAAO,EAER,IAAM,CAEf,KAAK,GACL,IAAK,IACL,IAAK,KACH,OAAO,EAAG,EAAG,EAAG,EAElB,KAAK,KACH,OAAO,EAAI,EAAG,EAAG,EAEnB,KAAK,IACH,OAAO,EAAG,EAAG,EAAG,EAElB,KAAK,KACH,OAAO,EAAI,EAAG,EAAG,EAEnB,KAAK,IACH,OAAO,EAAG,EAAG,EAAG,EAElB,KAAK,KACH,OAAO,EAAI,EAAG,EAAG,EAEnB,SACE,MAAM,AAAI,UAAU,CAAC,kBAAkB,EAAE,EAAA,CAAI,CACjD,CACF,gCClDA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,CAAE,OAAQ,CAAE,GAAE,CAAC,CAAE,CAAA,EAAA,CAAA,CAAA,MAyDvB,EAAO,OAAO,CAvDC,CAAC,CAuDC,CAvDQ,KACvB,GAAI,aAAmB,EACrB,MAD6B,CACtB,EAOT,GAJuB,UAAnB,AAA6B,OAAtB,GACT,GAAU,OAAO,EAAA,EAGI,UAAnB,AAA6B,OAAtB,EACT,OAAO,KAKT,IAAI,EAAQ,KACZ,GAAK,CAAD,AAHJ,EAAU,GAAW,EAAC,EAGT,GAAG,CAET,CAUL,AAZgB,IAaZ,EADE,EAAiB,EAAQ,iBAAiB,CAAG,CAAE,CAAC,EAAE,aAAa,CAAC,CAAG,CAAE,CAAC,EAAE,SAAS,CAAC,CAExF,KAAO,AAAC,GAAO,EAAe,IAAI,CAAC,EAAA,CAAQ,GACtC,CAAC,CAAF,EAAW,EAAM,KAAK,CAAG,CAAK,CAAC,EAAE,CAAC,MAAM,GAAK,EAAQ,MAAA,AAAM,EAC7D,CACI,AAAC,GACC,EAAK,KAAK,CAAG,CAAI,CAAC,EAAE,CAAC,MAAM,GAAK,EAAM,KAAK,CAAG,CAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CACnE,EAAQ,CAAA,EAEV,EAAe,SAAS,CAAG,EAAK,KAAK,CAAG,CAAI,CAAC,EAAE,CAAC,MAAM,CAAG,CAAI,CAAC,EAAE,CAAC,MAAM,AAGzE,GAAe,SAAS,CAAG,CAAC,CAC9B,MAxBE,EAAQ,EAAQ,KAAK,CAAC,EAAQ,iBAAiB,CAAG,CAAE,CAAC,EAAE,UAAU,CAAC,CAAG,CAAE,CAAC,EAAE,MAAM,CAAC,EA0BnF,GAAc,MAAM,CAAhB,EACF,OAAO,KAGT,IAAM,EAAQ,CAAK,CAAC,EAAE,CAChB,EAAQ,CAAK,CAAC,EAAE,EAAI,IACpB,EAAQ,CAAK,CAAC,EAAE,EAAI,IACpB,EAAa,EAAQ,iBAAiB,EAAI,CAAK,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAAK,CAAC,EAAE,CAAA,CAAE,CAAG,GACtE,EAAQ,EAAQ,iBAAiB,EAAI,CAAK,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAAK,CAAC,EAAE,CAAA,CAAE,CAAG,GAEvE,OAAO,EAAM,CAAA,EAAG,EAAM,CAAC,EAAE,EAAM,CAAC,EAAE,EAAA,EAAQ,EAAA,EAAa,EAAA,CAAO,CAAE,EAClE,8BCnBA,GAAO,OAAO,CAvCd,EAuCiB,IAtCf,AADI,aACW,CACb,IAAI,CAAC,GAAG,CAAG,IACX,IAAI,CAAC,GAAG,CAAG,IAAI,GACjB,CAEA,IAAK,CAAG,CAAE,CACR,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAC3B,GAAc,SAAV,EAAqB,AAMvB,OAFA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAK,GACX,CAEX,CAEA,OAAQ,CAAG,CAAE,CACX,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EACzB,CAEA,IAAK,CAAG,CAAE,CAAK,CAAE,CAGf,GAAI,CAAC,AAFW,IAAI,CAAC,MAAM,CAAC,SAEF,IAAV,EAAqB,CAEnC,GAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAI,IAAI,CAAC,GAAG,CAAE,CAC7B,IAAM,EAAW,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAC7C,IAAI,CAAC,MAAM,CAAC,EACd,CAEA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAK,EACpB,CAEA,OAAO,IAAI,AACb,CACF,gCCrCA,IAAM,EAAmB,MAGzB,OAAM,EACJ,YAAa,CAAK,CAAE,CAAO,CAAE,CAG3B,GAFA,EAAU,EAAa,GAEnB,aAAiB,EACnB,GACkB,CAAC,CAFO,AAEN,EAAQ,KAAK,GAA/B,EAAM,KAAK,EACiB,CAAC,CAAC,EAAQ,iBAAiB,EACvD,CADA,EAAM,iBAAiB,CAEvB,OAAO,OAEP,OAAO,IAAI,EAAM,EAAM,GAAG,CAAE,GAIhC,GAAI,aAAiB,EAKnB,OAHA,GAF+B,CAE3B,CAAC,GAAG,CAAG,EAAM,KAAK,CACtB,IAAI,CAAC,GAAG,CAAG,CAAC,CAAC,EAAM,CAAC,CACpB,IAAI,CAAC,SAAS,MAAG,EACV,IAAI,CAsBb,GAnBA,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,EAAQ,KAAK,CAC5B,IAAI,CAAC,iBAAiB,CAAG,CAAC,CAAC,EAAQ,iBAAiB,CAKpD,IAAI,CAAC,GAAG,CAAG,EAAM,IAAI,GAAG,OAAO,CAAC,EAAkB,KAGlD,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,GAAG,CAChB,KAAK,CAAC,KACP,CACC,GAAG,CAAC,GAAK,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,IAChC,CAGC,MAAM,CAAC,GAAK,AALgC,EAK9B,MAAM,EAEnB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAClB,CADoB,KACd,AAAI,IANsC,MAM5B,CAAC,sBAAsB,EAAE,IAAI,CAAC,GAAG,CAAA,CAAE,EAIzD,GAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAG,EAAG,CAEvB,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,CAEzB,GADA,IAAI,CAAC,GAAG,CAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAK,CAAC,EAAU,CAAC,CAAC,EAAE,GACvB,GAAG,CAAvB,IAAI,CAAC,GAAG,CAAC,MAAM,CACjB,IAAI,CAAC,GAAG,CAAG,CAAC,EAAM,MACb,GAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAG,GAE3B,AAF8B,IAEzB,IAAM,KAAK,IAAI,CAAC,GAAG,CAAE,AACxB,GAAiB,IAAb,EAAE,MAAM,EAAU,EAAM,CAAC,CAAC,EAAE,EAAG,CACjC,IAAI,CAAC,GAAG,CAAG,CAAC,EAAE,CACd,KACF,CACF,CAEJ,CAEA,IAAI,CAAC,SAAS,MAAG,CACnB,CAEA,IAAI,OAAS,CACX,QAAuB,IAAnB,IAAI,CAAC,SAAS,CAAgB,CAChC,IAAI,CAAC,SAAS,CAAG,GACjB,IAAK,IAAI,EAAI,EAAG,EAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAE,IAAK,CACpC,EAAI,GAAG,CACT,IAAI,CAAC,SAAS,EAAI,IAAA,EAEpB,IAAM,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,CACzB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAAK,AACjC,EAAI,GAAG,CACT,IAAI,CAAC,SAAS,EAAI,GAAA,EAEpB,IAAI,CAAC,SAAS,EAAI,CAAK,CAAC,EAAE,CAAC,QAAQ,GAAG,IAAI,EAE9C,CACF,CACA,OAAO,IAAI,CAAC,SAAS,AACvB,CAEA,QAAU,CACR,OAAO,IAAI,CAAC,KAAK,AACnB,CAEA,UAAY,CACV,OAAO,IAAI,CAAC,KAAK,AACnB,CAEA,WAAY,CAAK,CAAE,CAMjB,IAAM,EAAU,CAFd,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAI,CAAA,CAAuB,EACzD,EAAD,EAAK,CAAC,OAAO,CAAC,KAAK,EAAI,CAAA,CAAU,EACR,IAAM,EAC3B,EAAS,EAAM,GAAG,CAAC,GACzB,GAAI,EACF,MADU,CACH,EAGT,IAAM,EAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAE1B,EAAK,EAAQ,CAAE,CAAC,EAAE,gBAAgB,CAAC,CAAG,CAAE,CAAC,EAAE,WAAW,CAAC,CAE7D,EAAM,iBADN,CACwB,CADhB,EAAM,OAAO,CAAC,EAAI,EAAc,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAKtE,EAAM,kBADN,CACyB,CADjB,EAAM,OAAO,CAAC,CAAE,CAAC,EAAE,cAAc,CAAC,CAAE,IAK5C,EAAM,aADN,CACoB,CADZ,EAAM,OAAO,CAAC,CAAE,CAAC,EAAE,SAAS,CAAC,CAAE,IAKvC,EAAM,aADN,CACoB,CADZ,EAAM,OAAO,CAAC,CAAE,CAAC,EAAE,SAAS,CAAC,CAAE,IAMvC,IAAI,EAAY,EACb,KAAK,CAAC,KACN,GAAG,CAAC,GAAQ,EAAgB,EAAM,IAAI,CAAC,OAAO,GAC9C,IAAI,CAAC,KACL,KAAK,CAAC,MACP,CACC,GAAG,CAAC,GAAQ,EAAY,EAAM,IAAI,CAAC,OAAO,GAEzC,EAH2B,EAK7B,EAAY,CAFH,CAEa,MAAM,CAAC,IAC3B,EAAM,uBAAwB,EAAM,IAAI,CAAC,OAAO,EACzC,CAAC,CAAC,EAAK,KAAK,CAAC,CAAE,CAAC,EAAE,eAAe,CAAC,GAC3C,EAEF,EAAM,aAAc,GAKpB,IAAM,EAAW,IAAI,IAErB,IAAK,IAAM,KADS,EAAU,CACX,EADc,CAAC,GAAQ,IAAI,EAAW,EAAM,IAAI,CAAC,OAAO,GAC3C,CAC9B,GAAI,EAAU,GACZ,IADmB,EACZ,CAAC,EAAK,CAEf,EAAS,GAAG,CAAC,EAAK,KAAK,CAAE,EAC3B,CACI,EAAS,IAAI,CAAG,GAAK,EAAS,GAAG,CAAC,KAAK,AACzC,EAAS,MAAM,CAAC,IAGlB,IAAM,EAAS,IAAI,EAAS,MAAM,GAAG,CAErC,OADA,EAAM,GAAG,CAAC,EAAS,GACZ,CACT,CAEA,WAAY,CAAK,CAAE,CAAO,CAAE,CAC1B,GAAI,CAAC,CAAC,aAAiB,CAAA,CAAK,CAC1B,EAD6B,IACvB,AAAI,UAAU,uBAGtB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,AAAC,GAElB,EAAc,EAAiB,IAC/B,EAAM,GAAG,CAAC,IAAI,CAAC,AAAC,GAEZ,EAAc,EAAkB,IAChC,EAAgB,KAAK,CAAC,AAAC,GACd,EAAiB,KAAK,CAAC,AAAC,GACtB,EAAe,UAAU,CAAC,EAAiB,MAOhE,CAGA,KAAM,CAAO,CAAE,CACb,GAAI,CAAC,EACH,OAAO,AADK,EAId,GAAuB,UAAnB,AAA6B,OAAtB,EACT,GAAI,CACF,EAAU,IAAI,EAAO,EAAS,IAAI,CAAC,OAAO,CAC5C,CAAE,MAAO,EAAI,CACX,MAAO,EACT,CAGF,IAAK,IAAI,EAAI,EAAG,EAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAE,IACnC,AADwC,GACpC,EAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,CAAE,EAAS,IAAI,CAAC,OAAO,EAC5C,CAD+C,MACxC,EAGX,OAAO,CACT,CACF,CAEA,EAAO,OAAO,CAAG,EAGjB,IAAM,EAAQ,GADR,CAAA,AACY,EADZ,CAAA,CAAA,KAAA,EAGA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,CACJ,OAAQ,CAAE,GACV,CAAC,uBACD,CAAqB,kBACrB,CAAgB,kBAChB,CAAgB,CACjB,CAAA,EAAA,CAAA,CAAA,MACK,yBAAE,CAAuB,YAAE,CAAU,CAAE,CAAA,EAAA,CAAA,CAAA,OAEvC,EAAY,GAAiB,aAAZ,EAAE,KAAK,CACxB,EAAQ,GAAiB,KAAZ,EAAE,KAAK,CAIpB,EAAgB,CAAC,EAAa,KAClC,IAAI,GAAS,EACP,EAAuB,EAAY,KAAK,GAC1C,EAAiB,EAAqB,GAAG,GAE7C,KAAO,GAAU,EAAqB,MAAM,CAAE,CAC5C,EAAS,EAAqB,KAAK,CAAC,AAAC,GAC5B,EAAe,UAAU,CAAC,EAAiB,IAGpD,EAAiB,EAAqB,GAAG,GAG3C,OAAO,CACT,EAKM,EAAkB,CAAC,EAAM,KAC7B,EAAM,OAAQ,EAAM,GAEpB,EAAM,QADN,CACe,CADR,EAAc,EAAM,IAG3B,EAAM,SADN,CACgB,CADT,EAAc,EAAM,IAG3B,EAAM,SADN,CACgB,CADT,EAAe,EAAM,IAG5B,EAAM,QADN,CACe,CADR,EAAa,EAAM,IAEnB,GAGH,EAAM,GAAM,CAAC,GAA2B,MAArB,EAAG,WAAW,IAAqB,MAAP,EAS/C,EAAgB,CAAC,EAAM,IACpB,EACJ,IAAI,GACJ,KAAK,CAAC,OACN,GAAG,CAAE,AAAD,GAAO,EAAa,EAAG,IAC3B,IAAI,CAAC,KAGJ,EAAe,CAAC,EAAM,KAC1B,IAAM,EAAI,EAAQ,KAAK,CAAG,CAAE,CAAC,EAAE,UAAU,CAAC,CAAG,CAAE,CAAC,EAAE,KAAK,CAAC,CACxD,OAAO,EAAK,OAAO,CAAC,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,SAE9B,EAoBJ,OArBA,EAAM,QAAS,EAAM,EAAG,EAAG,EAAG,EAAG,GAG7B,EAAI,GACN,CADU,CACJ,GACG,EAAI,GACb,CADiB,CACX,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,CAC1B,EAAI,GAEb,CAFiB,CAEX,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,CAChC,GACT,CADa,CACP,kBAAmB,GACzB,EAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACzB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,EAGtB,EAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACpB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,CAGxB,EAAM,eAAgB,GACf,CACT,EACF,EAUM,EAAgB,CAAC,EAAM,IACpB,EACJ,IAAI,GACJ,KAAK,CAAC,OACN,GAAG,CAAC,AAAC,GAAM,EAAa,EAAG,IAC3B,IAAI,CAAC,KAGJ,EAAe,CAAC,EAAM,KAC1B,EAAM,QAAS,EAAM,GACrB,IAAM,EAAI,EAAQ,KAAK,CAAG,CAAE,CAAC,EAAE,UAAU,CAAC,CAAG,CAAE,CAAC,EAAE,KAAK,CAAC,CAClD,EAAI,EAAQ,iBAAiB,CAAG,KAAO,GAC7C,OAAO,EAAK,OAAO,CAAC,EAAG,CAAC,EAAG,EAAG,EAAG,EAAG,SAE9B,EA2CJ,OA5CA,EAAM,QAAS,EAAM,EAAG,EAAG,EAAG,EAAG,GAG7B,EAAI,GACN,CADU,CACJ,GACG,EAAI,GACb,CADiB,CACX,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,CAC9B,EAAI,GAEX,CAFe,CACP,KAAK,CAAX,EACI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,CAEvC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,CAEnC,GACT,CADa,CACP,kBAAmB,GAGrB,EAFM,KAAK,CAAX,EACQ,AAAN,KAAW,GACP,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACzB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,EAAE,CAAC,CAEnB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACzB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,CAGlB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACzB,EAAE,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,GAGrB,EAAM,SAGF,EAFM,KAAK,CAAX,EACQ,KAAK,CAAX,EACI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAA,EAClB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,EAAE,CAAC,CAEvB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAA,EAClB,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,CAGtB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EACpB,EAAE,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,EAIvB,EAAM,eAAgB,GACf,CACT,EACF,EAEM,EAAiB,CAAC,EAAM,KAC5B,EAAM,iBAAkB,EAAM,GACvB,EACJ,KAAK,CAAC,OACN,GAAG,CAAE,AAAD,GAAO,EAAc,EAAG,IAC5B,IAAI,CAAC,MAGJ,EAAgB,CAAC,EAAM,KAC3B,EAAO,EAAK,IAAI,GAChB,IAAM,EAAI,EAAQ,KAAK,CAAG,CAAE,CAAC,EAAE,WAAW,CAAC,CAAG,CAAE,CAAC,EAAE,MAAM,CAAC,CAC1D,OAAO,EAAK,OAAO,CAAC,EAAG,CAAC,EAAK,EAAM,EAAG,EAAG,EAAG,KAC1C,EAAM,SAAU,EAAM,EAAK,EAAM,EAAG,EAAG,EAAG,GAC1C,IAAM,EAAK,EAAI,GACT,EAAK,GAAM,EAAI,GACf,EAAK,GAAM,EAAI,GAgErB,MA7Da,MAAT,CAAgB,EAFP,IAEa,AACxB,EAAO,EAAA,EAKT,EAAK,EAAQ,iBAAiB,CAAG,KAAO,GAEpC,EAGA,EAHI,AACO,MAAT,GAAyB,KAAK,CAAd,EAEZ,WAGA,IAEC,MAGL,EAHa,CAIf,CADM,GACF,CAJiB,CAMvB,EAAI,EAES,KAAK,CAAd,GAGF,EAAO,KACH,GACF,CADM,CACF,CAAC,EAAI,EACT,EAAI,GAGJ,EAAI,CAAC,EAAI,EACT,EAAI,GAEY,MAAM,CAAf,IAGT,EAAO,IACH,EACF,EADM,AACF,CAAC,EAAI,EAET,EAAI,CAAC,EAAI,GAIA,KAAK,CAAd,IACF,EAAK,IAAA,EAGP,EAAM,CAAA,EAAG,EAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAA,EAAI,EAAA,CAAI,EACzB,EACT,EAAM,AADO,CACN,EAAE,EAAE,EAAE,IAAI,EAAE,EAAG,EAAE,EAAE,CAAC,EAAI,EAAE,MAAM,CAAC,CAC/B,IAAI,AACb,EAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EACrB,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAI,EAAE,IAAI,CAAC,EAGxB,EAAM,gBAAiB,GAEhB,CACT,EACF,EAIM,EAAe,CAAC,EAAM,KAC1B,EAAM,eAAgB,EAAM,GAErB,EACJ,IAAI,GACJ,OAAO,CAAC,CAAE,CAAC,EAAE,IAAI,CAAC,CAAE,KAGnB,EAAc,CAAC,EAAM,KACzB,EAAM,cAAe,EAAM,GACpB,EACJ,IAAI,GACJ,OAAO,CAAC,CAAE,CAAC,EAAQ,iBAAiB,CAAG,EAAE,OAAO,CAAG,EAAE,IAAI,CAAC,CAAE,KAS3D,EAAgB,GAAS,CAAC,EAC9B,EAAM,EAAI,EAAI,EAAI,EAAK,EACvB,EAAI,EAAI,EAAI,EAAI,KAEd,EADE,EAAI,GACC,EADI,CAEF,EAAI,GACN,CAAC,CADU,CACR,EAAE,EAAG,IAAI,EAAE,EAAQ,KAAO,GAAA,CAAI,CAC/B,EAAI,GACN,CAAC,CADU,CACR,EAAE,EAAG,CAAC,EAAE,EAAG,EAAE,EAAE,EAAQ,KAAO,GAAA,CAAI,CACnC,EACF,CAAC,EAAE,AADI,EACF,EAAA,CAAM,CAEX,CAAC,EAAE,EAAE,EAAA,EAAO,EAAQ,KAAO,GAAA,CAAI,CAItC,EADE,EAAI,GACD,EADM,CAEF,EAAI,GACR,CAAC,CADY,AACX,EAAE,CAAC,EAAK,EAAE,MAAM,CAAC,CACf,EAAI,GACR,CAAC,CADY,AACX,EAAE,EAAG,CAAC,EAAE,CAAC,EAAK,EAAE,IAAI,CAAC,CACnB,EACJ,CAAC,EADQ,AACN,EAAE,EAAG,CAAC,EAAE,EAAG,CAAC,EAAE,EAAG,CAAC,EAAE,EAAA,CAAK,CACxB,EACJ,CAAC,CAAC,EAAE,CADO,CACJ,CAAC,EAAE,EAAG,CAAC,EAAE,CAAC,EAAK,EAAE,EAAE,CAAC,CAE3B,CAAC,EAAE,EAAE,EAAA,CAAI,CAGT,CAAA,EAAG,EAAK,CAAC,EAAE,EAAA,CAAI,CAAC,IAAI,IAGvB,EAAU,CAAC,EAAK,EAAS,KAC7B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAI,MAAM,CAAE,IAC9B,AADmC,GAC/B,CAAC,CAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GACf,OADyB,AAClB,EAIX,GAAI,EAAQ,UAAU,CAAC,MAAM,EAAI,CAAC,EAAQ,iBAAiB,CAAE,CAM3D,IAAK,IAAI,EAAI,EAAG,EAAI,EAAI,MAAM,CAAE,IAAK,AAEnC,GADA,EAAM,CAAG,CAAC,EAAE,CAAC,MAAM,EACf,CAAG,CAAC,EAAE,CAAC,MAAM,GAAK,EAAW,GAAG,EAAE,AAIlC,CAAG,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAG,EAAG,CACvC,IAAM,EAAU,CAAG,CAAC,EAAE,CAAC,MAAM,CAC7B,GAAI,EAAQ,KAAK,GAAK,EAAQ,KAAK,EAC/B,EAAQ,KAAK,GAAK,EAAQ,KAAK,EAC/B,EAAQ,KAAK,GAAK,EAAQ,KAAK,CACjC,CADmC,MAC5B,CAEX,CAIF,MAAO,EACT,CAEA,OAAO,CACT,gCCziBA,IAAM,EAAM,OAAO,aAEnB,OAAM,EACJ,WAAW,KAAO,CAChB,OAAO,CACT,CAEA,YAAa,CAAI,CAAE,CAAO,CAAE,CAG1B,GAFA,EAAU,EAAa,GAEnB,aAAgB,EAClB,GAAI,AAAe,CAAC,CAAC,EAAQ,GADC,EACI,EAAE,GAA3B,KAAK,CACZ,OAAO,OAEP,EAAO,EAAK,KAAK,CAKrB,EAAM,aADN,CACoB,CADb,EAAK,IAAI,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,KACX,GAC1B,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,EAAQ,KAAK,CAC5B,IAAI,CAAC,KAAK,CAAC,GAEP,IAAI,CAAC,MAAM,GAAK,EAClB,GADuB,CACnB,CAAC,KAAK,CAAG,GAEb,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,QAAQ,CAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAGlD,EAAM,OAAQ,IAAI,CACpB,CAEA,MAAO,CAAI,CAAE,CACX,IAAM,EAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAG,CAAE,CAAC,EAAE,eAAe,CAAC,CAAG,CAAE,CAAC,EAAE,UAAU,CAAC,CACjE,EAAI,EAAK,KAAK,CAAC,GAErB,GAAI,CAAC,EACH,CADM,KACA,AAAI,UAAU,CAAC,oBAAoB,EAAE,EAAA,CAAM,EAGnD,IAAI,CAAC,QAAQ,CAAG,KAAS,KAAR,CAAC,EAAE,CAAiB,CAAC,CAAC,EAAE,CAAG,GACtB,KAAK,CAAvB,IAAI,CAAC,QAAQ,GACf,IAAI,CAAC,QAAQ,CAAG,EAAA,EAIb,CAAC,CAAC,EAAE,CAGP,CAHS,GAGL,CAAC,MAAM,CAAG,IAAI,EAAO,CAAC,CAAC,EAAE,CAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAFjD,IAAI,CAAC,MAAM,CAAG,CAIlB,CAEA,UAAY,CACV,OAAO,IAAI,CAAC,KAAK,AACnB,CAEA,KAAM,CAAO,CAAE,CAGb,GAFA,EAAM,kBAAmB,EAAS,IAAI,CAAC,OAAO,CAAC,KAAK,EAEhD,IAAI,CAAC,MAAM,GAAK,GAAO,IAAY,EACrC,GAD0C,IACnC,EAGT,GAAuB,UAAnB,AAA6B,OAAtB,EACT,GAAI,CACF,EAAU,IAAI,EAAO,EAAS,IAAI,CAAC,OAAO,CAC5C,CAAE,MAAO,EAAI,CACX,OAAO,CACT,CAGF,OAAO,EAAI,EAAS,IAAI,CAAC,QAAQ,CAAE,IAAI,CAAC,MAAM,CAAE,IAAI,CAAC,OAAO,CAC9D,CAEA,WAAY,CAAI,CAAE,CAAO,CAAE,CACzB,GAAI,CAAC,CAAC,aAAgB,CAAA,CAAU,CAC9B,EADiC,IAC3B,AAAI,UAAU,kCAGtB,AAAsB,IAAI,CAAtB,IAAI,CAAC,QAAQ,CACf,AAAmB,IAAI,CAAnB,IAAI,CAAC,KAAK,EAGP,IAAI,EAAM,EAAK,KAAK,CAAE,GAAS,IAAI,CAAC,IAAI,CAAC,KAAK,EAC1B,IAAI,CAAtB,EAAK,QAAQ,CACtB,AAAmB,IAAI,CAAnB,EAAK,KAAK,EAGP,IAAI,EAAM,IAAI,CAAC,KAAK,CAAE,GAAS,IAAI,CAAC,EAAK,MAAM,EAMxD,EAAI,CAHJ,EAAU,EAAa,EAAA,EAGX,iBAAiB,EAC1B,CAAe,CAAhB,gBAAK,CAAC,KAAK,EAAkC,aAAf,EAAK,KAAK,AAAK,CAAU,EAGrD,CAHwD,AAGvD,EAAQ,iBAAiB,GAC3B,CAAD,GAAK,CAAC,KAAK,CAAC,UAAU,CAAC,WAAa,EAAK,KAAK,CAAC,UAAU,CAAC,SAAA,CAAS,GAAG,GAKpE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAQ,EAAK,QAAQ,CAAC,UAAU,CAAC,MAAM,AAIhE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAQ,EAAK,QAAQ,CAAC,UAAU,CAAC,MAAM,AAKjE,IAAI,CAAC,MAAM,CAAC,OAAO,GAAK,EAAK,MAAM,CAAC,OAAO,EAC5C,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAQ,EAAK,QAAQ,CAAC,QAAQ,CAAC,MAIpD,AAJ0D,EAItD,IAAI,CAAC,MAAM,CAAE,IAAK,EAAK,MAAM,CAAE,IACrC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAQ,EAAK,QAAQ,CAAC,UAAU,CAAC,MAAM,AAI9D,EAAI,IAAI,CAAC,MAAM,CAAE,IAAK,EAAK,MAAM,CAAE,IACrC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAQ,EAAK,QAAQ,CAAC,UAAU,CAAC,KAI9D,CAJoE,AAKtE,CAEA,EAAO,OAAO,CAAG,EAEjB,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,CAAE,OAAQ,CAAE,GAAE,CAAC,CAAE,CAAA,EAAA,CAAA,CAAA,MACjB,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,qCC5IN,IAAM,EAAA,EAAA,CAAA,CAAA,OASN,EAAO,OAAO,CARI,CAAC,CAQF,CARW,EAAO,KACjC,GAAI,CACF,EAAQ,IAAI,EAAM,EAAO,EAC3B,CAAE,MAAO,EAAI,CACX,OAAO,CACT,CACA,OAAO,EAAM,IAAI,CAAC,EACpB,gCCRA,IAAM,EAAA,EAAA,CAAA,CAAA,MAON,GAAO,OAAO,CAJQ,CAAC,CAIN,CAJa,IAC5B,IAAI,EAAM,EAAO,GAAS,GAAG,CAC1B,GAAG,CAAC,GAAQ,EAAK,GAAG,CAAC,GAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,mCCL/D,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAuBN,EAAO,OAAO,CArBQ,CAAC,CAqBN,CArBgB,EAAO,KACtC,IAAI,EAAM,KACN,EAAQ,KACR,EAAW,KACf,GAAI,CACF,EAAW,IAAI,EAAM,EAAO,EAC9B,CAAE,MAAO,EAAI,CACX,OAAO,IACT,CAWA,OAVA,EAAS,OAAO,CAAC,AAAC,IACZ,EAAS,IAAI,CAAC,IAAI,CAEhB,CAAC,GAA4B,KAArB,EAAM,OAAO,CAAC,EAAQ,GAAG,CAGnC,EAAQ,IAAI,EADZ,EAAM,EACkB,CAAL,CAAK,CAG9B,GACO,CACT,gCCvBA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAsBN,EAAO,OAAO,CArBQ,CAAC,CAqBN,CArBgB,EAAO,KACtC,IAAI,EAAM,KACN,EAAQ,KACR,EAAW,KACf,GAAI,CACF,EAAW,IAAI,EAAM,EAAO,EAC9B,CAAE,MAAO,EAAI,CACX,OAAO,IACT,CAWA,OAVA,EAAS,OAAO,CAAC,AAAC,IACZ,EAAS,IAAI,CAAC,IAAI,CAEhB,CAAC,GAA4B,GAAG,CAAxB,EAAM,OAAO,CAAC,MAGxB,EAAQ,IAAI,EADZ,EAAM,EACkB,CAAL,CAAK,CAG9B,GACO,CACT,gCCtBA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MA0DN,GAAO,OAAO,CAxDK,CAAC,CAwDH,CAxDU,KACzB,EAAQ,IAAI,EAAM,EAAO,GAEzB,IAAI,EAAS,IAAI,EAAO,SACxB,GAAI,EAAM,IAAI,CAAC,KAIf,EAAS,EAJe,EAIX,EAAO,WAChB,EAAM,IAAI,CAAC,IAJb,KAIsB,EAJf,EAQT,EAAS,KACT,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,GAAG,CAAC,MAAM,CAAE,EAAE,EAAG,CACzC,IAAM,EAAc,EAAM,GAAG,CAAC,EAAE,CAE5B,EAAS,KACb,EAAY,OAAO,CAAC,AAAC,IAEnB,IAAM,EAAU,IAAI,EAAO,EAAW,MAAM,CAAC,OAAO,EACpD,OAAQ,EAAW,QAAQ,EACzB,IAAK,IAC+B,AAA9B,GAAiC,GAAzB,UAAU,CAAC,MAAM,CAC3B,EAAQ,KAAK,GAEb,EAAQ,UAAU,CAAC,IAAI,CAAC,GAE1B,EAAQ,GAAG,CAAG,EAAQ,MAAM,EAE9B,KAAK,GACL,IAAK,MACC,CAAC,GAAU,EAAG,EAAS,EAAA,GAAS,AAClC,GAAS,CAAA,EAEX,KACF,KAAK,IACL,IAAK,KAEH,KAEF,SACE,MAAU,AAAJ,MAAU,CAAC,sBAAsB,EAAE,EAAW,QAAQ,CAAA,CAAE,CAClE,CACF,GACI,GAAW,EAAC,GAAU,EAAZ,AAAe,EAAQ,EAAA,CAAO,GAAG,AAC7C,EAAS,CAAA,CAEb,QAEA,AAAI,GAAU,EAAM,IAAI,CAAC,GAChB,EAGF,IAJ2B,AAKpC,gCC3DA,IAAM,EAAA,EAAA,CAAA,CAAA,OAUN,EAAO,OAAO,CATK,CAAC,CASH,CATU,KACzB,GAAI,CAGF,OAAO,IAAI,EAAM,EAAO,GAAS,KAAK,EAAI,GAC5C,CAAE,MAAO,EAAI,CACX,OAAO,IACT,CACF,gCCTA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,KAAE,CAAG,CAAE,CAAG,EACV,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MAuEN,GAAO,OAAO,CArEE,CAAC,CAqEA,CArES,EAAO,EAAM,SAIjC,EAAM,EAAO,EAAM,EAAM,EAC7B,OAJA,EAAU,IAAI,EAAO,EAAS,GAC9B,EAAQ,IAAI,EAAM,EAAO,GAGjB,GACN,IAAK,IACH,EAAO,EACP,EAAQ,EACR,EAAO,EACP,EAAO,IACP,EAAQ,KACR,KACF,KAAK,IACH,EAAO,EACP,EAAQ,EACR,EAAO,EACP,EAAO,IACP,EAAQ,KACR,KACF,SACE,MAAM,AAAI,UAAU,wCACxB,CAGA,GAAI,EAAU,EAAS,EAAO,GAC5B,OADsC,AAC/B,EAMT,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,GAAG,CAAC,MAAM,CAAE,EAAE,EAAG,CACzC,IAAM,EAAc,EAAM,GAAG,CAAC,EAAE,CAE5B,EAAO,KACP,EAAM,KAiBV,GAfA,EAAY,OAAO,CAAE,AAAD,IACd,EAAW,MAAM,GAAK,IACxB,CAD6B,CAChB,IAAI,EAAW,UAAA,EAE9B,EAAO,GAAQ,EACf,EAAM,GAAO,EACT,EAAK,EAAW,MAAM,CAAE,EAAK,MAAM,CAAE,GACvC,EAAO,EACE,EAAK,CAFmC,CAExB,MAAM,CAAE,EAAI,MAAM,CAAE,KAC7C,EAAM,CAAA,CAEV,CAH2D,EAOvD,EAAK,QAAQ,GAAK,GAAQ,EAAK,QAAQ,GAAK,GAM5C,CAAC,CAAC,EAAI,AAN6C,QAMrC,EAAI,EAAI,QAAQ,GAAK,CAAA,CAAI,EACvC,EAAM,EAAS,EAAI,MAAM,GAElB,AAFqB,EAEjB,QAAQ,GAAK,GAAS,EAAK,EAAS,EAAI,MAAM,EAR3D,CAQ8D,MARvD,CAWX,CACA,OAAO,CACT,gCC5EA,IAAM,EAAA,EAAA,CAAA,CAAA,MAEN,GAAO,OAAO,CADF,CAAC,CACI,CADK,EAAO,IAAY,EAAQ,EAAS,EAAO,IAAK,iCCFtE,IAAM,EAAA,EAAA,CAAA,CAAA,MAGN,GAAO,OAAO,CADF,CAAC,CACI,CADK,EAAO,IAAY,EAAQ,EAAS,EAAO,IAAK,iCCFtE,IAAM,EAAA,EAAA,CAAA,CAAA,OAMN,EAAO,OAAO,CALK,CAAC,CAKH,CALO,EAAI,KAC1B,EAAK,IAAI,EAAM,EAAI,GACnB,EAAK,IAAI,EAAM,EAAI,GACZ,EAAG,UAAU,CAAC,EAAI,kCCD3B,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACN,EAAO,OAAO,CAAG,CAAC,EAAU,EAAO,KACjC,IAAM,EAAM,EAAE,CACV,EAAQ,KACR,EAAO,KACL,EAAI,EAAS,IAAI,CAAC,CAAC,EAAG,IAAM,EAAQ,EAAG,EAAG,IAChD,IAAK,IAAM,KAAW,EAAG,AACN,EAAU,EAAS,EAAO,IAEzC,EAAO,EACH,AAAC,IACH,EAAQ,CADE,AACF,IAGN,GACF,EAAI,CADI,GACA,CAAC,CAAC,EAAO,EAAK,EAExB,EAAO,KACP,EAAQ,MAGR,GACF,EAAI,EADK,EACD,CAAC,CAAC,EAAO,KAAK,EAGxB,IAAM,EAAS,EAAE,CACjB,IAAK,GAAM,CAAC,EAAK,EAAI,GAAI,EACnB,EADwB,EAChB,EACV,EAAO,CADQ,GACJ,CAAC,GACH,AAAC,GAAO,IAAQ,CAAC,CAAC,EAAE,CAEnB,CAFqB,CAItB,GAFM,CAEE,CAAC,CAAC,EAAE,CACrB,CADuB,CAChB,IAAI,CAAC,CAAC,EAAE,EAAE,EAAA,CAAK,EAEtB,EAAO,IAAI,CAAC,CAAA,EAAG,EAAI,GAAG,EAAE,EAAA,CAAK,EAJ7B,EAAO,IAAI,CAAC,CAAC,EAAE,EAAE,EAAA,CAAK,EAFtB,EAAO,IAAI,CAAC,KAShB,IAAM,EAAa,EAAO,IAAI,CAAC,QACzB,EAAgC,UAArB,OAAO,EAAM,GAAG,CAAgB,EAAM,GAAG,CAAG,OAAO,GACpE,OAAO,EAAW,MAAM,CAAG,EAAS,MAAM,CAAG,EAAa,CAC5D,gCC9CA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,KAAE,CAAG,CAAE,CAAG,EACV,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAkEA,EAA+B,CAAC,IAAI,EAAW,aAAa,CAC5D,EAAiB,CAAC,IAAI,EAAW,WAAW,CAE5C,EAAe,CAAC,EAAK,EAAK,SAwB1B,EAAI,EAeJ,EA6BA,EAAQ,EACR,EAAU,EApEd,GAAI,IAAQ,EACV,GADe,IACR,EAGT,GAAmB,IAAf,EAAI,MAAM,EAAU,CAAG,CAAC,EAAE,CAAC,MAAM,GAAK,EACxC,GAAmB,AAD0B,IACzC,EAAI,MAAM,EAAU,CAAG,CAAC,EAAE,CAAC,MAAM,GAAK,EACxC,GAD6C,IACtC,OAEP,EADS,EAAQ,iBAAiB,CAC5B,CAD8B,CAG9B,EAIV,GAAmB,IAAf,EAAI,MAAM,EAAU,CAAG,CAAC,EAAE,CAAC,MAAM,GAAK,EACxC,GAD6C,AACzC,EAAQ,iBAAiB,CAC3B,CAD6B,KACtB,QAEP,EAAM,EAIV,IAAM,EAAQ,IAAI,IAElB,IAAK,IAAM,KAAK,EACK,EADA,IACf,EAAE,QAAQ,EAAY,AAAe,MAAM,GAAnB,QAAQ,CAClC,EAAK,EAAS,EAAI,EAAG,GACG,MAAf,EAAE,QAAQ,EAA2B,MAAM,CAArB,EAAE,QAAQ,CACzC,EAAK,EAAQ,EAAI,EAAG,GAEpB,EAAM,GAAG,CAAC,EAAE,MAAM,EAItB,GAAI,EAAM,IAAI,CAAG,EACf,CADkB,MACX,KAIT,GAAI,GAAM,IAAI,CACZ,EAAW,EAAQ,EAAG,MAAM,CAAE,EAAG,MAAM,CAAE,EAAA,EAC1B,GAAG,AAEP,AAAa,KAAK,GAAiB,OAAhB,EAAG,QAAQ,EAA6B,OAAhB,EAAG,QAAQ,AAAK,CAAI,EADxE,CAC2E,MADpE,KAOX,IAAK,IAAM,KAAM,EAAO,CACtB,GAAI,GAAM,CAAC,EAAU,EAAI,OAAO,GAAK,IAIjC,GAAM,CAAC,EAAU,AAJ0B,EAItB,OAAO,GAAK,GAHnC,OAG6C,AAHtC,KAOT,IAAK,IAAM,KAAK,EACd,EADmB,CACf,CAAC,EAAU,EAAI,OAAO,GAAI,GAC5B,OADsC,AAC/B,EAIX,OAAO,CACT,CAMA,IAAI,IAAe,IAChB,EAAD,AAAS,iBAAiB,IAC1B,EAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAG,EAAG,MAAM,CACrC,EADwC,EACzB,IAChB,EAAD,AAAS,iBAAiB,IAC1B,EAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAG,EAAG,MAAM,CAOzC,EAP4C,EAOvC,IAAM,KALP,GAAmD,IAAnC,EAAa,UAAU,CAAC,MAAM,EAC9B,MAAhB,EAAG,QAAQ,EAA2C,GAAG,CAAlC,EAAa,UAAU,CAAC,EAAE,GACnD,EAAe,EAAA,EAGD,GAAK,CAGnB,GAFA,EAAW,GAA2B,MAAf,EAAE,QAAQ,EAA2B,OAAf,EAAE,QAAQ,CACvD,EAAW,GAA2B,MAAf,EAAE,QAAQ,EAA2B,OAAf,EAAE,QAAQ,CACnD,GASF,CATM,EACF,GACE,EAAE,MAAM,CAAC,EADG,QACO,EAAI,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,EACjD,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EACrC,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EACrC,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EAAE,CACzC,GAAe,CAAA,EAGA,MAAf,EAAE,QAAQ,EAA2B,MAAM,CAArB,EAAE,QAAQ,EAElC,GAAI,CADJ,EAAS,EAAS,EAAI,EAAG,EAAA,IACV,GAAK,IAAW,EAC7B,EADiC,KAC1B,CACT,MACK,GAAoB,OAAhB,EAAG,QAAQ,EAAa,CAAC,EAAU,EAAG,MAAM,CAAE,OAAO,GAAI,GAClE,OAD4E,AACrE,CACT,CAEF,GAAI,EASF,EATM,EACF,GACE,EAAE,MAAM,CAAC,EADG,QACO,EAAI,EAAE,MAAM,CAAC,UAAU,CAAC,MAAM,EACjD,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EACrC,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EACrC,EAAE,MAAM,CAAC,KAAK,GAAK,EAAa,KAAK,EAAE,CACzC,GAAe,CAAA,EAGA,MAAf,EAAE,QAAQ,EAA2B,MAAM,CAArB,EAAE,QAAQ,EAElC,GAAI,CADJ,EAAQ,EAAQ,EAAI,EAAG,EAAA,IACT,GAAK,IAAU,EAC3B,EAD+B,KACxB,CACT,MACK,GAAoB,OAAhB,EAAG,QAAQ,EAAa,CAAC,EAAU,EAAG,MAAM,CAAE,OAAO,GAAI,GAClE,OAD4E,AACrE,CACT,CAEF,GAAI,CAAC,EAAE,QAAQ,EAAK,EAAD,EAAO,CAAA,CAAE,EAAkB,GAAG,CAAhB,EAC/B,MAAO,EAEX,OAKA,EAAI,IAAM,IAAY,CAAC,GAAmB,GAAG,CAAhB,MAIzB,IAAM,IAAY,CAAC,OAAM,CAAa,GAAG,CAOzC,IAAgB,IAIb,CACT,EAGM,EAAW,CAAC,EAAG,EARe,AAQZ,KACtB,GAAI,CAAC,EACH,CADM,MACC,EAET,IAAM,EAAO,EAAQ,EAAE,MAAM,CAAE,EAAE,MAAM,CAAE,GACzC,OAAO,EAAO,EAAI,EACd,EAAO,GACQ,CADJ,KACX,EAAE,QAAQ,EAA2B,OAAf,EAAE,QAAQ,CAAY,EAC5C,CACN,EAGM,EAAU,CAAC,EAAG,EAAG,KACrB,GAAI,CAAC,EACH,CADM,MACC,EAET,IAAM,EAAO,EAAQ,EAAE,MAAM,CAAE,EAAE,MAAM,CAAE,GACzC,OAAO,EAAO,EAAI,EACd,EAAO,GACQ,CADJ,KACX,EAAE,QAAQ,EAA2B,OAAf,EAAE,QAAQ,CAAY,EAC5C,CACN,EAEA,EAAO,OAAO,CA5MC,CAAC,CA4MC,CA5MI,EAAK,EAAU,CAAC,CAAC,IACpC,GAAI,IAAQ,EACV,GADe,IACR,EAGT,EAAM,IAAI,EAAM,EAAK,GACrB,EAAM,IAAI,EAAM,EAAK,GACrB,IAAI,GAAa,EAEjB,EAAO,IAAK,IAAM,KAAa,EAAI,GAAG,CAAE,CACtC,IAAK,IAAM,KAAa,EAAI,GAAG,CAAE,CAC/B,IAAM,EAAQ,EAAa,EAAW,EAAW,GAEjD,GADA,EAAa,GAAwB,OAAV,EACvB,EACF,KADS,IACA,CAEb,CAKA,GAAI,EACF,OAAO,CAEX,CACA,CAJkB,MAIX,CACT,gCCnEA,IAAM,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGN,EAAO,OAAO,CAAG,CACf,cACA,QACA,MACA,OACA,QACA,QACA,QACA,aACA,UACA,WACA,EACA,4BACA,OACA,QACA,EACA,KACA,QACA,MACA,MACA,MACA,MACA,SACA,aACA,QACA,YACA,gBACA,gBACA,gBACA,aACA,aACA,UACA,MACA,EACA,MACA,aACA,cAtCI,EAAA,CAAA,CAAA,OAuCJ,OAtCI,EAAA,CAAA,CAAA,cAuCJ,EACA,GAAI,EAAW,EAAE,CACjB,IAAK,EAAW,GAAG,CACnB,OAAQ,EAAW,CAAC,CACpB,oBAAqB,EAAU,mBAAmB,CAClD,cAAe,EAAU,aAAa,CACtC,mBAAoB,EAAY,kBAAkB,CAClD,oBAAqB,EAAY,mBAAmB,AACtD,mBCxFA,EAAO,OAAO,CAFR,AAEW,EAFX,CAAA,CAAA,OAEkB,SAAS,CAAC,QAAQ,OAAO,CAAE,6BCAnD,EAAO,OAAO,CAAG,AAFX,EAAA,CAAA,CAAA,OAEkB,SAAS,CAAC,QAAQ,OAAO,CAAE,6BCFnD,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAA2B,CAC/B,GAAM,CAAC,QAAS,QAAS,QAAQ,CACjC,IAAO,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAQ,CAC7D,UAAW,CAAC,QAAS,QAAS,QAAQ,AACxC,EAEM,EAAgB,CACpB,MAAO,aACP,MAAO,YACP,MAAO,WACT,EAEA,EAAO,OAAO,CAAG,SAAS,CAAS,CAAE,CAAG,EACtC,GAAI,CAAC,GAAa,CAAC,EAAK,OAExB,IAAM,EAAU,EAAI,iBAAiB,CACrC,GAAI,CAAC,EAAS,OAEd,IAAM,EAAoB,CAAwB,CAAC,EAAQ,CAE3D,GAAI,CAAC,EACH,MAAM,AAAI,MAAM,CAAC,IADK,cACa,EAAE,EAAQ,EAAE,CAAC,EAGlD,GAAI,CAAC,EAAkB,QAAQ,CAAC,GAC9B,MAAM,AAAI,GADgC,GAC1B,CAAC,qBAAqB,EAAE,EAAQ,2BAA2B,EAAE,EAAkB,IAAI,CAAC,MAAM,CAAC,CAAC,EAU9G,GAAI,EACF,OAAQ,GACR,IAAK,KACH,IAAM,EAAW,EAAI,KAHa,eAGO,CAAC,UAAU,CAC9C,EAAe,CAAa,CAAC,EAAU,CAE7C,GAAI,IAAa,EACf,MAAM,AAAI,MADmB,AACb,CAAC,iBAAiB,EAAE,EAAU,kBAAkB,EAAE,EAAa,EAAE,CAAC,EAEpF,KAEF,KAAK,UACH,GAAI,EAA+B,CACjC,IAAM,EAAS,SAAS,EAAU,KAAK,CAAC,CAAC,GAAI,IACvC,eAAE,CAAa,mBAAE,CAAiB,YAAE,CAAU,CAAE,CAAG,EAAI,oBAAoB,CAEjF,GAAI,IAAkB,CAAC,GAAG,EAAE,EAAA,CAAQ,EAAI,IAAsB,EAC5D,MAAM,AAAI,MAAM,CAAC,AAD0D,6FACmC,EAAE,EAAU,CAAC,CAAC,EAG9H,QAAmB,IAAf,GAA4B,EAAa,GAAU,EACrD,CADwD,KAClD,AAAI,MAAM,CAAC,yGAAyG,EAAE,EAAU,CAAC,CAAC,CAE5I,CAEF,CAEJ,kBC/DA,EAAO,OAAO,CAFV,AAEa,EAFb,CAAA,CAAA,OAEoB,SAAS,CAAC,QAAQ,OAAO,CAAE,uCCFnD,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,WAAC,CAAS,iBAAE,CAAe,iBAAE,CAAe,CAAC,CAAA,EAAA,CAAA,CAAA,OAE7C,EAAe,CAAC,QAAS,QAAS,QAAQ,CAC1C,EAAc,CAAC,QAAS,QAAS,QAAQ,CACzC,EAAe,CAAC,QAAS,QAAS,QAAQ,CAC1C,EAAU,CAAC,QAAS,QAAS,QAAQ,CAEvC,IACF,EAAa,MAAM,CAAC,CADJ,CACiB,MAAM,CAAE,EAAG,QAAS,QAAS,SAC9D,EAAa,MAAM,CAAC,EAAa,MAAM,CAAE,EAAG,QAAS,QAAS,UAGhE,EAAO,OAAO,CAAG,SAAU,CAAS,CAAE,CAAiB,CAAE,CAAO,CAAE,CAAQ,MAapE,EAuCA,EAaA,EAzCJ,GAvBwB,YAAnB,EAAkC,KAA3B,GAA4B,IACtC,EAAW,EACX,EAAU,AAFsC,CAErC,GAGT,AAAC,IACH,EAAU,EAAC,CADC,CAKd,EAAU,OAAO,MAAM,CAAC,CAAC,EAAG,GAK1B,EADE,GAGK,EAFA,KADK,EAGI,CAAG,CAAE,CAAI,EACvB,GAAI,EAAK,MAAM,EACf,OAAO,CACT,EAGE,EAAQ,cAAc,EAAsC,UAAU,AAA5C,OAAO,EAAQ,cAAc,CACzD,OAAO,EAAK,IAAI,EAAkB,oCAGpC,QAAsB,IAAlB,EAAQ,KAAK,EAAkB,CAA0B,AAAzB,iBAAO,EAAQ,KAAK,EAA0C,KAAzB,EAAQ,KAAK,CAAC,IAAI,EAAO,CAAE,CAClG,EADqG,KAC9F,EAAK,IAAI,EAAkB,qCAGpC,GAAI,KAA2C,MAAnC,8BAA8B,EAAoE,WAAlD,AAA6D,OAAtD,EAAQ,8BAA8B,CACvG,OAAO,EAAK,IAAI,EAAkB,qDAGpC,IAAM,EAAiB,EAAQ,cAAc,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,GAAK,KAEzE,GAAI,CAAC,EACH,OAAO,EADM,AACD,IAAI,EAAkB,yBAGpC,GAAyB,UAArB,AAA+B,OAAxB,EACT,OAAO,EAAK,IAAI,EAAkB,yBAGpC,IAAM,EAAQ,EAAU,KAAK,CAAC,KAE9B,GAAqB,GAAE,CAAnB,EAAM,MAAM,CACd,OAAO,EAAK,IAAI,EAAkB,kBAKpC,GAAI,CACF,EAAe,EAAO,EAAW,CAAE,UAAU,CAAK,EACpD,CAAE,MAAM,EAAK,CACX,OAAO,EAAK,EACd,CAEA,GAAI,CAAC,EACH,OAAO,EAAK,GADK,CACD,EAAkB,kBAGpC,IAAM,EAAS,EAAa,MAAM,CAGlC,GAAG,AAA6B,mBAAtB,EAAkC,CAC1C,GAAG,CAAC,EACF,OAAO,CADK,CACA,IAAI,EAAkB,yFAGpC,EAAY,CACd,MAEE,CADG,CACS,SAAS,CAAM,CAAE,CAAc,EACzC,OAAO,EAAe,KAAM,EAC9B,EAGF,OAAO,EAAU,EAAQ,SAAS,CAAG,CAAE,CAAiB,MA6DlD,EA5DJ,GAAG,EACD,GADM,IACC,EAAK,IAAI,EAAkB,2CAA6C,EAAI,OAAO,GAG5F,IAAM,EAAmC,KAApB,CAAK,CAAC,EAAE,CAAC,IAAI,GAElC,GAAI,CAAC,GAAgB,EACnB,OAAO,EAAK,IAAI,EAAkB,EADG,4BAIvC,GAAI,GAAgB,CAAC,EACnB,OAAO,EAAK,IAAI,EAAkB,EADI,wCAIxC,GAAI,CAAC,GAAgB,CAAC,EAAQ,UAAU,CACtC,CADwC,MACjC,EAAK,IAAI,EAAkB,oEAGpC,GAAyB,MAArB,GAA6B,CAAC,CAAC,aAA6B,CAAA,CAAS,CACvE,EAD0E,CACtE,CACF,EAAoB,EAAgB,EACtC,CAAE,MAAO,EAAG,CACV,GAAI,CACF,EAAoB,EAA6C,UAA7B,OAAO,EAAiC,OAAO,IAAI,CAAC,GAAqB,EAC/G,CAAE,MAAO,EAAG,CACV,OAAO,EAAK,IAAI,EAAkB,+CACpC,CACF,CAeF,GAZK,EAAQ,UAAU,EAAE,CACQ,UAAU,CAArC,EAAkB,IAAI,CACxB,EAAQ,UAAU,CAAG,EACZ,CAAC,MAAO,UAAU,CAAC,QAAQ,CAAC,EAAkB,iBAAiB,EACxE,CAD2E,CACnE,UAAU,CAAG,EAC4B,MAAM,CAA9C,EAAkB,iBAAiB,CAC5C,EAAQ,UAAU,CAAG,EAErB,EAAQ,UAAU,CAAG,GAImC,CAAC,GAAG,CAA5D,EAAQ,UAAU,CAAC,OAAO,CAAC,EAAa,MAAM,CAAC,GAAG,EACpD,OAAO,EAAK,IAAI,EAAkB,sBAGpC,GAAI,EAAO,GAAG,CAAC,UAAU,CAAC,OAAoC,UAAU,CAArC,EAAkB,IAAI,CACvD,OAAO,EAAK,IAAI,EAAmB,CAAC,qDAAqD,EAAE,EAAO,GAAG,CAAA,CAAE,GAClG,GAAI,gBAAgB,IAAI,CAAC,EAAO,GAAG,GAAgC,UAAU,CAArC,EAAkB,IAAI,CACnE,OAAO,EAAK,IAAI,EAAmB,CAAC,uDAAuD,EAAE,EAAO,GAAG,CAAA,CAAE,GAG3G,GAAI,CAAC,EAAQ,8BAA8B,CACzC,CAD2C,EACvC,CACF,EAAsB,EAAO,GAAG,CAAE,EACpC,CAAE,MAAO,EAAG,CACV,OAAO,EAAK,EACd,CAKF,GAAI,CACF,EAAQ,EAAI,MAAM,CAAC,EAAW,EAAa,MAAM,CAAC,GAAG,CAAE,EACzD,CAAE,MAAO,EAAG,CACV,OAAO,EAAK,EACd,CAEA,GAAI,CAAC,EACH,KADU,EACH,EAAK,IAAI,EAAkB,sBAGpC,IAAM,EAAU,EAAa,OAAO,CAEpC,GAAI,KAAuB,IAAhB,EAAQ,GAAG,EAAoB,CAAC,EAAQ,eAAe,CAAE,CAClE,GAA2B,UAAU,AAAjC,OAAO,EAAQ,GAAG,CACpB,OAAO,EAAK,IAAI,EAAkB,sBAEpC,GAAI,EAAQ,GAAG,CAAG,GAAkB,EAAQ,YAAT,EAAuB,GAAI,CAAC,CAC7D,EADgE,KACzD,EAAK,IAAI,EAAe,iBAAkB,IAAI,KAAK,AAAc,MAAN,GAAG,GAEzE,CAEA,GAAI,KAAuB,IAAhB,EAAQ,GAAG,EAAoB,CAAC,EAAQ,gBAAgB,CAAE,CACnE,GAA2B,UAAvB,AAAiC,OAA1B,EAAQ,GAAG,CACpB,OAAO,EAAK,IAAI,EAAkB,sBAEpC,GAAI,GAAkB,EAAQ,GAAG,EAAI,CAAD,CAAS,cAAc,GAAI,CAAC,CAC9D,EADiE,KAC1D,EAAK,IAAI,EAAkB,cAAe,IAAI,KAAmB,IAAd,EAAQ,GAAG,GAEzE,CAEA,GAAI,EAAQ,QAAQ,CAAE,CACpB,IAAM,EAAY,MAAM,OAAO,CAAC,EAAQ,QAAQ,EAAI,EAAQ,QAAQ,CAAG,CAAC,EAAQ,QAAQ,CAAC,CASzF,GAAI,CANU,AAMT,CARU,MAAM,AAQT,OARgB,CAAC,EAAQ,GAAG,EAAI,EAAQ,GAAG,CAAG,CAAC,EAAQ,GAAG,CAAC,EAElD,IAAI,CAAC,SAAU,CAAc,EAChD,OAAO,EAAU,IAAI,CAAC,SAAU,CAAQ,EACtC,OAAO,aAAoB,OAAS,EAAS,IAAI,CAAC,GAAkB,IAAa,CACnF,EACF,GAGE,OAAO,EAAK,IAAI,EAAkB,mCAAqC,EAAU,IAAI,CAAC,SAE1F,CAEA,GAAI,EAAQ,MAAM,EAAE,CAEiB,UAA1B,OAAO,EAAQ,MAAM,EAAiB,EAAQ,GAAG,GAAK,EAAQ,MAAM,EACpE,MAAM,OAAO,CAAC,EAAQ,MAAM,GAAK,AAAwC,OAAhC,MAAM,CAAC,OAAO,CAAC,EAAQ,GAAG,CAAO,EAGjF,OAAO,EAAK,IAAI,EAAkB,iCAAmC,EAAQ,MAAM,GAIvF,GAAI,EAAQ,OAAO,EAAE,AACf,EAAQ,GAAG,GAAK,EAAQ,OAAO,CACjC,CADmC,MAC5B,EAAK,IAAI,EAAkB,kCAAoC,EAAQ,OAAO,GAIzF,GAAI,EAAQ,KAAK,EAAE,AACb,EAAQ,GAAG,GAAK,EAAQ,KAAK,CAC/B,CADiC,MAC1B,EAAK,IAAI,EAAkB,gCAAkC,EAAQ,KAAK,GAIrF,GAAI,EAAQ,KAAK,EAAE,AACb,EAAQ,KAAK,GAAK,EAAQ,KAAK,CACjC,CADmC,MAC5B,EAAK,IAAI,EAAkB,gCAAkC,EAAQ,KAAK,GAIrF,GAAI,EAAQ,MAAM,CAAE,CAClB,GAA2B,UAAvB,AAAiC,OAA1B,EAAQ,GAAG,CACpB,OAAO,EAAK,IAAI,EAAkB,0CAGpC,IAAM,EAAkB,EAAS,EAAQ,MAAM,CAAE,EAAQ,GAAG,EAC5D,GAAI,KAA2B,IAApB,EACT,OAD0C,AACnC,EAAK,IAAI,EAAkB,iGAEpC,GAAI,GAAkB,GAAmB,EAAQ,aAAT,CAAuB,GAAI,CAAC,CAClE,EADqE,KAC9D,EAAK,IAAI,EAAkB,kBAAmB,IAAI,KAAuB,IAAlB,IAElE,OAEA,CAAyB,IAArB,EAAQ,AAAmB,QAAX,CAGX,EAAK,KAAM,CAChB,OAAQ,EACR,QAAS,EACT,UALgB,CAKL,CALkB,SAAS,AAMxC,GAGK,EAAK,KAAM,EACpB,EACF,mBC5PA,IAuJiB,EAAM,EAvJnB,AAuJiB,EAvJN,EAAI,EAGf,CAFA,AAsJ4B,CApJtB,EAAI,EAUV,EAAS,aAGT,EAAa,qBAGb,EAAa,aAGb,EAAY,cAGZ,EAAW,mBAGX,EAAe,SA4EnB,SAAS,EAAU,CAAK,EACtB,OAAO,GAAU,CACnB,CAoDA,IAAI,EAAc,OAAO,SAAS,CAG9B,EAAiB,EAAY,cAAc,CAO3C,EAAiB,EAAY,QAAQ,CAGrC,EAAuB,EAAY,oBAAoB,CAGvD,KAAqB,OAAO,CAAf,GAAmB,GAAE,OAtB7B,SAAS,CAAG,EACjB,OAAO,EAAK,EAAU,GACxB,GAqBE,EAAY,KAAK,GAAG,CAwKpB,EAAU,MAAM,OAAO,CA2B3B,SAAS,EAAY,CAAK,MAmFR,EAjCE,EAGd,CA8BiB,CAlFrB,CAiDuB,MAjDhB,AAAS,QAAQ,CAmFD,UAAhB,OAAO,EAnFmB,EAAM,MAAM,GAoF3C,EApFgD,AAoFxC,CApFyC,AAoFxC,GAAK,EAAQ,GAAK,GAAK,SAAS,YAhc/B,AAiaL,OAAO,cAhaZ,GA+ZQ,IApDoD,GAoDlC,EAAT,AAAwB,IAAI,CAAC,GAAS,KA/Z9C,8BACT,AA+ZuB,CApD3B,CA+GA,KA3DkC,IA2DzB,EAAS,CAAK,EACrB,IAAI,EAAO,OAAO,EAClB,MAAO,CAAC,CAAC,IAAkB,KAAT,KAAC,GAA4B,YAAR,CAAQ,CAAU,AAC3D,CA0BA,SAAS,EAAa,CAAK,EACzB,MAAO,CAAC,CAAC,GAAyB,UAAhB,OAAO,CAC3B,CA4NA,EAAO,OAAO,CAncd,EAmciB,OAncR,AAAS,CAAU,CAAE,CAAK,CAAE,CAAS,CAAE,CAAK,EACnD,EAAa,EAAY,GAAc,EAAa,AA8btD,SAAS,AAAO,CAAM,EACpB,OAAO,EAprBT,AAmGS,OAilBS,EAprBT,AAmGS,AAnGA,CAAK,CAAE,CAAQ,EAK/B,IAJA,IAAI,EAAQ,CAAC,EACT,EAAS,EAAQ,EAAM,MAAM,CAAG,EAChC,EAAS,MAAM,GAEZ,EAAE,EAAQ,GACf,CAAM,CAAC,EAAM,AADU,CACP,EAAS,CAAK,CAAC,EAAM,CAAE,EAAO,GAEhD,OAAO,CACT,EA6oBS,KApgBT,AAogB+B,OAAV,EApgBZ,AAAc,CAAK,CAAE,CAAS,EAGrC,AAigB2C,IAnYxB,IAlFJ,CAkFS,GA9HpB,CA4CgB,CA5CN,EAAQ,IAoNf,AApFA,EAmFkB,IAnNO,AAAY,CAmNd,GACA,EAAY,CAAtB,GApFe,EAAV,AAAyB,IAAI,CAAC,EAAO,YAC3D,CAAD,AAAE,EAAqB,IAAI,CAAC,EAAO,WAvTzB,sBACV,AAsTgD,EAAe,IAAI,CAAC,EAAU,CAAO,CAhInF,AAxEN,MAwMkF,GAxMzE,AAAU,CAAC,CAAE,CAAQ,EAI5B,IAHA,IAAI,EAAQ,CAAC,EACT,EAAS,MAAM,GAEZ,EAAE,EAAQ,EAAG,CAClB,CAAM,CAAC,EAAM,CAAG,EAAS,GAE3B,OAAO,CACT,EAgEgB,EAAM,MAAM,CAAE,QACxB,EAAE,CAEF,EAAS,EAAO,MAAM,CACtB,EAAc,CAAC,CAAC,EAEpB,IAAK,IAAI,KAAO,EACd,CAAI,AAAc,CAAb,CAA4B,CADZ,GACgB,CAAC,EAAO,IAAI,AAC7C,CAAC,CAAC,IAAuB,UAAP,CAAD,CAAoB,IAAQ,EAqC5C,CADP,AACQ,CAAC,CADA,AAAU,OADG,AACI,EAExB,AAtCsD,GApMnC,CAuOO,gBACiB,CAvO3C,AAuO2C,IAE1C,AAAgB,iBAAT,GAAqB,EAAS,IAAI,CAAC,EAAA,CAAM,EAChD,EAAQ,CAAC,GAAK,EAAQ,GAAK,GAAK,EAAQ,EAvCa,CAAO,CAAC,EAC5D,CAD+D,CACxD,IAAI,CAAC,EACd,CAEF,OAAO,CACT,KASA,AA0euD,SAAS,AA1evD,AAAS,CAAM,EACtB,IAAI,CAmCO,AAnCN,GAAY,IAmCG,EAAM,GAnCA,QAmCW,CAG9B,CAFH,IAAwB,KAEX,OAFJ,OAAO,GAAsB,EAAK,SAAS,EAAK,CAAA,EAnC3D,OAAO,EAAW,GAEpB,IA+BmB,EACf,EAhCA,CA+BoB,CA/BX,EAAE,CACf,IAAK,IAAI,KAAO,OAAO,GACjB,EAAe,GADW,CACP,CAAC,EAAQ,IAAe,eAAe,AAAtB,GACtC,EAAO,IAAI,CAAC,GAGhB,OAAO,CACT,EA6f0C,GAjlBjB,SAAS,CAAG,EACjC,OAglByB,AAhlBlB,CAAM,CAAC,EAAI,AACpB,GA+kBmD,CAAhB,CACrC,AADuD,EA/bM,GAC3D,EAAa,GAAa,CAAC,GAkVvB,EAAY,CADZ,EAjVgC,AA2SpC,CADgB,EA1S8B,CA2S1C,CAAC,CADgB,AAKjB,CADJ,AAmCa,EAnCL,AAgEV,GAnEc,IAsCU,EA6Bf,AAAS,CAAK,EACrB,GAAI,AAAgB,UAAU,OAAnB,EACT,OAAO,EAET,GAnGuB,CAmGnB,SAnGG,OADS,AACF,EAmGD,GApGQ,CAElB,EAAa,EAkGK,EAroBP,mBAmiBY,EAAe,IAAI,CAAC,GAmG5C,OAnGsD,AAmG/C,EAET,GAAI,EAAS,GAAQ,CACnB,MAAI,EAAgC,YAAxB,OAAO,EAAM,OAAO,CAAiB,EAAM,OAAO,GAAK,EACnE,EAAQ,EAAS,GAAU,EAAQ,GAAM,CAC3C,CACA,GAAoB,UAAhB,AAA0B,OAAnB,EACT,OAAiB,IAAV,EAAc,EAAQ,CAAC,EAEhC,EAAQ,EAAM,OAAO,CAAC,EAAQ,IAC9B,IAAI,EAAW,EAAW,IAAI,CAAC,GAC/B,OAAQ,GAAY,EAAU,IAAI,CAAC,GAC/B,EAAa,EAAM,KAAK,CAAC,GAAI,EAAW,EAAI,GAC3C,EAAW,IAAI,CAAC,GAAS,EAAM,CAAC,CACvC,EAnFmB,EAAA,IACH,GAAY,IAAU,CAAC,EAE5B,CADK,EAAQ,EAAI,CAAC,CACX,CAF+B,CAChB,EA3kBf,sBA8kBT,GAAU,EAAQ,EAAQ,EAPd,IAAV,EAAc,EAAQ,GAsCN,EAElB,GAAW,EAAU,EAAY,EAAS,EAAY,EAAU,GApVZ,EAE3D,UAsPgB,EAtPZ,EAAS,CAsPQ,CAtPG,MAAM,CAI9B,OAHI,EAAY,GAAG,CACjB,EAAY,EAAU,EAAS,EAAW,EAAA,EAqPrC,AAAgB,AAnPhB,OAmPO,YAnPE,IAoPb,CAAC,EAAQ,IAAU,EAAa,IA9gBrB,mBA8gB+B,AA7gB3C,EA6gB0D,IAAI,CAAC,GAnP5D,GAAa,GAAU,CAmP+C,CAnPpC,OAAO,CAAC,EAAO,GAAa,CAAC,EAC/D,CAAC,CAAC,GAnNT,AAmNmB,SAnNV,AAAY,CAAK,CAAE,CAAK,CAAE,CAAS,EAC1C,GAAI,GAAU,EACL,CAnBT,IAkBqB,IAtB6B,EAC9C,EAAS,EAAM,GADwC,GAClC,CACrB,EAAQ,AAqB6B,EArBA,CAAC,CAAC,CAEnC,EAAY,IAAU,CAFN,CAAC,AAEO,EAAQ,GACtC,GAkB4B,AAlBxB,CAD2C,CACjC,CAAK,CAHgB,AAGf,EAAM,CAAE,EAkBP,GAjBnB,EADiC,KAC1B,EAGX,CAJ6C,MAItC,CAAC,CAciC,CAKzC,IAHA,IAAI,EAAQ,EAAY,EACpB,EAAS,EAAM,MAAM,CAElB,EAAE,EAAQ,GACf,GAAI,CAAK,AADc,CACb,EAAM,GAAK,EACnB,KAD0B,EACnB,EAGX,OAAO,CAAC,CACV,EAsM+B,EAAY,EAAO,GAAa,CAAC,CAChE,mBC7RA,IAAI,EANc,AAMG,OANI,SAAS,CAMD,QAAQ,CAkDzC,EAAO,OAAO,CAhCd,EAgCiB,OAhCR,AAAU,CAAK,QACtB,OAAiB,IAAV,IAA4B,IAAV,GA4BlB,AA3BJ,CA2BK,CAAC,CADW,EA1BJ,GA0BS,CACS,UAAhB,OAAO,GAxDb,oBA6Bc,EAAe,IAAI,CAAC,EAChD,QAD0D,WC7B1D,IAAI,EAAW,EAAI,EAEf,CADA,CACM,EAAI,EAMV,EAAS,aAGT,EAAa,qBAGb,EAAa,aAGb,EAAY,cAGZ,EAAe,SAUf,EAPc,AAOG,OAPI,SAAS,CAOD,QAAQ,CAyDzC,SAAS,EAAS,CAAK,EACrB,IAAI,EAAO,OAAO,EAClB,MAAO,CAAC,CAAC,IAAkB,KAAT,KAAC,GAA4B,YAAR,CAAQ,CAAU,AAC3D,CAoKA,EAAO,OAAO,CApMd,EAoMiB,OApME,AAAV,CAAe,EAkJtB,IAvCgB,EAuCZ,GAvCiB,CA1GrB,KAiJa,CAjJU,QAiJD,EAjJf,OAAO,GAAqB,IAkJ/B,EAAY,GAlJ4B,AA2G5C,GA3GsD,CA2GlD,CAAC,CAID,CADJ,EAgEF,AAhEU,GAHI,MAmEL,AAAS,CAAK,EACrB,GAAoB,UAAhB,AAA0B,OAAnB,EACT,OAAO,EAET,GAnGuB,CAmGnB,SAnGG,OAAO,EAmGD,IAlGV,GAtB+B,CAwHb,SAxHH,OAsBF,AAtBS,GAhHX,mBAsIY,EAAe,IAAI,CAAC,GAmG5C,OAAO,AAnG+C,EAqGxD,GAAI,EAAS,GAAQ,CACnB,IAxGc,EAwGV,EAAgC,CAxGjB,WAwGP,OAAO,EAAM,OAAO,CAAiB,EAAM,OAAO,GAAK,EACnE,EAAQ,EAAS,GAAU,EAAQ,GAAM,CAC3C,CACA,GAAoB,UAAhB,AAA0B,OAAnB,EACT,OAAiB,IAAV,EAAc,EAAQ,CAAC,EAEhC,EAAQ,EAAM,OAAO,CAAC,EAAQ,IAC9B,IAAI,EAAW,EAAW,IAAI,CAAC,GAC/B,OAAQ,GAAY,EAAU,IAAI,CAAC,GAC/B,EAAa,EAAM,KAAK,CAAC,GAAI,EAAW,EAAI,GAC3C,EAAW,IAAI,CAAC,GAAS,EAAM,CAAC,CACvC,EAnFmB,EAAA,IACH,GAAY,IAAU,CAAC,EAE5B,CADK,EAAQ,EAAI,CAAC,CACX,CAF+B,CAChB,EA1Kf,sBA6KT,GAAU,EAAQ,EAAQ,EAPd,IAAV,EAAc,EAAQ,GAsCN,EAElB,GAAW,EAAU,EAAY,EAAS,EAAY,EAAU,EAnJzE,kBCnDA,IAAI,EANc,AAMG,OANI,SAAS,CAMD,QAAQ,AA2DzC,GAAO,OAAO,CALd,EAKiB,OALC,AAAT,CAAc,EACrB,MAAO,AAAgB,iBAAT,GA7BP,AA8BJ,CA9BK,CAAC,GAAyB,UAAhB,OA8BF,AA9BS,GAnCX,mBAiEY,EAAe,IAAI,CAAC,EAChD,QAD0D,WC7B1D,IAAI,IACA,EAAc,OAAO,SAAS,CAG9B,EAJY,AAIG,SAJM,SAAS,CAIL,QAAQ,CAGjC,EAAiB,EAAY,cAAc,CAG3C,EAAmB,EAAa,IAAI,CAAC,QAOrC,EAAiB,EAAY,QAAQ,CAGrC,GA3Ba,EA2BU,EA3BN,KA2Ba,GAAf,WAA6B,CA3BzB,EA2B2B,OA3BlB,AACvB,SAAS,CAAG,EACjB,OAAO,EAAK,EAAU,GACxB,GAgGF,EAAO,OAAO,CAdd,EAciB,OAdR,AAAc,CAAK,EAC1B,GAAI,CAAC,CAhCE,CAAC,CAAC,CAAyB,UAAhB,OAAO,AAgCP,CAhCgB,GAnFpB,mBAoHV,EAAe,IAAI,CAAC,IAAuB,AA3GjD,MA2GoC,GA3Gd,AAAb,CAAkB,EAGzB,IAAI,GAAS,EACb,GAAa,MAAT,GAA0C,YAAzB,AAAqC,OAA9B,EAAM,QAAQ,CACxC,GAAI,CACF,EAAS,CAAC,CAAC,CAAC,EAAQ,EAAA,CAAE,AACxB,CAAE,MAAO,EAAG,CAAC,CAEf,OAAO,CACT,EAiG8D,GAC1D,KADkE,EAC3D,EAET,IAAI,EAAQ,EAAa,GACzB,GAAI,AAAU,MAAM,GAClB,OAAO,EAET,IAAI,EAAO,EAAe,IAAI,CAAC,EAAO,gBAAkB,EAAM,WAAW,CACzE,MAAQ,AAAe,mBAAR,GACb,aAAgB,GAAQ,EAAa,IAAI,CAAC,IAAS,CACvD,mBCrHA,IAAI,EAAiB,AANH,OAAO,SAAS,CAMD,QAAQ,CAyBrC,EAAU,MAAM,OAAO,CAkD3B,EAAO,OAAO,CALd,EAKiB,OALR,AAAS,CAAK,QACrB,MAAO,AAAgB,iBAAT,GACX,CAAC,EAAQ,IArBL,CAAC,CAAC,CADW,EAsBe,CAAb,EAtBG,CACS,UAAhB,OAAO,GA5DX,mBAiF+B,EAAe,IAAI,CAAC,EACnE,QAD6E,WC9E7E,IAAI,EAAW,EAAI,EAEf,CADA,CACM,EAAI,EAMV,EAAS,aAGT,EAAa,qBAGb,EAAa,aAGb,EAAY,cAGZ,EAAe,SAUf,EAAiB,AAPH,OAAO,SAAS,CAOD,QAAQ,CAmFzC,SAAS,EAAS,CAAK,EACrB,IAAI,EAAO,OAAO,EAClB,MAAO,CAAC,CAAC,IAAU,AAAQ,KAAT,QAA6B,YAAR,CAAQ,CAAU,AAC3D,CAoKA,EAAO,OAAO,CApMd,EAoMiB,OApMR,AAAK,CAAI,MA2GA,EAuCZ,EACA,CAxCiB,CA7IjB,EADU,CAAC,CAoCD,CAiJD,CArLI,EAoCA,EApCI,AAErB,GAAmB,CAmLG,WAnLS,AAA3B,OAAO,EACT,MAAM,AAAI,UAAU,AAvDF,uBA0DpB,SAgLgB,GAvChB,GA1Ic,CA0IV,CAAC,CAID,CADJ,EAAQ,AAgEV,GAnEc,MAmEL,AAAS,CAAK,EACrB,GAAI,AAAgB,UAAU,OAAnB,EACT,OAAO,EAET,GAnGuB,CAmGnB,SAnGG,OAAO,EAmGD,IAlGV,GAtB+B,CAwHb,SAxHH,OAAO,AAsBT,GAhKF,mBAgKY,EAAe,IAAI,CAAC,GAmG5C,OAnGsD,AAmG/C,EAET,GAAI,EAAS,GAAQ,CACnB,IAxGc,EAwGV,EAAQ,AAAwB,CAxGjB,kBAwGA,EAAM,OAAO,CAAiB,EAAM,OAAO,GAAK,EACnE,EAAQ,EAAS,GAAU,EAAQ,GAAM,CAC3C,CACA,GAAoB,UAAhB,AAA0B,OAAnB,EACT,OAAiB,IAAV,EAAc,EAAQ,CAAC,EAEhC,EAAQ,EAAM,OAAO,CAAC,EAAQ,IAC9B,IAAI,EAAW,EAAW,IAAI,CAAC,GAC/B,OAAQ,GAAY,EAAU,IAAI,CAAC,GAC/B,EAAa,EAAM,KAAK,CAAC,GAAI,EAAW,EAAI,GAC3C,EAAW,IAAI,CAAC,GAAS,EAAM,CAAC,CACvC,EAnFmB,EAAA,IACH,GAAY,IAAU,CAAC,EAE5B,CADK,EAAQ,EAAI,CAAC,CACX,CAF+B,CAChB,EApMf,sBAuMT,GAAU,EAAQ,EAAQ,EAPd,IAAV,EAAc,EAAQ,GAsCN,EAjLzB,EAmLO,EAnLH,CAmLc,EAAU,EAAY,EAAS,EAAY,EAAU,EAlLhE,WAOL,MANI,EAAE,EAAI,GAAG,CACX,EAAS,EAAK,KAAK,CAAC,IAAI,CAAE,UAAA,EAExB,GAAK,GAAG,CACV,OAAO,CAAA,EAEF,CACT,CAuBF,mBCnGA,IAAM,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,WAAE,CAAS,iBAAE,CAAe,kBAAE,CAAgB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEhD,EAAiB,CAAC,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,OAAO,CAC5G,GACF,EAAe,MAAM,CAAC,EADN,AACS,EAAG,QAAS,QAAS,SAGhD,IAAM,EAAsB,CAC1B,UAAW,CAAE,QAAS,SAAS,CAAK,EAAI,OAAO,EAAU,IAAW,EAAS,IAAU,CAAQ,EAAG,QAAS,6EAA8E,EACzL,UAAW,CAAE,QAAS,SAAS,CAAK,EAAI,OAAO,EAAU,IAAW,EAAS,IAAU,CAAQ,EAAG,QAAS,6EAA8E,EACzL,SAAU,CAAE,QAAS,SAAS,CAAK,EAAI,OAAO,EAAS,IAAU,MAAM,OAAO,CAAC,EAAQ,EAAG,QAAS,sCAAuC,EAC1I,UAAW,CAAE,QAAS,EAAS,IAAI,CAAC,KAAM,GAAiB,QAAS,+CAAgD,EACpH,OAAQ,CAAE,QAAS,EAAe,QAAS,4BAA6B,EACxE,SAAU,CAAE,QAAS,EAAU,QAAS,6BAA8B,EACtE,OAAQ,CAAE,QAAS,EAAU,QAAS,2BAA4B,EAClE,QAAS,CAAE,QAAS,EAAU,QAAS,4BAA6B,EACpE,MAAO,CAAE,QAAS,EAAU,QAAS,0BAA2B,EAChE,YAAa,CAAE,QAAS,EAAW,QAAS,iCAAkC,EAC9E,MAAO,CAAE,QAAS,EAAU,QAAS,0BAA2B,EAChE,cAAe,CAAE,QAAS,EAAW,QAAS,mCAAoC,EAClF,sBAAuB,CAAE,QAAS,EAAW,QAAS,2CAA2C,EACjG,+BAAgC,CAAE,QAAS,EAAW,QAAS,oDAAoD,CACrH,EAEM,EAA2B,CAC/B,IAAK,CAAE,QAAS,EAAU,QAAS,qCAAsC,EACzE,IAAK,CAAE,QAAS,EAAU,QAAS,qCAAsC,EACzE,IAAK,CAAE,QAAS,EAAU,QAAS,qCAAsC,CAC3E,EAEA,SAAS,EAAS,CAAM,CAAE,CAAY,CAAE,CAAM,CAAE,CAAa,EAC3D,GAAI,CAAC,EAAc,GACjB,MAD0B,AACpB,AAAI,MAAM,aAAe,EAAgB,2BAEjD,OAAO,IAAI,CAAC,GACT,OAAO,CAAC,SAAS,CAAG,EACnB,IAAM,EAAY,CAAM,CAAC,EAAI,CAC7B,GAAI,CAAC,EAAW,CACd,GAAI,CAAC,EACH,MAAM,AAAI,MADO,AACD,IAAM,EAAM,wBAA0B,EAAgB,KAExE,MACF,CACA,GAAI,CAAC,EAAU,OAAO,CAAC,CAAM,CAAC,EAAI,EAChC,CADmC,KAC7B,AAAI,MAAM,EAAU,OAAO,CAErC,EACJ,CAUA,IAAM,EAAqB,CACzB,SAAY,MACZ,OAAU,MACV,QAAW,MACX,MAAS,KACX,EAEM,EAAsB,CAC1B,YACA,YACA,cACA,WACA,SACA,UACA,QACD,CAED,EAAO,OAAO,CAAG,SAAU,CAAO,CAAE,CAAkB,CAAE,CAAO,CAAE,CAAQ,UAChD,YAAnB,AAA+B,OAAxB,GACT,EAAW,EACX,EAAU,CAAC,GAEX,EAAU,GAAW,CAAC,EAGxB,IAAM,EAAkB,AAAmB,iBAAZ,GACT,CAAC,OAAO,QAAQ,CAAC,GAEjC,EAAS,OAAO,MAAM,CAAC,CAC3B,IAAK,EAAQ,SAAS,EAAI,QAC1B,IAAK,EAAkB,WAAQ,EAC/B,IAAK,EAAQ,KAAK,AACpB,EAAG,EAAQ,MAAM,EAEjB,SAAS,EAAQ,CAAG,EAClB,GAAI,EACF,OAAO,CADK,CACI,EAElB,OAAM,CACR,CAEA,GAAI,CAAC,GAA4C,QAAQ,CAA9B,EAAQ,SAAS,CAC1C,OAAO,EAAQ,AAAI,MAAM,yCAG3B,GAA0B,MAAtB,GAA8B,CAAC,CAAC,aAA8B,CAAA,CAAS,CACzE,EAD4E,CACxE,CACF,EAAqB,EAAiB,EACxC,CAAE,MAAO,EAAG,CACV,GAAI,CACF,EAAqB,EAA8C,UAA9B,OAAO,EAAkC,OAAO,IAAI,CAAC,GAAsB,EAClH,CAAE,MAAO,EAAG,CACV,OAAO,EAAQ,AAAI,MAAM,gDAC3B,CACF,CAGF,GAAI,EAAO,GAAG,CAAC,UAAU,CAAC,OAAqC,UAAU,CAAtC,EAAmB,IAAI,CACxD,OAAO,EAAQ,AAAI,MAAO,CAAC,sDAAsD,EAAE,EAAO,GAAG,CAAA,CAAE,GAC1F,GAAI,gBAAgB,IAAI,CAAC,EAAO,GAAG,EAAG,CAC3C,GAAgC,WAAW,CAAvC,EAAmB,IAAI,CACzB,OAAO,EAAQ,AAAI,MAAO,CAAC,wDAAwD,EAAE,EAAO,GAAG,CAAA,CAAE,GAEnG,GAAI,CAAC,EAAQ,qBAAqB,EAChC,CAAC,EAAO,GAAG,CAAC,UAAU,CAAC,OACvB,KAA4C,MAAzB,OAAsC,aAAlB,EACvC,EAAmB,oBAAoB,CAAC,aAAa,CAAG,KAD0D,AAElH,CAD8D,MACvD,EAAQ,AAAI,MAAM,CAAC,2DAA2D,EAAE,EAAO,GAAG,CAAA,CAAE,EAEvG,CAEA,GAAI,KAAmB,IAAZ,EACT,OADkC,AAC3B,EAAQ,AAAI,MAAM,wBACpB,GAAI,EAAiB,CAC1B,GAAI,CA9EiB,EA+EH,EA9Eb,EAAS,CADc,EACY,EAAM,EAAS,UA+EvD,CACA,MAAO,EAAO,CACZ,OAAO,EAAQ,EACjB,CACI,AAAC,EAAQ,aAAa,EAAE,CAC1B,EAAU,OAAO,MAAM,CAAC,CAAC,EAAE,EAAA,CAE/B,KAAO,CACL,IAAM,EAAkB,EAAoB,MAAM,CAAC,SAAU,CAAG,EAC9D,OAAO,AAAwB,SAAjB,CAAO,CAAC,EAAI,AAC5B,GAEA,GAAI,EAAgB,MAAM,CAAG,EAC3B,CAD8B,MACvB,EAAY,AAAJ,MAAU,WAAa,EAAgB,IAAI,CAAC,KAAO,eAAkB,OAAO,EAAY,YAE3G,CAEA,GAAI,KAAuB,IAAhB,EAAQ,GAAG,EAAoB,AAA6B,SAAtB,EAAQ,EAA2B,OAAlB,CAChE,OAAO,EAAQ,AAAI,MAAM,8EAG3B,GAAI,KAAuB,IAAhB,EAAQ,GAAG,EAAoB,AAA6B,SAAtB,EAAQ,EAA2B,OAAlB,CAChE,OAAO,EAAQ,AAAI,MAAM,8EAG3B,GAAI,CA7GmB,EA8GL,EA7GX,EAAS,CADc,EACO,EAAO,EAAS,UA8GrD,CACA,MAAO,EAAO,CACZ,OAAO,EAAQ,EACjB,CAEA,GAAI,CAAC,EAAQ,8BAA8B,CACzC,CAD2C,EACvC,CACF,EAAsB,EAAO,GAAG,CAAE,EACpC,CAAE,MAAO,EAAO,CACd,OAAO,EAAQ,EACjB,CAGF,IAAM,EAAY,EAAQ,GAAG,EAAI,KAAK,KAAK,CAAC,KAAK,GAAG,GAAK,KAQzD,GANI,EAAQ,WAAW,CACrB,CADuB,MAChB,EAAQ,GAAG,CACT,IACT,EAAQ,GAAG,CAAG,CAAA,EAGZ,IAJwB,CAIK,IAAtB,EAAQ,SAAS,CAAkB,CAC5C,GAAI,CACF,EAAQ,GAAG,CAAG,EAAS,EAAQ,SAAS,CAAE,EAC5C,CACA,MAAO,EAAK,CACV,OAAO,EAAQ,EACjB,CACA,GAAI,KAAuB,IAAhB,EAAQ,GAAG,CACpB,GADsC,IAC/B,EAAQ,AAAI,MAAM,mGAE7B,CAEA,GAAI,AAA6B,SAAtB,EAAQ,SAAS,EAAuC,UAAnB,OAAO,EAAsB,CAC3E,GAAI,CACF,EAAQ,GAAG,CAAG,EAAS,EAAQ,SAAS,CAAE,EAC5C,CACA,MAAO,EAAK,CACV,OAAO,EAAQ,EACjB,CACA,GAAI,KAAuB,IAAhB,EAAQ,GAAG,CACpB,GADsC,IAC/B,EAAQ,AAAI,MAAM,mGAE7B,CAEA,OAAO,IAAI,CAAC,GAAoB,OAAO,CAAC,SAAU,CAAG,EACnD,IAAM,EAAQ,CAAkB,CAAC,EAAI,CACrC,GAAI,KAAwB,IAAjB,CAAO,CAAC,EAAI,CAAkB,CACvC,GAAI,KAA0B,IAAnB,CAAO,CAAC,EAAM,CACvB,IADyC,GAClC,EAAQ,AAAI,MAAM,gBAAkB,EAAM,yCAA2C,EAAQ,gBAEtG,CAAO,CAAC,EAAM,CAAG,CAAO,CAAC,EAAI,AAC/B,CACF,GAEA,IAAM,EAAW,EAAQ,QAAQ,EAAI,OAErC,GAAwB,YAApB,AAAgC,OAAzB,EACT,EAAW,GAAY,EAAK,GAE5B,EAAI,UAAU,CAAC,CACb,OAAQ,EACR,WAAY,EACZ,QAAS,EACT,SAAU,CACZ,GAAG,IAAI,CAAC,QAAS,GACd,IAAI,CAAC,OAAQ,SAAU,CAAS,EAE/B,GAAG,CAAC,EAAQ,qBAAqB,EAAI,aAAa,IAAI,CAAC,EAAO,GAAG,GAAK,EAAU,MAAM,CAAG,IACvF,CAD4F,MACrF,EAAS,AAAI,MAAM,CAAC,2DAA2D,EAAE,EAAO,GAAG,CAAA,CAAE,GAEtG,EAAS,KAAM,EACjB,OACG,CACL,IAAI,EAAY,EAAI,IAAI,CAAC,CAAC,OAAQ,EAAQ,QAAS,EAAS,OAAQ,EAAoB,SAAU,CAAQ,GAE1G,GAAG,CAAC,EAAQ,qBAAqB,EAAI,aAAa,IAAI,CAAC,EAAO,GAAG,GAAK,EAAU,MAAM,CAAG,IACvF,CAD4F,KACtF,AAAI,MAAM,CAAC,2DAA2D,EAAE,EAAO,GAAG,CAAA,CAAE,EAE5F,OAAO,CACT,CACF,mBC5PA,EAAO,OAAO,CAAG,CACf,MAAM,CAAA,EAAA,CAAA,CAAA,OACN,MAAM,CAAA,EAAA,CAAA,CAAA,OACN,IAAI,CAAA,EAAA,CAAA,CAAA,OACJ,iBAAiB,CAAA,EAAA,CAAA,CAAA,OACjB,cAAc,CAAA,EAAA,CAAA,CAAA,OACd,iBAAiB,CAAA,EAAA,CAAA,CAAA,MACnB,6FCPA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIO,IAAM,EAFW,AAGtB,WAAgB,MAAM,EAAI,IAAI,EAAA,YAAY,CAAC,CACzC,IAAK,CAAC,OAAQ,QAAQ,AACxB,GCwBF,EAAA,CAAA,CAAA,OA+SqB,aAAnB,OAAO,SACP,SAC4B,YAA5B,OAAO,QAAQ,QAAQ,GACK,YAAxB,CACE,MADK,cAEL,QAAQ,QAAA,AAAQ,ECjVxB,CDkVM,GClVN,EAAA,EAAA,CAAA,CAAA,OAUA,IAAM,EAAa,QAAQ,GAAG,CAAC,eAAe,EAAI,oBAW3C,SAAS,EAAY,CAAa,EACvC,GAAI,CACF,OAAO,EAAA,OAAG,CAAC,MAAM,CAAC,EAAO,EAC3B,CAAE,KAAM,CACN,OAAO,IACT,CACF", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 83]}