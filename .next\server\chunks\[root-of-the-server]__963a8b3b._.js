module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},87683,e=>{"use strict";e.s(["getUserPermissions",()=>n,"hasPermission",()=>s]);var t=e.i(15270);let r=["DASHBOARD_VIEW","TICKETS_VIEW","TICKETS_UPDATE","TICKETS_ASSIGN","REPORTS_VIEW","DEPARTMENTS_MANAGE","PROJECTS_MANAGE","AUDIT_VIEW"];async function n(e){return new Set("ADMIN"===e.role?r:(await t.prisma.userPermission.findMany({where:{userId:e.id,organizationId:e.organizationId},select:{permission:!0}})).map(e=>e.permission))}function s(e,t){return e.has(t)}},59203,(e,t,r)=>{},41601,e=>{"use strict";e.s(["handler",()=>b,"patchFetch",()=>C,"routeModule",()=>T,"serverHooks",()=>A,"workAsyncStorage",()=>y,"workUnitAsyncStorage",()=>j],41601);var t=e.i(47909),r=e.i(74017),n=e.i(96250),s=e.i(59756),i=e.i(61916),a=e.i(69741),o=e.i(16795),d=e.i(87718),u=e.i(95169),l=e.i(47587),p=e.i(66012),c=e.i(70101),m=e.i(26937),x=e.i(10372),f=e.i(93695);e.i(52474);var h=e.i(220);e.s(["GET",()=>w,"PATCH",()=>E],67693);var g=e.i(89171),R=e.i(15270),I=e.i(65800),v=e.i(87683);async function w(e,t){let r=e.cookies.get("auth-token")?.value,n=r?(0,I.verifyToken)(r):null;if(!n)return g.NextResponse.json({error:"Unauthorized"},{status:401});let{params:s}=t,{id:i}=await s,a=await R.prisma.ticket.findUnique({where:{id:i},include:{createdBy:{select:{fullName:!0,email:!0}},assignedTo:{select:{fullName:!0,email:!0}},messages:{include:{author:{select:{fullName:!0,email:!0}}},orderBy:{createdAt:"asc"}}}});return a&&a.organizationId===n.organizationId&&(!a.projectId||await R.prisma.userProject.findUnique({where:{userId_projectId:{userId:n.id,projectId:a.projectId}}}))?g.NextResponse.json({ticket:a}):g.NextResponse.json({error:"Not found"},{status:404})}async function E(e,t){let r=e.cookies.get("auth-token")?.value,n=r?(0,I.verifyToken)(r):null;if(!n)return g.NextResponse.json({error:"Unauthorized"},{status:401});let{status:s,priority:i,assignedToId:a,title:o,description:d,category:u,departmentId:l}=await e.json()||{},{params:p}=t,{id:c}=await p,m=await R.prisma.ticket.findUnique({where:{id:c}});if(!m||m.organizationId!==n.organizationId||m.projectId&&!await R.prisma.userProject.findUnique({where:{userId_projectId:{userId:n.id,projectId:m.projectId}}}))return g.NextResponse.json({error:"Not found"},{status:404});let x="ADMIN"===n.role,f="MANAGER"===n.role,h="AGENT"===n.role;if("CUSTOMER"===n.role)return g.NextResponse.json({error:"Forbidden"},{status:403});let w=await (0,v.getUserPermissions)({id:n.id,role:n.role,organizationId:n.organizationId});if((x||f)&&void 0!==l){if(null===l);else if(!await R.prisma.department.findFirst({where:{id:l,organizationId:n.organizationId},select:{id:!0}}))return g.NextResponse.json({error:"Invalid department"},{status:400})}if((x||f)&&void 0!==a&&a){let e=await R.prisma.user.findUnique({where:{id:a}});if(!e||e.organizationId!==n.organizationId)return g.NextResponse.json({error:"Invalid assignee"},{status:400});if(m.projectId&&!await R.prisma.userProject.findUnique({where:{userId_projectId:{userId:e.id,projectId:m.projectId}}}))return g.NextResponse.json({error:"Assignee is not a member of the ticket's project"},{status:400});let t=void 0!==l?l:m.departmentId;if(t&&!await R.prisma.userDepartment.findUnique({where:{userId_departmentId:{userId:e.id,departmentId:t}}}))return g.NextResponse.json({error:"Assignee is not a member of the ticket's department"},{status:400})}if(f){let e=void 0!==l?l:m.departmentId;if(e&&!await R.prisma.userDepartment.findUnique({where:{userId_departmentId:{userId:n.id,departmentId:e}}}))return g.NextResponse.json({error:"Forbidden: manager not a member of department"},{status:403})}if(h){if(void 0!==a||void 0!==l||o||d||u)return g.NextResponse.json({error:"Forbidden: agents cannot modify assignment, department, title, description, or category"},{status:403});if(!(0,v.hasPermission)(w,"TICKETS_UPDATE"))return g.NextResponse.json({error:"Forbidden: missing permission TICKETS_UPDATE"},{status:403});if(s&&!["IN_PROGRESS","WAITING_FOR_CUSTOMER","RESOLVED"].includes(s))return g.NextResponse.json({error:"Forbidden: status not allowed for agents"},{status:403});if("URGENT"===i)return g.NextResponse.json({error:"Forbidden: priority URGENT not allowed for agents"},{status:403})}if(f){if(o||d||u)return g.NextResponse.json({error:"Forbidden: managers cannot modify title, description, or category"},{status:403});if((void 0!==a||void 0!==l)&&!(0,v.hasPermission)(w,"TICKETS_ASSIGN"))return g.NextResponse.json({error:"Forbidden: missing permission TICKETS_ASSIGN"},{status:403})}let E={};s&&(E.status=s),i&&(E.priority=i),(x||f)&&(void 0!==a&&(E.assignedToId=a||null),void 0!==l&&(E.departmentId=l||null)),x&&(o&&(E.title=o),d&&(E.description=d),u&&(E.category=u));let N=await R.prisma.ticket.update({where:{id:c},data:E}),T=[];return s&&s!==m.status&&T.push(`Status: ${m.status} → ${s}`),i&&i!==m.priority&&T.push(`Priority: ${m.priority} → ${i}`),(x||f)&&void 0!==a&&a!==m.assignedToId&&T.push(`Assignee: ${m.assignedToId||"Unassigned"} → ${a||"Unassigned"}`),(x||f)&&void 0!==l&&l!==m.departmentId&&T.push(`Department: ${m.departmentId||"None"} → ${l||"None"}`),(x||f)&&o&&o!==m.title&&T.push("Title updated"),(x||f)&&d&&d!==m.description&&T.push("Description updated"),(x||f)&&u&&u!==(m.category||"")&&T.push(`Category: ${m.category||"None"} → ${u}`),T.length&&await R.prisma.ticketMessage.create({data:{content:`[AUDIT] ${T.join("; ")} — by ${n.fullName||n.email}`,isInternal:!0,ticketId:m.id,authorId:n.id}}),g.NextResponse.json({ticket:N})}var N=e.i(67693);let T=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/tickets/[id]/route",pathname:"/api/tickets/[id]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/tickets/[id]/route.ts",nextConfigOutput:"export",userland:N}),{workAsyncStorage:y,workUnitAsyncStorage:j,serverHooks:A}=T;function C(){return(0,n.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:j})}async function b(e,t,n){var g;let R="/api/tickets/[id]/route";R=R.replace(/\/index$/,"")||"/";let I=await T.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!I)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:E,isDraftMode:N,prerenderManifest:y,routerServerContext:j,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:b}=I,S=(0,a.normalizeAppPath)(R),P=!!(y.dynamicRoutes[S]||y.routes[b]);if(P&&!N){let e=!!y.routes[b],t=y.dynamicRoutes[S];if(t&&!1===t.fallback&&!e)throw new f.NoFallbackError}let _=null;!P||T.isDev||N||(_="/index"===(_=b)?"/":_);let k=!0===T.isDev||!P,U=P&&!k,q=e.method||"GET",O=(0,i.getTracer)(),D=O.getActiveScopeSpan(),M={params:w,prerenderManifest:y,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:k,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=E.experimental)?void 0:g.cacheLife,isRevalidate:U,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>T.onRequestError(e,t,n,j)},sharedContext:{buildId:v}},$=new o.NodeNextRequest(e),H=new o.NodeNextResponse(t),F=d.NextRequestAdapter.fromNodeNextRequest($,(0,d.signalFromNodeResponse)(t));try{let a=async r=>T.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=O.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=n.get("next.route");if(s){let e=`${q} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${q} ${e.url}`)}),o=async i=>{var o,d;let u=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&A&&C&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await a(i);e.fetchMetrics=M.renderOpts.fetchMetrics;let d=M.renderOpts.pendingWaitUntil;d&&n.waitUntil&&(n.waitUntil(d),d=void 0);let u=M.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)($,H,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);u&&(t[x.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,n=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await T.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:A})},j),t}},f=await T.handleResponse({req:e,nextConfig:E,cacheKey:_,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:u,waitUntil:n.waitUntil});if(!P)return null;if((null==f||null==(o=f.value)?void 0:o.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==f||null==(d=f.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":f.isMiss?"MISS":f.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(f.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&P||g.delete(x.NEXT_CACHE_TAGS_HEADER),!f.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,m.getCacheControlHeader)(f.cacheControl)),await (0,p.sendResponse)($,H,new Response(f.value.body,{headers:g,status:f.value.status||200})),null};D?await o(D):await O.withPropagatedContext(e.headers,()=>O.trace(u.BaseServerSpan.handleRequest,{spanName:`${q} ${e.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":q,"http.target":e.url}},o))}catch(t){if(D||t instanceof f.NoFallbackError||await T.onRequestError(e,t,{routerKind:"App Router",routePath:S,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:A})}),P)throw t;return await (0,p.sendResponse)($,H,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__963a8b3b._.js.map