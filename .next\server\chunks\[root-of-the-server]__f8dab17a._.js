module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},8315,(e,t,r)=>{},61081,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>k,"routeModule",()=>N,"serverHooks",()=>A,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>b],61081);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),m=e.i(10372),h=e.i(93695);e.i(52474);var R=e.i(220);e.s(["DELETE",()=>E,"PATCH",()=>y,"runtime",()=>w],33811);var v=e.i(89171),f=e.i(15270),g=e.i(65800);let w="nodejs";async function y(e,t){let r=e.cookies.get("auth-token")?.value,a=r?(0,g.verifyToken)(r):null;if(!a)return v.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==a.role)return v.NextResponse.json({error:"Forbidden"},{status:403});try{let{name:r,description:n}=await e.json(),{params:s}=t,{id:i}=await s,o=await f.prisma.department.findUnique({where:{id:i}});if(!o||o.organizationId!==a.organizationId)return v.NextResponse.json({error:"Not found"},{status:404});let l=await f.prisma.department.update({where:{id:o.id},data:{..."string"==typeof r&&r.trim()?{name:r}:{},...void 0!==n?{description:n||null}:{}},select:{id:!0,name:!0,description:!0}});return v.NextResponse.json({department:l})}catch(e){return v.NextResponse.json({error:e?.message||"Failed to update department"},{status:400})}}async function E(e,t){let r=e.cookies.get("auth-token")?.value,a=r?(0,g.verifyToken)(r):null;if(!a)return v.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==a.role)return v.NextResponse.json({error:"Forbidden"},{status:403});try{let{params:e}=t,{id:r}=await e,n=await f.prisma.department.findUnique({where:{id:r}});if(!n||n.organizationId!==a.organizationId)return v.NextResponse.json({error:"Not found"},{status:404});if(await f.prisma.ticket.count({where:{departmentId:n.id}})>0)return v.NextResponse.json({error:"Cannot delete department with existing tickets"},{status:400});return await f.prisma.userDepartment.deleteMany({where:{departmentId:n.id}}),await f.prisma.department.delete({where:{id:n.id}}),v.NextResponse.json({success:!0})}catch(e){return v.NextResponse.json({error:e?.message||"Failed to delete department"},{status:400})}}var C=e.i(33811);let N=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/departments/[id]/route",pathname:"/api/departments/[id]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/departments/[id]/route.ts",nextConfigOutput:"export",userland:C}),{workAsyncStorage:j,workUnitAsyncStorage:b,serverHooks:A}=N;function k(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:b})}async function q(e,t,a){var v;let f="/api/departments/[id]/route";f=f.replace(/\/index$/,"")||"/";let g=await N.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:E,isDraftMode:C,prerenderManifest:j,routerServerContext:b,isOnDemandRevalidate:A,revalidateOnlyGenerated:k,resolvedPathname:q}=g,T=(0,i.normalizeAppPath)(f),P=!!(j.dynamicRoutes[T]||j.routes[q]);if(P&&!C){let e=!!j.routes[q],t=j.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let I=null;!P||N.isDev||C||(I="/index"===(I=q)?"/":I);let O=!0===N.isDev||!P,U=P&&!O,_=e.method||"GET",H=(0,s.getTracer)(),S=H.getActiveScopeSpan(),M={params:y,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:U,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>N.onRequestError(e,t,a,b)},sharedContext:{buildId:w}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>N.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${_} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&A&&k&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await N.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:A})},b),t}},h=await N.handleResponse({req:e,nextConfig:E,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:k,responseGenerator:d,waitUntil:a.waitUntil});if(!P)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(D,F,new Response(h.value.body,{headers:v,status:h.value.status||200})),null};S?await o(S):await H.withPropagatedContext(e.headers,()=>H.trace(d.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},o))}catch(t){if(S||t instanceof h.NoFallbackError||await N.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:A})}),P)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__f8dab17a._.js.map