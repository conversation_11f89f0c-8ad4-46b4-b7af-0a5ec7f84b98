module.exports=[46058,(a,b,c)=>{"use strict";function d(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(d=function(a){return a?c:b})(a)}c._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=d(b);if(c&&c.has(a))return c.get(a);var e={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(e,g,h):e[g]=a[g]}return e.default=a,c&&c.set(a,e),e}},91735,(a,b,c)=>{"use strict";function d(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function e(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function f(a){let b=new URLSearchParams;for(let[c,d]of Object.entries(a))if(Array.isArray(d))for(let a of d)b.append(c,e(a));else b.set(c,e(d));return b}function g(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{assign:function(){return g},searchParamsToUrlQuery:function(){return d},urlQueryToSearchParams:function(){return f}})},43087,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=a.r(46058)._(a.r(91735)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},8591,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=a.r(72131);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},46272,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return t},MissingStaticPage:function(){return s},NormalizeError:function(){return q},PageNotFoundError:function(){return r},SP:function(){return n},ST:function(){return o},WEB_VITALS:function(){return d},execOnce:function(){return e},getDisplayName:function(){return j},getLocationOrigin:function(){return h},getURL:function(){return i},isAbsoluteUrl:function(){return g},isResSent:function(){return k},loadGetInitialProps:function(){return m},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return u}});let d=["CLS","FCP","FID","INP","LCP","TTFB"];function e(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let f=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,g=a=>f.test(a);function h(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function i(){let{href:a}=window.location,b=h();return a.substring(b.length)}function j(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function k(a){return a.finished||a.headersSent}function l(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function m(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await m(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&k(c))return d;if(!d)throw Object.defineProperty(Error('"'+j(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let n="undefined"!=typeof performance,o=n&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class p extends Error{}class q extends Error{}class r extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class s extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class t extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function u(a){return JSON.stringify({message:a.message,stack:a.stack})}},8868,(a,b,c)=>{"use strict";function d(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"parsePath",{enumerable:!0,get:function(){return d}})},5407,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(8868);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}},46112,(a,b,c)=>{"use strict";function d(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeTrailingSlash",{enumerable:!0,get:function(){return d}})},25075,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=a.r(46112),e=a.r(8868),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return""+(0,d.removeTrailingSlash)(b)+c+f};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},38243,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addBasePath",{enumerable:!0,get:function(){return f}});let d=a.r(5407),e=a.r(25075);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,"/WeCare"))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},92434,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},88347,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HMR_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return e},ACTION_PREFETCH:function(){return h},ACTION_REFRESH:function(){return d},ACTION_RESTORE:function(){return f},ACTION_SERVER_ACTION:function(){return j},ACTION_SERVER_PATCH:function(){return g},PrefetchCacheEntryStatus:function(){return l},PrefetchKind:function(){return k}});let d="refresh",e="navigate",f="restore",g="server-patch",h="prefetch",i="hmr-refresh",j="server-action";var k=function(a){return a.AUTO="auto",a.FULL="full",a.TEMPORARY="temporary",a}({}),l=function(a){return a.fresh="fresh",a.reusable="reusable",a.expired="expired",a.stale="stale",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},37909,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HEADER:function(){return e},FLIGHT_HEADERS:function(){return m},NEXT_ACTION_NOT_FOUND_HEADER:function(){return t},NEXT_DID_POSTPONE_HEADER:function(){return p},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return j},NEXT_HMR_REFRESH_HEADER:function(){return i},NEXT_IS_PRERENDER_HEADER:function(){return s},NEXT_REWRITTEN_PATH_HEADER:function(){return q},NEXT_REWRITTEN_QUERY_HEADER:function(){return r},NEXT_ROUTER_PREFETCH_HEADER:function(){return g},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return h},NEXT_ROUTER_STALE_TIME_HEADER:function(){return o},NEXT_ROUTER_STATE_TREE_HEADER:function(){return f},NEXT_RSC_UNION_QUERY:function(){return n},NEXT_URL:function(){return k},RSC_CONTENT_TYPE_HEADER:function(){return l},RSC_HEADER:function(){return d}});let d="rsc",e="next-action",f="next-router-state-tree",g="next-router-prefetch",h="next-router-segment-prefetch",i="next-hmr-refresh",j="__next_hmr_refresh_hash__",k="next-url",l="text/x-component",m=[d,f,g,i,h],n="_rsc",o="x-nextjs-stale-time",p="x-nextjs-postponed",q="x-nextjs-rewritten-path",r="x-nextjs-rewritten-query",s="x-nextjs-prerender",t="x-nextjs-action-not-found";("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},67009,(a,b,c)=>{"use strict";function d(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isThenable",{enumerable:!0,get:function(){return d}})},90841,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{dispatchAppRouterAction:function(){return g},useActionQueue:function(){return h}});let d=a.r(46058)._(a.r(72131)),e=a.r(67009),f=null;function g(a){if(null===f)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});f(a)}function h(a){let[b,c]=d.default.useState(a.state);return f=b=>a.dispatch(b,c),(0,e.isThenable)(b)?(0,d.use)(b):b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20611,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"callServer",{enumerable:!0,get:function(){return g}});let d=a.r(72131),e=a.r(88347),f=a.r(90841);async function g(a,b){return new Promise((c,g)=>{(0,d.startTransition)(()=>{(0,f.dispatchAppRouterAction)({type:e.ACTION_SERVER_ACTION,actionId:a,actionArgs:b,resolve:c,reject:g})})})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},1722,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findSourceMapURL",{enumerable:!0,get:function(){return d}});let d=void 0;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},22129,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getFlightDataPartsFromPath:function(){return e},getNextFlightSegmentPath:function(){return f},normalizeFlightData:function(){return g},prepareFlightRouterStateForRequest:function(){return h}});let d=a.r(39118);function e(a){var b;let[c,d,e,f]=a.slice(-4),g=a.slice(0,-4);return{pathToSegment:g.slice(0,-1),segmentPath:g,segment:null!=(b=g[g.length-1])?b:"",tree:c,seedData:d,head:e,isHeadPartial:f,isRootRender:4===a.length}}function f(a){return a.slice(2)}function g(a){return"string"==typeof a?a:a.map(a=>e(a))}function h(a,b){return b?encodeURIComponent(JSON.stringify(a)):encodeURIComponent(JSON.stringify(function a(b){var c,e;let[f,g,h,i,j,k]=b,l="string"==typeof(c=f)&&c.startsWith(d.PAGE_SEGMENT_KEY+"?")?d.PAGE_SEGMENT_KEY:c,m={};for(let[b,c]of Object.entries(g))m[b]=a(c);let n=[l,m,null,(e=i)&&"refresh"!==e?i:null];return void 0!==j&&(n[4]=j),void 0!==k&&(n[5]=k),n}(a)))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},91377,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getAppBuildId:function(){return f},setAppBuildId:function(){return e}});let d="";function e(a){d=a}function f(){return d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16661,(a,b,c)=>{"use strict";function d(a){let b=5381;for(let c=0;c<a.length;c++)b=(b<<5)+b+a.charCodeAt(c)|0;return b>>>0}function e(a){return d(a).toString(36).slice(0,5)}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{djb2Hash:function(){return d},hexHash:function(){return e}})},35604,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"computeCacheBustingSearchParam",{enumerable:!0,get:function(){return e}});let d=a.r(16661);function e(a,b,c,e){return(void 0===a||"0"===a)&&void 0===b&&void 0===c&&void 0===e?"":(0,d.hexHash)([a||"0",b||"0",c||"0",e||"0"].join(","))}},25744,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{setCacheBustingSearchParam:function(){return f},setCacheBustingSearchParamWithHash:function(){return g}});let d=a.r(35604),e=a.r(37909),f=(a,b)=>{g(a,(0,d.computeCacheBustingSearchParam)(b[e.NEXT_ROUTER_PREFETCH_HEADER],b[e.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],b[e.NEXT_ROUTER_STATE_TREE_HEADER],b[e.NEXT_URL]))},g=(a,b)=>{let c=a.search,d=(c.startsWith("?")?c.slice(1):c).split("&").filter(a=>a&&!a.startsWith(""+e.NEXT_RSC_UNION_QUERY+"="));b.length>0?d.push(e.NEXT_RSC_UNION_QUERY+"="+b):d.push(""+e.NEXT_RSC_UNION_QUERY),a.search=d.length?"?"+d.join("&"):""};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},58216,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ROOT_SEGMENT_CACHE_KEY:function(){return f},ROOT_SEGMENT_REQUEST_KEY:function(){return e},appendSegmentCacheKeyPart:function(){return j},appendSegmentRequestKeyPart:function(){return h},convertSegmentPathToStaticExportFilename:function(){return m},createSegmentCacheKeyPart:function(){return i},createSegmentRequestKeyPart:function(){return g}});let d=a.r(39118),e="",f="";function g(a){if("string"==typeof a)return a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:"/_not-found"===a?"_not-found":l(a);let b=a[0],c=a[2];return"$"+c+"$"+l(b)}function h(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}function i(a,b){return"string"==typeof b?a:a+"$"+l(b[1])}function j(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}let k=/^[a-zA-Z0-9\-_@]+$/;function l(a){return k.test(a)?a:"!"+btoa(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function m(a){return"__next"+a.replace(/\//g,".")+".txt"}},23187,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{doesStaticSegmentAppearInURL:function(){return j},getCacheKeyForDynamicParam:function(){return k},getParamValueFromCacheKey:function(){return m},getRenderedPathname:function(){return h},getRenderedSearch:function(){return g},parseDynamicParamFromURLPart:function(){return i},urlToUrlWithoutFlightMarker:function(){return l}});let d=a.r(39118),e=a.r(58216),f=a.r(37909);function g(a){let b=a.headers.get(f.NEXT_REWRITTEN_QUERY_HEADER);return null!==b?""===b?"":"?"+b:l(new URL(a.url)).search}function h(a){let b=a.headers.get(f.NEXT_REWRITTEN_PATH_HEADER);return null!=b?b:l(new URL(a.url)).pathname}function i(a,b,c){switch(a){case"c":case"ci":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):[];case"oc":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):null;case"d":case"di":if(c>=b.length)return"";return encodeURIComponent(b[c]);default:return""}}function j(a){return!(a===e.ROOT_SEGMENT_REQUEST_KEY||a.startsWith(d.PAGE_SEGMENT_KEY)||"("===a[0]&&a.endsWith(")"))&&a!==d.DEFAULT_SEGMENT_KEY&&"/_not-found"!==a}function k(a,b){return"string"==typeof a?(0,d.addSearchParamsIfPageSegment)(a,Object.fromEntries(new URLSearchParams(b))):null===a?"":a.join("/")}function l(a){let b=new URL(a);return b.searchParams.delete(f.NEXT_RSC_UNION_QUERY),b}function m(a,b){return"c"===b||"oc"===b?a.split("/"):a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},20873,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createFetch:function(){return q},createFromNextReadableStream:function(){return r},fetchServerResponse:function(){return p}});let d=a.r(38783),e=a.r(37909),f=a.r(20611),g=a.r(1722),h=a.r(88347),i=a.r(22129),j=a.r(91377),k=a.r(25744),l=a.r(23187),m=d.createFromReadableStream;function n(a){return{flightData:(0,l.urlToUrlWithoutFlightMarker)(new URL(a,location.origin)).toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}let o=new AbortController;async function p(a,b){let{flightRouterState:c,nextUrl:d,prefetchKind:f}=b,g={[e.RSC_HEADER]:"1",[e.NEXT_ROUTER_STATE_TREE_HEADER]:(0,i.prepareFlightRouterStateForRequest)(c,b.isHmrRefresh)};f===h.PrefetchKind.AUTO&&(g[e.NEXT_ROUTER_PREFETCH_HEADER]="1"),d&&(g[e.NEXT_URL]=d);try{var k;let b=f?f===h.PrefetchKind.TEMPORARY?"high":"low":"auto",c=await q(a,g,b,o.signal),d=(0,l.urlToUrlWithoutFlightMarker)(new URL(c.url)),m=c.redirected?d:void 0,p=c.headers.get("content-type")||"",s=!!(null==(k=c.headers.get("vary"))?void 0:k.includes(e.NEXT_URL)),t=!!c.headers.get(e.NEXT_DID_POSTPONE_HEADER),u=c.headers.get(e.NEXT_ROUTER_STALE_TIME_HEADER),v=null!==u?1e3*parseInt(u,10):-1;if(!p.startsWith(e.RSC_CONTENT_TYPE_HEADER)||!c.ok||!c.body)return a.hash&&(d.hash=a.hash),n(d.toString());let w=t?function(a){let b=a.getReader();return new ReadableStream({async pull(a){for(;;){let{done:c,value:d}=await b.read();if(!c){a.enqueue(d);continue}return}}})}(c.body):c.body,x=await r(w);if((0,j.getAppBuildId)()!==x.b)return n(c.url);return{flightData:(0,i.normalizeFlightData)(x.f),canonicalUrl:m,couldBeIntercepted:s,prerendered:x.S,postponed:t,staleTime:v}}catch(b){return o.signal.aborted||console.error("Failed to fetch RSC payload for "+a+". Falling back to browser navigation.",b),{flightData:a.toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}}async function q(a,b,c,d){let f=new URL(a);(0,k.setCacheBustingSearchParam)(f,b);let g=await fetch(f,{credentials:"same-origin",headers:b,priority:c||void 0,signal:d}),h=g.redirected,i=new URL(g.url,f);return i.searchParams.delete(e.NEXT_RSC_UNION_QUERY),{url:i.href,redirected:h,ok:g.ok,headers:g.headers,body:g.body,status:g.status}}function r(a){return m(a,{callServer:f.callServer,findSourceMapURL:g.findSourceMapURL})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},74685,(a,b,c)=>{"use strict";function d(a,b){return void 0===b&&(b=!0),a.pathname+a.search+(b?a.hash:"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createHrefFromUrl",{enumerable:!0,get:function(){return d}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},70681,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createRouterCacheKey",{enumerable:!0,get:function(){return e}});let d=a.r(39118);function e(a,b){return(void 0===b&&(b=!1),Array.isArray(a))?a[0]+"|"+a[1]+"|"+a[2]:b&&a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},77159,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=a.r(70681),e=a.r(22129);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30641,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"matchSegment",{enumerable:!0,get:function(){return d}});let d=(a,b)=>"string"==typeof a?"string"==typeof b&&a===b:"string"!=typeof b&&a[0]===b[0]&&a[1]===b[1];("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},10089,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=a.r(70681),e=a.r(88347);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88770,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=a.r(70681);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},66013,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=a.r(88770),e=a.r(10089),f=a.r(70681),g=a.r(39118);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},97768,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=a.r(10089),e=a.r(66013);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30719,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=a.r(97768),e=a.r(20873),f=a.r(39118);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},51748,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=a.r(39118),e=a.r(22129),f=a.r(30641),g=a.r(30719);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},9381,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=a.r(22129),e=a.r(30641);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},71094,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},19924,(a,b,c)=>{"use strict";function d(a){return a.startsWith("/")?a:"/"+a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ensureLeadingSlash",{enumerable:!0,get:function(){return d}})},53808,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{normalizeAppPath:function(){return f},normalizeRscURL:function(){return g}});let d=a.r(19924),e=a.r(39118);function f(a){return(0,d.ensureLeadingSlash)(a.split("/").reduce((a,b,c,d)=>!b||(0,e.isGroupSegment)(b)||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b,""))}function g(a){return a.replace(/\.rsc($|\?)/,"$1")}},18099,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{INTERCEPTION_ROUTE_MARKERS:function(){return e},extractInterceptionRouteInformation:function(){return g},isInterceptionRouteAppPath:function(){return f}});let d=a.r(53808),e=["(..)(..)","(.)","(..)","(...)"];function f(a){return void 0!==a.split("/").find(a=>e.find(b=>a.startsWith(b)))}function g(a){let b,c,f;for(let d of a.split("/"))if(c=e.find(a=>d.startsWith(a))){[b,f]=a.split(c,2);break}if(!b||!c||!f)throw Object.defineProperty(Error("Invalid interception route: "+a+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(b=(0,d.normalizeAppPath)(b),c){case"(.)":f="/"===b?"/"+f:b+"/"+f;break;case"(..)":if("/"===b)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});f=b.split("/").slice(0,-1).concat(f).join("/");break;case"(...)":f="/"+f;break;case"(..)(..)":let g=b.split("/");if(g.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});f=g.slice(0,-2).concat(f).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:b,interceptedRoute:f}}},37042,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=a.r(18099),e=a.r(39118),f=a.r(30641),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88632,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleMutable",{enumerable:!0,get:function(){return f}});let d=a.r(37042);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},4662,(a,b,c)=>{"use strict";c._=function(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}},91296,(a,b,c)=>{"use strict";var d=0;c._=function(a){return"__private_"+d+++"_"+a}},81083,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=a.r(4662),e=a.r(91296);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},98629,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=a.r(20873),e=a.r(88347),f=a.r(35399);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d}=a;return Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},35399,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=a.r(81083),e=a.r(98629),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33354,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},51842,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return d}});let d=/[\w-]+-Google|Google-[\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i},19269,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=a.r(51842),e=/Googlebot(?!-)|Googlebot$/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},72724,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=a.r(72131),e=a.r(35112),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},57774,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{RedirectBoundary:function(){return l},RedirectErrorBoundary:function(){return k}});let d=a.r(46058),e=a.r(87924),f=d._(a.r(72131)),g=a.r(74137),h=a.r(22099),i=a.r(11026);function j(a){let{redirect:b,reset:c,redirectType:d}=a,e=(0,g.useRouter)();return(0,f.useEffect)(()=>{f.default.startTransition(()=>{d===i.RedirectType.push?e.push(b,{}):e.replace(b,{}),c()})},[b,d,c,e]),null}class k extends f.default.Component{static getDerivedStateFromError(a){if((0,i.isRedirectError)(a))return{redirect:(0,h.getURLFromRedirectError)(a),redirectType:(0,h.getRedirectTypeFromError)(a)};throw a}render(){let{redirect:a,redirectType:b}=this.state;return null!==a&&null!==b?(0,e.jsx)(j,{redirect:a,redirectType:b,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(a){super(a),this.state={redirect:null,redirectType:null}}}function l(a){let{children:b}=a,c=(0,g.useRouter)();return(0,e.jsx)(k,{router:c,children:b})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},99138,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=a.r(39118),e=a.r(70681);function f(a,b){return function a(b,c,f,g){if(0===Object.keys(c).length)return[b,f,g];let h=Object.keys(c).filter(a=>"children"!==a);for(let g of("children"in c&&h.unshift("children"),h)){let[h,i]=c[g];if(h===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(g);if(!j)continue;let k=(0,e.createRouterCacheKey)(h),l=(0,e.createRouterCacheKey)(h,!0),m=j.get(k);if(!m)continue;let n=a(m,i,f+"/"+k,f+"/"+l);if(n)return n}return null}(a,b,"","")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},72471,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unresolvedThenable",{enumerable:!0,get:function(){return d}});let d={then:()=>{}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11679,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(8868);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},78897,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=a.r(11679);function e(a){return(0,d.pathHasPrefix)(a,"/WeCare")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16749,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeBasePath",{enumerable:!0,get:function(){return e}}),a.r(78897);let d="/WeCare";function e(a){return 0===d.length||(a=a.slice(d.length)).startsWith("/")||(a="/"+a),a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},18274,(a,b,c)=>{"use strict";function d(a){return!1}function e(){}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{handleHardNavError:function(){return d},useNavFailureHandler:function(){return e}}),a.r(72131),a.r(74685),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88807,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=a.r(87924),e=a.r(72131);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},94722,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useUntrackedPathname",{enumerable:!0,get:function(){return f}});let d=a.r(72131),e=a.r(36313);function f(){return!function(){{let{workUnitAsyncStorage:b}=a.r(32319),c=b.getStore();if(!c)return!1;switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":let d=c.fallbackRouteParams;return!!d&&d.size>0}return!1}}()?(0,d.useContext)(e.PathnameContext):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},80981,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ErrorBoundary:function(){return k},ErrorBoundaryHandler:function(){return j}});let d=a.r(33354),e=a.r(87924),f=d._(a.r(72131)),g=a.r(94722),h=a.r(77747);a.r(18274);let i=a.r(51234);a.r(19269);class j extends f.default.Component{static getDerivedStateFromError(a){if((0,h.isNextRouterError)(a))throw a;return{error:a}}static getDerivedStateFromProps(a,b){let{error:c}=b;return a.pathname!==b.previousPathname&&b.error?{error:null,previousPathname:a.pathname}:{error:b.error,previousPathname:a.pathname}}render(){return this.state.error&&1?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(i.HandleISRError,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,e.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(a){super(a),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function k(a){let{errorComponent:b,errorStyles:c,errorScripts:d,children:f}=a,h=(0,g.useUntrackedPathname)();return b?(0,e.jsx)(j,{pathname:h,errorComponent:b,errorStyles:c,errorScripts:d,children:f}):(0,e.jsx)(e.Fragment,{children:f})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},86293,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}}),a.r(33354);let d=a.r(87924);a.r(72131),a.r(88807);let e=a.r(80981);function f(a){let{children:b,errorComponent:c,errorStyles:f,errorScripts:g}=a;return(0,d.jsx)(e.ErrorBoundary,{errorComponent:c,errorStyles:f,errorScripts:g,children:b})}a.r(19269),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},50822,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{MetadataBoundary:function(){return f},OutletBoundary:function(){return h},RootLayoutBoundary:function(){return i},ViewportBoundary:function(){return g}});let d=a.r(83590),e={[d.METADATA_BOUNDARY_NAME]:function({children:a}){return a},[d.VIEWPORT_BOUNDARY_NAME]:function({children:a}){return a},[d.OUTLET_BOUNDARY_NAME]:function({children:a}){return a},[d.ROOT_LAYOUT_BOUNDARY_NAME]:function({children:a}){return a}},f=e[d.METADATA_BOUNDARY_NAME.slice(0)],g=e[d.VIEWPORT_BOUNDARY_NAME.slice(0)],h=e[d.OUTLET_BOUNDARY_NAME.slice(0)],i=e[d.ROOT_LAYOUT_BOUNDARY_NAME.slice(0)]},451,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=a.r(33354),e=a.r(46058),f=a.r(87924),g=e._(a.r(72131)),h=a.r(9270),i=a.r(88347),j=a.r(74685),k=a.r(36313),l=a.r(90841),m=a.r(19269),n=a.r(38243),o=a.r(72724),p=a.r(57774),q=a.r(99138),r=a.r(72471),s=a.r(16749),t=a.r(78897),u=a.r(37042),v=a.r(18274),w=a.r(44637),x=a.r(22099),y=a.r(11026);a.r(16441);let z=d._(a.r(86293)),A=d._(a.r(40622)),B=a.r(50822),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,m.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,n.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e}=a,j=(0,l.useActionQueue)(c),{canonicalUrl:m}=j,{searchParams:n,pathname:v}=(0,g.useMemo)(()=>{let a=new URL(m,"http://n");return{searchParams:a.searchParams,pathname:(0,t.hasBasePath)(a.pathname)?(0,s.removeBasePath)(a.pathname):a.pathname}},[m]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,y.isRedirectError)(b)){a.preventDefault();let c=(0,x.getURLFromRedirectError)(b);(0,x.getRedirectTypeFromError)(b)===y.RedirectType.push?w.publicAppRouterInstance.push(c,{}):w.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:A}=j;if(A.mpaNavigation){if(C.pendingMpaPath!==m){let a=window.location;A.pendingPush?a.assign(m):a.replace(m),C.pendingMpaPath=m}throw r.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,w.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:D,tree:E,nextUrl:G,focusAndScrollRef:J}=j,K=(0,g.useMemo)(()=>(0,q.findHeadInCache)(D,E[1]),[D,E]),L=(0,g.useMemo)(()=>(0,u.getSelectedParams)(E),[E]),M=(0,g.useMemo)(()=>({parentTree:E,parentCacheNode:D,parentSegmentPath:null,url:m}),[E,D,m]),O=(0,g.useMemo)(()=>({tree:E,focusAndScrollRef:J,nextUrl:G}),[E,J,G]);if(null!==K){let[a,c,d]=K;b=(0,f.jsx)(I,{headCacheNode:a},d)}else b=null;let P=(0,f.jsxs)(p.RedirectBoundary,{children:[b,(0,f.jsx)(B.RootLayoutBoundary,{children:D.rsc}),(0,f.jsx)(o.AppRouterAnnouncer,{tree:E})]});return P=(0,f.jsx)(z.default,{errorComponent:e[0],errorStyles:e[1],children:P}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:j}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:L,children:(0,f.jsx)(k.PathnameContext.Provider,{value:v,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:n,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:O,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:w.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:M,children:P})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d}=a;(0,v.useNavFailureHandler)();let e=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c});return(0,f.jsx)(z.default,{errorComponent:A.default,children:e})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},57051,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=a.r(39118),e=a.r(30641),f=a.r(70681),g=a.r(71094),h=a.r(98629),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},45496,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=a.r(22129),e=a.r(70681);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},17522,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addSearchParamsToPageSegments:function(){return m},handleAliasedPrefetchEntry:function(){return l}});let d=a.r(39118),e=a.r(451),f=a.r(51748),g=a.r(74685),h=a.r(70681),i=a.r(66013),j=a.r(88632),k=a.r(23053);function l(a,b,c,l,n){let o,p=b.tree,q=b.cache,r=(0,g.createHrefFromUrl)(l),s=[];if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=m(c,Object.fromEntries(l.searchParams));let{seedData:g,isRootRender:j,pathToSegment:n}=b,t=["",...n];c=m(c,Object.fromEntries(l.searchParams));let u=(0,f.applyRouterStatePatchToTree)(t,p,c,r),v=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];v.loading=g[3],v.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,v,q,c,g)}else v.rsc=q.rsc,v.prefetchRsc=q.prefetchRsc,v.loading=q.loading,v.parallelRoutes=new Map(q.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,v,q,b);for(let a of(u&&(p=u,q=v,o=!0),(0,k.generateSegmentsFromPatch)(c))){let c=[...b.pathToSegment,...a];c[c.length-1]!==d.DEFAULT_SEGMENT_KEY&&s.push(c)}}return!!o&&(n.patchedTree=p,n.cache=q,n.canonicalUrl=r,n.hashFragment=l.hash,n.scrollableSegments=s,(0,j.handleMutable)(b,n))}function m(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=m(c,b);return[c,g,...f]}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},29442,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{FetchStrategy:function(){return p},NavigationResultTag:function(){return n},PrefetchPriority:function(){return o},cancelPrefetchTask:function(){return j},createCacheKey:function(){return m},getCurrentCacheVersion:function(){return h},isPrefetchTaskDirty:function(){return l},navigate:function(){return f},prefetch:function(){return e},reschedulePrefetchTask:function(){return k},revalidateEntireCache:function(){return g},schedulePrefetchTask:function(){return i}});let d=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},e=d,f=d,g=d,h=d,i=d,j=d,k=d,l=d,m=d;var n=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),o=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({}),p=function(a){return a[a.LoadingBoundary=0]="LoadingBoundary",a[a.PPR=1]="PPR",a[a.PPRRuntime=2]="PPRRuntime",a[a.Full=3]="Full",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},23053,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{generateSegmentsFromPatch:function(){return u},handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=a.r(20873),e=a.r(74685),f=a.r(77159),g=a.r(51748),h=a.r(9381),i=a.r(71094),j=a.r(88347),k=a.r(88632),l=a.r(97768),m=a.r(35399),n=a.r(451),o=a.r(39118),p=a.r(57051),q=a.r(98629),r=a.r(45496),s=a.r(17522);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}a.r(29442),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},95828,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=a.r(74685),e=a.r(51748),f=a.r(71094),g=a.r(23053),h=a.r(97768),i=a.r(88632),j=a.r(451);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},52869,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=a.r(74685),e=a.r(37042);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}a.r(57051),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88850,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=a.r(23053);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54050,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function a(b){let[c,e]=b;if(Array.isArray(c)&&("di"===c[2]||"ci"===c[2])||"string"==typeof c&&(0,d.isInterceptionRouteAppPath)(c))return!0;if(e){for(let b in e)if(a(e[b]))return!0}return!1}}});let d=a.r(18099);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},84496,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=a.r(20873),e=a.r(74685),f=a.r(51748),g=a.r(71094),h=a.r(23053),i=a.r(88632),j=a.r(10089),k=a.r(451),l=a.r(88850),m=a.r(54050),n=a.r(30719);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}a.r(29442),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},41970,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),a.r(20873),a.r(74685),a.r(51748),a.r(71094),a.r(23053),a.r(88632),a.r(97768),a.r(451),a.r(88850),a.r(54050);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},63333,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"assignLocation",{enumerable:!0,get:function(){return e}});let d=a.r(38243);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},22713,(a,b,c)=>{"use strict";function d(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function e(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{extractInfoFromServerReferenceId:function(){return d},omitUnusedArgs:function(){return e}})},41633,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverActionReducer",{enumerable:!0,get:function(){return E}});let d=a.r(20611),e=a.r(1722),f=a.r(37909),g=a.r(57997),h=a.r(38783),i=a.r(88347),j=a.r(63333),k=a.r(74685),l=a.r(23053),m=a.r(51748),n=a.r(71094),o=a.r(88632),p=a.r(10089),q=a.r(451),r=a.r(54050),s=a.r(88850),t=a.r(30719),u=a.r(22129),v=a.r(22099),w=a.r(11026),x=a.r(98629),y=a.r(16749),z=a.r(78897),A=a.r(22713);a.r(29442);let B=h.createFromFetch;async function C(a,b,c){let i,k,l,m,{actionId:n,actionArgs:o}=c,p=(0,h.createTemporaryReferenceSet)(),q=(0,A.extractInfoFromServerReferenceId)(n),r="use-cache"===q.type?(0,A.omitUnusedArgs)(o,q):o,s=await (0,h.encodeReply)(r,{temporaryReferences:p}),t=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:n,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,u.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:s});if("1"===t.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(new g.UnrecognizedActionError('Server Action "'+n+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let v=t.headers.get("x-action-redirect"),[x,y]=(null==v?void 0:v.split(";"))||[];switch(y){case"push":i=w.RedirectType.push;break;case"replace":i=w.RedirectType.replace;break;default:i=void 0}let z=!!t.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(t.headers.get("x-action-revalidated")||"[[],0,0]");k={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){k=D}let C=x?(0,j.assignLocation)(x,new URL(a.canonicalUrl,window.location.href)):void 0,E=t.headers.get("content-type"),F=!!(E&&E.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!F&&!C)throw Object.defineProperty(Error(t.status>=400&&"text/plain"===E?await t.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(F){let a=await B(Promise.resolve(t),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:p});l=C?void 0:a.a,m=(0,u.normalizeFlightData)(a.f)}else l=void 0,m=void 0;return{actionResult:l,actionFlightData:m,redirectLocation:C,redirectType:i,revalidatedParts:k,isPrerender:z}}let D={paths:[],tag:!1,cookie:!1};function E(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,r.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,h=Date.now();return C(a,g,b).then(async j=>{let r,{actionResult:u,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=j;if(B&&(C===w.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=r=(0,k.createHrefFromUrl)(B,!1)),!A)return(c(u),B)?(0,l.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(u),(0,l.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:i,seedData:j,head:k,isRootRender:o}=d;if(!o)return console.log("SERVER ACTION APPLY FAILED"),c(u),a;let v=(0,m.applyRouterStatePatchToTree)([""],f,i,r||a.canonicalUrl);if(null===v)return c(u),(0,s.handleSegmentMismatch)(a,b,i);if((0,n.isNavigatingToNewRootLayout)(f,v))return c(u),(0,l.handleExternalUrl)(a,e,r||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,q.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,p.fillLazyItemsTillLeafWithHead)(h,c,void 0,i,j,k,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,t.refreshInactiveParallelSegments)({navigatedAt:h,state:a,updatedTree:v,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=v,f=v}return B&&r?(F||((0,x.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,v.getRedirectError)((0,z.hasBasePath)(r)?(0,y.removeBasePath)(r):r,C||w.RedirectType.push))):c(u),(0,o.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54940,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"reducer",{enumerable:!0,get:function(){return d}}),a.r(88347),a.r(23053),a.r(95828),a.r(52869),a.r(84496),a.r(35399),a.r(41970),a.r(41633);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},44637,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=a.r(88347),e=a.r(54940),f=a.r(72131),g=a.r(67009);a.r(29442);let h=a.r(90841),i=a.r(38243),j=a.r(451),k=a.r(35399),l=a.r(16441);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},16441,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{IDLE_LINK_STATUS:function(){return i},PENDING_LINK_STATUS:function(){return h},mountFormInstance:function(){return r},mountLinkInstance:function(){return q},onLinkVisibilityChanged:function(){return t},onNavigationIntent:function(){return u},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return j},unmountLinkForCurrentNavigation:function(){return k},unmountPrefetchableInstance:function(){return s}}),a.r(44637);let d=a.r(451),e=a.r(29442),f=a.r(72131);a.r(88347),a.r(88644);let g=null,h={pending:!0},i={pending:!1};function j(a){(0,f.startTransition)(()=>{null==g||g.setOptimisticLinkStatus(i),null==a||a.setOptimisticLinkStatus(h),g=a})}function k(a){g===a&&(g=null)}let l="function"==typeof WeakMap?new WeakMap:new Map,m=new Set,n="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;t(b.target,a)}},{rootMargin:"200px"}):null;function o(a,b){void 0!==l.get(a)&&s(a),l.set(a,b),null!==n&&n.observe(a)}function p(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function q(a,b,c,d,e,f){if(e){let e=p(b);if(null!==e){let b={router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return o(a,b),b}}return{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function r(a,b,c,d){let e=p(b);null!==e&&o(a,{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function s(a){let b=l.get(a);if(void 0!==b){l.delete(a),m.delete(b);let c=b.prefetchTask;null!==c&&(0,e.cancelPrefetchTask)(c)}null!==n&&n.unobserve(a)}function t(a,b){let c=l.get(a);void 0!==c&&(c.isVisible=b,b?m.add(c):m.delete(c),v(c,e.PrefetchPriority.Default))}function u(a,b){let c=l.get(a);void 0!==c&&void 0!==c&&v(c,e.PrefetchPriority.Intent)}function v(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,e.cancelPrefetchTask)(c);return}}function w(a,b){for(let c of m){let d=c.prefetchTask;if(null!==d&&!(0,e.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,e.cancelPrefetchTask)(d);let f=(0,e.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,e.schedulePrefetchTask)(f,b,c.fetchStrategy,e.PrefetchPriority.Default,null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11762,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=a.r(46272),e=a.r(78897);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},9833,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"errorOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},38246,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return q},useLinkStatus:function(){return s}});let d=a.r(46058),e=a.r(87924),f=d._(a.r(72131)),g=a.r(43087),h=a.r(9270),i=a.r(8591),j=a.r(46272),k=a.r(38243);a.r(92434);let l=a.r(16441),m=a.r(11762),n=a.r(44637);a.r(9833);let o=a.r(29442);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){var b;let c,d,g,[q,s]=(0,f.useOptimistic)(l.IDLE_LINK_STATUS),t=(0,f.useRef)(null),{href:u,as:v,children:w,prefetch:x=null,passHref:y,replace:z,shallow:A,scroll:B,onClick:C,onMouseEnter:D,onTouchStart:E,legacyBehavior:F=!1,onNavigate:G,ref:H,unstable_dynamicOnHover:I,...J}=a;c=w,F&&("string"==typeof c||"number"==typeof c)&&(c=(0,e.jsx)("a",{children:c}));let K=f.default.useContext(h.AppRouterContext),L=!1!==x,M=!1!==x?null===(b=x)||"auto"===b?o.FetchStrategy.PPR:o.FetchStrategy.Full:o.FetchStrategy.PPR,{href:N,as:O}=f.default.useMemo(()=>{let a=p(u);return{href:a,as:v?p(v):a}},[u,v]);F&&(d=f.default.Children.only(c));let P=F?d&&"object"==typeof d&&d.ref:H,Q=f.default.useCallback(a=>(null!==K&&(t.current=(0,l.mountLinkInstance)(a,N,K,M,L,s)),()=>{t.current&&((0,l.unmountLinkForCurrentNavigation)(t.current),t.current=null),(0,l.unmountPrefetchableInstance)(a)}),[L,N,K,M,s]),R={ref:(0,i.useMergedRef)(Q,P),onClick(a){F||"function"!=typeof C||C(a),F&&d.props&&"function"==typeof d.props.onClick&&d.props.onClick(a),K&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,m.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,n.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,N,O,t,z,B,G))},onMouseEnter(a){F||"function"!=typeof D||D(a),F&&d.props&&"function"==typeof d.props.onMouseEnter&&d.props.onMouseEnter(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)},onTouchStart:function(a){F||"function"!=typeof E||E(a),F&&d.props&&"function"==typeof d.props.onTouchStart&&d.props.onTouchStart(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)}};return(0,j.isAbsoluteUrl)(O)?R.href=O:F&&!y&&("a"!==d.type||"href"in d.props)||(R.href=(0,k.addBasePath)(O)),g=F?f.default.cloneElement(d,R):(0,e.jsx)("a",{...J,...R,children:c}),(0,e.jsx)(r.Provider,{value:q,children:g})}let r=(0,f.createContext)(l.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)}];

//# sourceMappingURL=node_modules_c6edb212._.js.map