{"/_not-found/page": "app/_not-found/page.js", "/api/audit/users/route": "app/api/audit/users/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/departments/[id]/route": "app/api/departments/[id]/route.js", "/api/departments/route": "app/api/departments/route.js", "/api/email/config/route": "app/api/email/config/route.js", "/api/email/config/test/route": "app/api/email/config/test/route.js", "/api/email/config/test/send-ack/route": "app/api/email/config/test/send-ack/route.js", "/api/email/inbound/route": "app/api/email/inbound/route.js", "/api/me/route": "app/api/me/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/tickets/[id]/messages/route": "app/api/tickets/[id]/messages/route.js", "/api/tickets/[id]/route": "app/api/tickets/[id]/route.js", "/api/tickets/route": "app/api/tickets/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/users/route": "app/api/users/route.js", "/auth/login/page": "app/auth/login/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/dashboard/departments/page": "app/dashboard/departments/page.js", "/dashboard/email-config/page": "app/dashboard/email-config/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/reports/page": "app/dashboard/reports/page.js", "/dashboard/tickets/[id]/page": "app/dashboard/tickets/[id]/page.js", "/dashboard/tickets/new/page": "app/dashboard/tickets/new/page.js", "/dashboard/tickets/page": "app/dashboard/tickets/page.js", "/dashboard/users/page": "app/dashboard/users/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js"}