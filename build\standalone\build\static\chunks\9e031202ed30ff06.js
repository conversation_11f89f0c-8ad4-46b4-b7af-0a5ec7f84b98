(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,23750,e=>{"use strict";e.s(["Input",()=>s]);var t=e.i(43476),r=e.i(71645),a=e.i(47163);let s=r.forwardRef((e,r)=>{let{className:s,type:l,...i}=e;return(0,t.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:r,...i})});s.displayName="Input"},67881,e=>{"use strict";e.s(["Button",()=>s]);var t=e.i(43476),r=e.i(71645),a=e.i(47163);let s=r.forwardRef((e,r)=>{let{className:s,variant:l="default",size:i="default",...n}=e;return(0,t.jsx)("button",{className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[l],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i],s),ref:r,...n})});s.displayName="Button"},70065,e=>{"use strict";e.s(["Card",()=>s,"CardContent",()=>d,"CardDescription",()=>n,"CardHeader",()=>l,"CardTitle",()=>i]);var t=e.i(43476),r=e.i(71645),a=e.i(47163);let s=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm",s),...l})});s.displayName="Card";let l=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...l})});l.displayName="CardHeader";let i=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});i.displayName="CardTitle";let n=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600",s),...l})});n.displayName="CardDescription";let d=r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",s),...l})});d.displayName="CardContent",r.forwardRef((e,r)=>{let{className:s,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",s),...l})}).displayName="CardFooter"},91679,e=>{"use strict";e.s(["default",()=>d]);var t=e.i(43476),r=e.i(71645),a=e.i(18566),s=e.i(70065),l=e.i(23750),i=e.i(67881);let n=["Technical Support","Billing","Account","Feature Request","Data Management","Other"];function d(){let e=(0,a.useRouter)(),[d,o]=(0,r.useState)(""),[c,u]=(0,r.useState)(""),[m,p]=(0,r.useState)("MEDIUM"),[x,h]=(0,r.useState)("Technical Support"),[f,g]=(0,r.useState)([]),[b,j]=(0,r.useState)(""),[v,y]=(0,r.useState)([]),[N,w]=(0,r.useState)(""),[C,k]=(0,r.useState)(!1),[T,S]=(0,r.useState)(""),R=(0,r.useRef)(null);(0,r.useEffect)(()=>{(async()=>{try{let[e,t]=await Promise.all([fetch("/api/projects"),fetch("/api/departments?my=1")]),r=await e.json(),a=await t.json();e.ok?(g(r.projects||[]),(r.projects||[]).length>0&&j(r.projects[0].id)):S(r.error||"Failed to load projects"),t.ok?(y(a.departments||[]),(a.departments||[]).length>0&&w(a.departments[0].id)):T||S(a.error||"Failed to load departments")}catch(e){S("Failed to load projects/departments")}})()},[]);let P=async t=>{t.preventDefault(),k(!0),S("");try{var r;if(!b){S("Please select a project"),k(!1);return}if(!N){S("Please select a department"),k(!1);return}let t=new FormData;t.append("title",d),t.append("description",c),t.append("priority",m),t.append("category",x),t.append("projectId",b),t.append("departmentId",N);let a=null==(r=R.current)?void 0:r.files;if(a){let e=Math.min(a.length,3);for(let r=0;r<e;r++)t.append("images",a[r])}let s=await fetch("/api/tickets",{method:"POST",body:t}),l=await s.json();if(!s.ok)return void S(l.error||"Failed to create ticket");e.push("/dashboard/tickets"),e.refresh()}catch(e){S("Unexpected error")}finally{k(!1)}};return(0,t.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Create New Ticket"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Submit a new support request."})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsx)(s.CardTitle,{children:"Ticket Details"}),(0,t.jsx)(s.CardDescription,{children:"Provide details to help us resolve your issue."})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[T&&(0,t.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-3 rounded",children:T}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Project"}),(0,t.jsx)("select",{value:b,onChange:e=>j(e.target.value),className:"mt-1 w-full border rounded p-2",required:!0,children:f.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))}),0===f.length&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"No projects available. Ask an admin to assign you to a project."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Department"}),(0,t.jsx)("select",{value:N,onChange:e=>w(e.target.value),className:"mt-1 w-full border rounded p-2",required:!0,children:v.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))}),0===v.length&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"No departments configured. Ask an admin to add departments."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Title"}),(0,t.jsx)(l.Input,{value:d,onChange:e=>o(e.target.value),required:!0,className:"mt-1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,t.jsx)("textarea",{value:c,onChange:e=>u(e.target.value),required:!0,className:"mt-1 w-full border rounded p-2 h-32"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Priority"}),(0,t.jsxs)("select",{value:m,onChange:e=>p(e.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,t.jsx)("option",{children:"LOW"}),(0,t.jsx)("option",{children:"MEDIUM"}),(0,t.jsx)("option",{children:"HIGH"}),(0,t.jsx)("option",{children:"URGENT"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),(0,t.jsx)("select",{value:x,onChange:e=>h(e.target.value),className:"mt-1 w-full border rounded p-2",children:n.map(e=>(0,t.jsx)("option",{value:e,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Images (up to 3)"}),(0,t.jsx)("input",{ref:R,type:"file",accept:"image/*",multiple:!0,className:"mt-1 w-full border rounded p-2",name:"images",onChange:e=>{let t=e.currentTarget.files;t&&t.length>3&&(e.currentTarget.value="",alert("Please select up to 3 images"))}}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PNG, JPG, or GIF. Max 3 images."})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(i.Button,{type:"submit",disabled:C||0===f.length,children:C?"Creating...":"Create Ticket"}),(0,t.jsx)(i.Button,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})}}]);