(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,23750,e=>{"use strict";e.s(["Input",()=>r]);var s=e.i(43476),t=e.i(71645),a=e.i(47163);let r=t.forwardRef((e,t)=>{let{className:r,type:l,...i}=e;return(0,s.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});r.displayName="Input"},67881,e=>{"use strict";e.s(["Button",()=>r]);var s=e.i(43476),t=e.i(71645),a=e.i(47163);let r=t.forwardRef((e,t)=>{let{className:r,variant:l="default",size:i="default",...d}=e;return(0,s.jsx)("button",{className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[l],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i],r),ref:t,...d})});r.displayName="Button"},9855,e=>{"use strict";e.s(["default",()=>l]);var s=e.i(43476),t=e.i(71645),a=e.i(23750),r=e.i(67881);function l(e){let[l,i]=(0,t.useState)([]),[d,n]=(0,t.useState)([]),[c,o]=(0,t.useState)(""),[m,u]=(0,t.useState)(""),[h,p]=(0,t.useState)(""),[x,j]=(0,t.useState)("CUSTOMER"),[g,b]=(0,t.useState)([]),[f,v]=(0,t.useState)([]),[N,y]=(0,t.useState)(!1),[w,k]=(0,t.useState)(""),[S,A]=(0,t.useState)("");(0,t.useEffect)(()=>{(async()=>{try{let[e,s]=await Promise.all([fetch("/api/projects?all=1"),fetch("/api/departments")]),t=await e.json(),a=await s.json();e.ok?i(t.projects||[]):k(t.error||"Failed to load projects"),s.ok?n(a.departments||[]):w||k(a.error||"Failed to load departments")}catch(e){k("Failed to load projects/departments")}})()},[]);let C=async e=>{e.preventDefault(),y(!0),k(""),A("");try{let e=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c,fullName:m,password:h,role:x,projectIds:g,departmentIds:f})}),s=await e.json();if(!e.ok)return void k(s.error||"Failed to create user");A("User created"),o(""),u(""),p(""),j("CUSTOMER"),b([]),v([])}catch(e){k("Unexpected error")}finally{y(!1)}};return(0,s.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[w&&(0,s.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-3 rounded",children:w}),S&&(0,s.jsx)("div",{className:"bg-green-50 text-green-700 border border-green-200 p-3 rounded",children:S}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,s.jsx)(a.Input,{type:"email",value:c,onChange:e=>o(e.target.value),required:!0,className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)(a.Input,{value:m,onChange:e=>u(e.target.value),className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)(a.Input,{type:"password",value:h,onChange:e=>p(e.target.value),required:!0,className:"mt-1"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,s.jsxs)("select",{value:x,onChange:e=>j(e.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,s.jsx)("option",{value:"CUSTOMER",children:"Customer"}),(0,s.jsx)("option",{value:"AGENT",children:"Agent"}),(0,s.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,s.jsx)("option",{value:"ADMIN",children:"Admin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign Projects"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded",children:[l.map(e=>(0,s.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)("input",{type:"checkbox",checked:g.includes(e.id),onChange:()=>{var s;return s=e.id,void b(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])}}),(0,s.jsx)("span",{children:e.name})]},e.id)),0===l.length&&(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"No projects found"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign Departments"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded",children:[d.map(e=>(0,s.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)("input",{type:"checkbox",checked:f.includes(e.id),onChange:()=>{var s;return s=e.id,void v(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])}}),(0,s.jsx)("span",{children:e.name})]},e.id)),0===d.length&&(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"No departments found"})]})]}),(0,s.jsx)("div",{className:"flex gap-2",children:(0,s.jsx)(r.Button,{type:"submit",disabled:N,children:N?"Creating...":"Create User"})})]})}},58314,e=>{"use strict";e.s(["default",()=>l]);var s=e.i(43476),t=e.i(71645),a=e.i(67881);let r=[{key:"DASHBOARD_VIEW",label:"Dashboard View"},{key:"TICKETS_VIEW",label:"Tickets: View"},{key:"TICKETS_UPDATE",label:"Tickets: Update"},{key:"TICKETS_ASSIGN",label:"Tickets: Assign"},{key:"REPORTS_VIEW",label:"Reports: View"},{key:"DEPARTMENTS_MANAGE",label:"Departments: Manage"},{key:"PROJECTS_MANAGE",label:"Projects: Manage"},{key:"AUDIT_VIEW",label:"Audit: View"}];function l(){let[e,l]=(0,t.useState)([]),[i,d]=(0,t.useState)([]),[n,c]=(0,t.useState)([]),[o,m]=(0,t.useState)(!0),[u,h]=(0,t.useState)(""),[p,x]=(0,t.useState)(null),j=async()=>{m(!0),h("");try{let[e,s,t]=await Promise.all([fetch("/api/users"),fetch("/api/projects?all=1"),fetch("/api/departments")]),[a,r,i]=await Promise.all([e.json(),s.json(),t.json()]);if(!e.ok)throw Error(a.error||"Failed to load users");if(!s.ok)throw Error(r.error||"Failed to load projects");if(!t.ok)throw Error(i.error||"Failed to load departments");l(a.users||[]),d(r.projects||[]),c(i.departments||[])}catch(e){h((null==e?void 0:e.message)||"Failed to load")}finally{m(!1)}};(0,t.useEffect)(()=>{j()},[]);let g=async(e,s)=>{x(e.id),h("");try{let t=await fetch("/api/users/".concat(e.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to update user");await j()}catch(e){h((null==e?void 0:e.message)||"Failed to update user")}finally{x(null)}},b=(0,t.useMemo)(()=>new Set(i.map(e=>e.id)),[i]),f=(0,t.useMemo)(()=>new Set(n.map(e=>e.id)),[n]);return o?(0,s.jsx)("div",{className:"p-4",children:"Loading users..."}):u?(0,s.jsx)("div",{className:"p-4 text-red-600",children:u}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"font-semibold",children:"Manage Users"}),(0,s.jsx)(a.Button,{variant:"outline",onClick:j,children:"Refresh"})]}),(0,s.jsx)("div",{className:"p-4 overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full text-sm",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"text-left text-gray-500",children:[(0,s.jsx)("th",{className:"p-2",children:"Name"}),(0,s.jsx)("th",{className:"p-2",children:"Email"}),(0,s.jsx)("th",{className:"p-2",children:"Role"}),(0,s.jsx)("th",{className:"p-2",children:"Projects"}),(0,s.jsx)("th",{className:"p-2",children:"Departments"}),(0,s.jsx)("th",{className:"p-2",children:"Permissions"}),(0,s.jsx)("th",{className:"p-2",children:"Active"}),(0,s.jsx)("th",{className:"p-2 text-right",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>{let t=new Set(e.userProjects.map(e=>e.project.id).filter(e=>b.has(e))),l=new Set(e.userDepartments.map(e=>e.department.id).filter(e=>f.has(e)));return(0,s.jsxs)("tr",{className:"border-t",children:[(0,s.jsx)("td",{className:"p-2",children:e.fullName||"—"}),(0,s.jsx)("td",{className:"p-2",children:e.email}),(0,s.jsx)("td",{className:"p-2",children:(0,s.jsxs)("select",{value:e.role,onChange:s=>g(e,{role:s.target.value}),className:"border rounded p-1",children:[(0,s.jsx)("option",{value:"CUSTOMER",children:"Customer"}),(0,s.jsx)("option",{value:"AGENT",children:"Agent"}),(0,s.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,s.jsx)("option",{value:"ADMIN",children:"Admin"})]})}),(0,s.jsx)("td",{className:"p-2",children:(0,s.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:i.map(a=>(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:t.has(a.id),onChange:()=>{let s=new Set(t);s.has(a.id)?s.delete(a.id):s.add(a.id),g(e,{projectIds:Array.from(s)})}}),(0,s.jsx)("span",{className:"truncate",children:a.name})]},a.id))})}),(0,s.jsx)("td",{className:"p-2",children:(0,s.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:n.map(t=>(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:l.has(t.id),onChange:()=>{let s=new Set(l);s.has(t.id)?s.delete(t.id):s.add(t.id),g(e,{departmentIds:Array.from(s)})}}),(0,s.jsx)("span",{className:"truncate",children:t.name})]},t.id))})}),(0,s.jsx)("td",{className:"p-2",children:(0,s.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:r.map(t=>{let a=new Set((e.userPermissions||[]).map(e=>e.permission)),r=a.has(t.key);return(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:r,onChange:()=>{r?a.delete(t.key):a.add(t.key),g(e,{permissions:Array.from(a)})}}),(0,s.jsx)("span",{className:"truncate",children:t.label})]},t.key)})})}),(0,s.jsx)("td",{className:"p-2",children:(0,s.jsx)("input",{type:"checkbox",checked:e.isActive,onChange:()=>g(e,{isActive:!e.isActive})})}),(0,s.jsx)("td",{className:"p-2 text-right",children:(0,s.jsx)(a.Button,{size:"sm",disabled:p===e.id,variant:"outline",onClick:()=>g(e,{fullName:e.fullName||""}),children:p===e.id?"Saving...":"Save"})})]},e.id)})})]})})]})}}]);