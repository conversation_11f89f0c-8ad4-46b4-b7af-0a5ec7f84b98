{"version": 3, "file": "adapters.d.ts", "sourceRoot": "", "sources": ["src/adapters.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,GAAG,CAAA;AAC5C,OAAO,KAAK,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,qBAAqB,CAAA;AAEnE,MAAM,WAAW,WAAY,SAAQ,IAAI;IACvC,EAAE,EAAE,MAAM,CAAA;IACV,KAAK,EAAE,MAAM,CAAA;IACb,aAAa,EAAE,IAAI,GAAG,IAAI,CAAA;CAC3B;AAED,MAAM,WAAW,cAAe,SAAQ,OAAO;IAC7C,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,cAAc;IAC7B,0EAA0E;IAC1E,YAAY,EAAE,MAAM,CAAA;IACpB,uDAAuD;IACvD,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,IAAI,CAAA;CACd;AAED,MAAM,WAAW,iBAAiB;IAChC,UAAU,EAAE,MAAM,CAAA;IAClB,OAAO,EAAE,IAAI,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;CACd;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAM,WAAW,OAAO;IACtB,UAAU,CAAC,EACP,aAAa,CAAC,YAAY,CAAC,GAC3B,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,SAAS,CAAC,WAAW,CAAC,CAAC,CAAA;IAC/D,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IACvD,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IACjE,yFAAyF;IACzF,gBAAgB,CAAC,EAAE,CACjB,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,GAAG,mBAAmB,CAAC,KACtE,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IAClC,UAAU,CAAC,EAAE,CACX,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KACjD,SAAS,CAAC,WAAW,CAAC,CAAA;IAC3B,sBAAsB;IACtB,UAAU,CAAC,EAAE,CACX,MAAM,EAAE,MAAM,KACX,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAC9D,WAAW,CAAC,EACR,aAAa,CAAC,aAAa,CAAC,GAC5B,CAAC,CACC,OAAO,EAAE,cAAc,KACpB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAC,CAAA;IACtE,sBAAsB;IACtB,aAAa,CAAC,EACV,aAAa,CAAC,eAAe,CAAC,GAC9B,CAAC,CACC,iBAAiB,EAAE,IAAI,CACrB,cAAc,EACd,UAAU,GAAG,mBAAmB,CACjC,KACE,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,CAAC,CAAA;IAC/D,qDAAqD;IACrD,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE;QACxB,YAAY,EAAE,MAAM,CAAA;QACpB,MAAM,EAAE,MAAM,CAAA;QACd,OAAO,EAAE,IAAI,CAAA;KACd,KAAK,SAAS,CAAC,cAAc,CAAC,CAAA;IAC/B,iBAAiB,CAAC,EAAE,CAClB,YAAY,EAAE,MAAM,KACjB,SAAS,CAAC;QAAE,OAAO,EAAE,cAAc,CAAC;QAAC,IAAI,EAAE,WAAW,CAAA;KAAE,GAAG,IAAI,CAAC,CAAA;IACrE,aAAa,CAAC,EAAE,CACd,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,KACpE,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IACjD;;;;OAIG;IACH,aAAa,CAAC,EAAE,CACd,YAAY,EAAE,MAAM,KACjB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IACjE,uBAAuB,CAAC,EAAE,CACxB,iBAAiB,EAAE,iBAAiB,KACjC,SAAS,CAAC,iBAAiB,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IACpD;;;OAGG;IACH,oBAAoB,CAAC,EAAE,CAAC,MAAM,EAAE;QAC9B,UAAU,EAAE,MAAM,CAAA;QAClB,KAAK,EAAE,MAAM,CAAA;KACd,KAAK,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAA;CAC1C"}