module.exports=[40695,a=>{"use strict";a.s(["Button",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,variant:c="default",size:e="default",...f},g)=>(0,b.jsx)("button",{className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[c],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[e],a),ref:g,...f}));e.displayName="Button"},5522,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},9511,a=>{"use strict";a.s(["default",()=>f]);var b=a.i(87924),c=a.i(72131),d=a.i(40695),e=a.i(5522);function f(){let[a,f]=(0,c.useState)([]),[h,i]=(0,c.useState)(!0),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(null),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)(""),r=async()=>{i(!0),k("");try{let a=await fetch("/api/departments"),b=await a.json();if(!a.ok)throw Error(b.error||"Failed to load departments");f(b.departments||[])}catch(a){k(a?.message||"Failed to load")}finally{i(!1)}};(0,c.useEffect)(()=>{r()},[]);let s=async()=>{if(n.trim()){m("new"),k("");try{let a=await fetch("/api/departments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:n.trim(),description:p.trim()||null})}),b=await a.json();if(!a.ok)throw Error(b.error||"Failed to create");o(""),q(""),await r()}catch(a){k(a?.message||"Failed to create department")}finally{m(null)}}},t=async(a,b,c)=>{m(a),k("");try{let d=await fetch(`/api/departments/${a}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:b,description:c})}),e=await d.json();if(!d.ok)throw Error(e.error||"Failed to update");await r()}catch(a){k(a?.message||"Failed to update department")}finally{m(null)}},u=async a=>{if(confirm("Delete this department? This cannot be undone.")){m(a),k("");try{let b=await fetch(`/api/departments/${a}`,{method:"DELETE"}),c=await b.json().catch(()=>({success:b.ok}));if(!b.ok)throw Error(c.error||"Failed to delete");await r()}catch(a){k(a?.message||"Failed to delete department")}finally{m(null)}}};return h?(0,b.jsx)("div",{className:"p-4",children:"Loading departments..."}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,b.jsx)("h2",{className:"font-semibold",children:"Departments"}),(0,b.jsx)(d.Button,{variant:"outline",onClick:r,children:"Refresh"})]}),(0,b.jsxs)("div",{className:"p-4 space-y-4",children:[j&&(0,b.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:j}),(0,b.jsxs)("div",{className:"grid gap-2 sm:grid-cols-[240px_1fr_auto] items-start",children:[(0,b.jsx)(e.Input,{placeholder:"New department name",value:n,onChange:a=>o(a.target.value)}),(0,b.jsx)(e.Input,{placeholder:"Description (optional)",value:p,onChange:a=>q(a.target.value)}),(0,b.jsx)(d.Button,{onClick:s,disabled:"new"===l||!n.trim(),children:"new"===l?"Adding...":"Add"})]}),(0,b.jsxs)("div",{className:"divide-y",children:[a.map(a=>(0,b.jsx)(g,{d:a,onSave:t,onDelete:u,saving:l===a.id},a.id)),0===a.length&&(0,b.jsx)("div",{className:"text-sm text-gray-500",children:"No departments found."})]})]})]})}function g({d:a,onSave:f,onDelete:g,saving:h}){let[i,j]=(0,c.useState)(a.name),[k,l]=(0,c.useState)(a.description||""),m=i!==a.name||(k||"")!==(a.description||"");return(0,b.jsxs)("div",{className:"py-3 grid gap-2 sm:grid-cols-[240px_1fr_auto] items-center",children:[(0,b.jsx)(e.Input,{value:i,onChange:a=>j(a.target.value)}),(0,b.jsx)(e.Input,{value:k,onChange:a=>l(a.target.value)}),(0,b.jsxs)("div",{className:"flex gap-2 justify-end",children:[(0,b.jsx)(d.Button,{size:"sm",variant:"outline",onClick:()=>g(a.id),disabled:h,children:"Delete"}),(0,b.jsx)(d.Button,{size:"sm",onClick:()=>f(a.id,i,k),disabled:h||!m,children:h?"Saving...":"Save"})]})]})}}];

//# sourceMappingURL=_495bc5cc._.js.map