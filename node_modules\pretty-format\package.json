{"name": "pretty-format", "version": "3.8.0", "description": "Stringify any JavaScript value.", "license": "MIT", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "keywords": [], "repository": "https://github.com/thejameskyle/pretty-format.git", "bugs": "https://github.com/thejameskyle/pretty-format/issues", "homepage": "https://github.com/thejameskle/pretty-format", "scripts": {"test": "jest", "perf": "node perf/test.js"}, "jest": {"testEnvironment": "node", "verbose": true}, "devDependencies": {"chalk": "^1.1.3", "jest": "^15.1.1", "left-pad": "^1.1.1", "react": "15.3.0"}}