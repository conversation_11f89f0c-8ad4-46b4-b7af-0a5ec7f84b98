module.exports=[24005,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(87924),c=a.i(72131);function d({ticketId:a}){let[d,e]=(0,c.useState)(""),[f,g]=(0,c.useState)(!1),[h,i]=(0,c.useState)(""),j=async b=>{b.preventDefault(),g(!0),i("");try{let b=await fetch(`/api/tickets/${a}/messages`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:d})});if(!b.ok){let a=await b.json().catch(()=>({}));i(a.error||"Failed to send message");return}e(""),window.location.reload()}catch(a){i("Unexpected error")}finally{g(!1)}};return(0,b.jsxs)("form",{onSubmit:j,className:"space-y-3",children:[h&&(0,b.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:h}),(0,b.jsx)("textarea",{value:d,onChange:a=>e(a.target.value),placeholder:"Write a message...",className:"w-full border rounded p-2 h-28",required:!0}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsx)("button",{type:"submit",disabled:f,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:f?"Sending...":"Send"})})]})}},39312,a=>{"use strict";a.s(["default",()=>d]);var b=a.i(87924),c=a.i(72131);function d({ticketId:a,initial:d,role:e}){let[f,g]=(0,c.useState)(d.status),[h,i]=(0,c.useState)(d.priority),[j,k]=(0,c.useState)(d.departmentId||""),[l,m]=(0,c.useState)(d.assignedToId||""),[n,o]=(0,c.useState)([]),[p,q]=(0,c.useState)([]),[r,s]=(0,c.useState)(!1),[t,u]=(0,c.useState)("");(0,c.useEffect)(()=>{("ADMIN"===e||"MANAGER"===e)&&(async()=>{try{let a=await fetch("/api/departments"),b=await a.json();a.ok&&(q(b.departments||[]),!d.departmentId&&(b.departments||[]).length>0&&k(b.departments[0].id))}catch{}})()},[e]),(0,c.useEffect)(()=>{("ADMIN"===e||"MANAGER"===e)&&(async()=>{try{let a=new URLSearchParams;a.set("role","AGENT"),j&&a.set("departmentId",j),d.projectId&&a.set("projectId",d.projectId);let b=await fetch(`/api/users?${a.toString()}`),c=await b.json();if(b.ok){let a=(c.users||[]).map(a=>({id:a.id,fullName:a.fullName,email:a.email}));o(a),l&&!a.some(a=>a.id===l)&&m("")}}catch{}})()},[j,d.projectId,e]);let v=async b=>{b.preventDefault(),s(!0),u("");try{let b={status:f,priority:h};("ADMIN"===e||"MANAGER"===e)&&(b.departmentId=j,b.assignedToId=l||null);let c=await fetch(`/api/tickets/${a}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!c.ok){let a=await c.json().catch(()=>({}));u(a.error||"Failed to update ticket");return}window.location.reload()}catch(a){u("Unexpected error")}finally{s(!1)}};return(0,b.jsxs)("form",{onSubmit:v,className:"space-y-3",children:[t&&(0,b.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-2 rounded",children:t}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Status"}),(()=>{let a="AGENT"===e?["IN_PROGRESS","WAITING_FOR_CUSTOMER","RESOLVED"]:["OPEN","IN_PROGRESS","WAITING_FOR_CUSTOMER","WAITING_FOR_AGENT","RESOLVED","CLOSED","CANCELLED"];return(0,b.jsx)("select",{value:f,onChange:a=>g(a.target.value),className:"mt-1 w-full border rounded p-2",children:a.map(a=>(0,b.jsx)("option",{value:a,children:a},a))})})()]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Priority"}),(()=>{let a="AGENT"===e?["LOW","MEDIUM","HIGH"]:["LOW","MEDIUM","HIGH","URGENT"];return(0,b.jsx)("select",{value:h,onChange:a=>i(a.target.value),className:"mt-1 w-full border rounded p-2",children:a.map(a=>(0,b.jsx)("option",{value:a,children:a},a))})})()]}),("ADMIN"===e||"MANAGER"===e)&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Department"}),(0,b.jsx)("select",{value:j,onChange:a=>k(a.target.value),className:"mt-1 w-full border rounded p-2",children:p.map(a=>(0,b.jsx)("option",{value:a.id,children:a.name},a.id))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Assign To"}),(0,b.jsxs)("select",{value:l,onChange:a=>m(a.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,b.jsx)("option",{value:"",children:"Unassigned"}),n.map(a=>(0,b.jsx)("option",{value:a.id,children:a.fullName||a.email},a.id))]})]})]}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsx)("button",{type:"submit",disabled:r,className:"bg-gray-800 hover:bg-black text-white px-4 py-2 rounded",children:r?"Saving...":"Save"})})]})}}];

//# sourceMappingURL=src_app_dashboard_tickets_%5Bid%5D_e25c54f2._.js.map