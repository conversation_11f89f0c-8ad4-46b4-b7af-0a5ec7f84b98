module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},57695,(e,t,r)=>{},19341,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>k,"routeModule",()=>y,"serverHooks",()=>b,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>j],19341);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),s=e.i(61916),i=e.i(69741),o=e.i(16795),l=e.i(87718),d=e.i(95169),u=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),m=e.i(93695);e.i(52474);var R=e.i(220);e.s(["POST",()=>w],95745);var v=e.i(89171),f=e.i(15270),g=e.i(65800);async function w(e,t){let r=e.cookies.get("auth-token")?.value,a=r?(0,g.verifyToken)(r):null;if(!a)return v.NextResponse.json({error:"Unauthorized"},{status:401});let{params:n}=t,{id:s}=await n,i=await f.prisma.ticket.findUnique({where:{id:s}});if(!i||i.organizationId!==a.organizationId||i.projectId&&!await f.prisma.userProject.findUnique({where:{userId_projectId:{userId:a.id,projectId:i.projectId}}}))return v.NextResponse.json({error:"Not found"},{status:404});let{content:o,isInternal:l=!1}=await e.json();if(!o||"string"!=typeof o)return v.NextResponse.json({error:"Content is required"},{status:400});if(l&&"CUSTOMER"===a.role)return v.NextResponse.json({error:"Forbidden"},{status:403});let d=await f.prisma.ticketMessage.create({data:{content:o,isInternal:l,ticketId:i.id,authorId:a.id},include:{author:{select:{fullName:!0,email:!0}}}});return v.NextResponse.json({message:d},{status:201})}var E=e.i(95745);let y=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/tickets/[id]/messages/route",pathname:"/api/tickets/[id]/messages",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/tickets/[id]/messages/route.ts",nextConfigOutput:"export",userland:E}),{workAsyncStorage:C,workUnitAsyncStorage:j,serverHooks:b}=y;function k(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:j})}async function q(e,t,a){var v;let f="/api/tickets/[id]/messages/route";f=f.replace(/\/index$/,"")||"/";let g=await y.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:E,nextConfig:C,isDraftMode:j,prerenderManifest:b,routerServerContext:k,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,resolvedPathname:A}=g,T=(0,i.normalizeAppPath)(f),P=!!(b.dynamicRoutes[T]||b.routes[A]);if(P&&!j){let e=!!b.routes[A],t=b.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let I=null;!P||y.isDev||j||(I="/index"===(I=A)?"/":I);let O=!0===y.isDev||!P,_=P&&!O,S=e.method||"GET",U=(0,s.getTracer)(),H=U.getActiveScopeSpan(),M={params:E,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=C.experimental)?void 0:v.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>y.onRequestError(e,t,a,k)},sharedContext:{buildId:w}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>y.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${S} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await y.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:q})},k),t}},m=await y.handleResponse({req:e,nextConfig:C,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,responseGenerator:d,waitUntil:a.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(D,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};H?await o(H):await U.withPropagatedContext(e.headers,()=>U.trace(d.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await y.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:q})}),P)throw t;return await (0,p.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__c909a1ce._.js.map