module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},88947,(e,t,r)=>{t.exports=e.x("stream",()=>require("stream"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},15270,e=>{"use strict";e.s(["prisma",()=>r]);var t=e.i(29173);let r=globalThis.prisma??new t.PrismaClient({log:["warn","error"]})},78647,(e,t,r)=>{},10655,e=>{"use strict";e.s(["handler",()=>j,"patchFetch",()=>N,"routeModule",()=>E,"serverHooks",()=>b,"workAsyncStorage",()=>I,"workUnitAsyncStorage",()=>C],10655);var t=e.i(47909),r=e.i(74017),a=e.i(96250),i=e.i(59756),s=e.i(61916),n=e.i(69741),o=e.i(16795),d=e.i(87718),l=e.i(95169),p=e.i(47587),u=e.i(66012),c=e.i(70101),m=e.i(26937),f=e.i(10372),x=e.i(93695);e.i(52474);var h=e.i(220);e.s(["PATCH",()=>A,"runtime",()=>y],97613);var w=e.i(89171),R=e.i(15270),v=e.i(65800);let y="nodejs";async function A(e,t){let r=e.cookies.get("auth-token")?.value,a=r?(0,v.verifyToken)(r):null;if(!a)return w.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==a.role)return w.NextResponse.json({error:"Forbidden"},{status:403});let{role:i,isActive:s,projectIds:n,departmentIds:o,fullName:d,permissions:l}=await e.json()||{},{params:p}=t,{id:u}=await p,c=await R.prisma.user.findUnique({where:{id:u}});if(!c||c.organizationId!==a.organizationId)return w.NextResponse.json({error:"Not found"},{status:404});let m=await R.prisma.userProject.findMany({where:{userId:c.id},select:{projectId:!0}}),f=await R.prisma.userDepartment.findMany({where:{userId:c.id},select:{departmentId:!0}}),x=await R.prisma.userPermission.findMany({where:{userId:c.id},select:{permission:!0}}),h=m.map(e=>e.projectId),y=f.map(e=>e.departmentId),A=x.map(e=>e.permission),g=await R.prisma.user.update({where:{id:u},data:{...void 0!==d?{fullName:d}:{},..."boolean"==typeof s?{isActive:s}:{},...i&&["ADMIN","MANAGER","AGENT","CUSTOMER"].includes(i)?{role:i}:{}}});if(Array.isArray(n))for(let e of(await R.prisma.userProject.deleteMany({where:{userId:g.id}}),await R.prisma.project.findMany({where:{id:{in:n},organizationId:a.organizationId},select:{id:!0}})))await R.prisma.userProject.create({data:{userId:g.id,projectId:e.id}});if(Array.isArray(o))for(let e of(await R.prisma.userDepartment.deleteMany({where:{userId:g.id}}),await R.prisma.department.findMany({where:{id:{in:o},organizationId:a.organizationId},select:{id:!0}})))await R.prisma.userDepartment.create({data:{userId:g.id,departmentId:e.id}});if(Array.isArray(l))for(let e of(await R.prisma.userPermission.deleteMany({where:{userId:g.id}}),Array.from(new Set(l.filter(e=>"string"==typeof e)))))await R.prisma.userPermission.create({data:{userId:g.id,organizationId:a.organizationId,permission:e}});let E=e.headers.get("x-forwarded-for")?.split(",")[0]||null,I=[];if(i&&i!==c.role&&["ADMIN","MANAGER","AGENT","CUSTOMER"].includes(i)&&I.push({action:"USER_ROLE_CHANGE",before:{role:c.role},after:{role:i}}),"boolean"==typeof s&&s!==c.isActive&&I.push({action:"USER_ACTIVATION",before:{isActive:c.isActive},after:{isActive:s}}),Array.isArray(n)){let e=(await R.prisma.userProject.findMany({where:{userId:g.id},select:{projectId:!0}})).map(e=>e.projectId);I.push({action:"USER_PROJECT_SET",before:{projectIds:h},after:{projectIds:e}})}if(Array.isArray(o)){let e=(await R.prisma.userDepartment.findMany({where:{userId:g.id},select:{departmentId:!0}})).map(e=>e.departmentId);I.push({action:"USER_DEPARTMENT_SET",before:{departmentIds:y},after:{departmentIds:e}})}if(Array.isArray(l)){let e=(await R.prisma.userPermission.findMany({where:{userId:g.id},select:{permission:!0}})).map(e=>e.permission);I.push({action:"USER_PERMISSION_SET",before:{permissions:A},after:{permissions:e}})}for(let e of I)await R.prisma.userAuditLog.create({data:{organizationId:a.organizationId,actorId:a.id,targetUserId:g.id,action:e.action,before:e.before,after:e.after,ip:E||void 0}});return w.NextResponse.json({user:{id:g.id,email:g.email,fullName:g.fullName,role:g.role,isActive:g.isActive}})}var g=e.i(97613);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:""},distDir:"build",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/users/[id]/route.ts",nextConfigOutput:"",userland:g}),{workAsyncStorage:I,workUnitAsyncStorage:C,serverHooks:b}=E;function N(){return(0,a.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:C})}async function j(e,t,a){var w;let R="/api/users/[id]/route";R=R.replace(/\/index$/,"")||"/";let v=await E.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:y,params:A,nextConfig:g,isDraftMode:I,prerenderManifest:C,routerServerContext:b,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,resolvedPathname:T}=v,P=(0,n.normalizeAppPath)(R),M=!!(C.dynamicRoutes[P]||C.routes[T]);if(M&&!I){let e=!!C.routes[T],t=C.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new x.NoFallbackError}let S=null;!M||E.isDev||I||(S="/index"===(S=T)?"/":S);let _=!0===E.isDev||!M,q=M&&!_,O=e.method||"GET",U=(0,s.getTracer)(),k=U.getActiveScopeSpan(),D={params:A,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!g.experimental.cacheComponents,authInterrupts:!!g.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,i.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(w=g.experimental)?void 0:w.cacheLife,isRevalidate:q,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,b)},sharedContext:{buildId:y}},H=new o.NodeNextRequest(e),z=new o.NodeNextResponse(t),F=d.NextRequestAdapter.fromNodeNextRequest(H,(0,d.signalFromNodeResponse)(t));try{let n=async r=>E.handle(F,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let i=a.get("next.route");if(i){let e=`${O} ${i}`;r.setAttributes({"next.route":i,"http.route":i,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,i.getRequestMeta)(e,"minimalMode")&&N&&j&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await n(s);e.fetchMetrics=D.renderOpts.fetchMetrics;let d=D.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let l=D.renderOpts.collectedTags;if(!M)return await (0,u.sendResponse)(H,z,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[f.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=f.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,a=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=f.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:h.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:N})},b),t}},x=await E.handleResponse({req:e,nextConfig:g,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:j,responseGenerator:l,waitUntil:a.waitUntil});if(!M)return null;if((null==x||null==(o=x.value)?void 0:o.kind)!==h.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==x||null==(d=x.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":x.isMiss?"MISS":x.isStale?"STALE":"HIT"),I&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let w=(0,c.fromNodeOutgoingHttpHeaders)(x.value.headers);return(0,i.getRequestMeta)(e,"minimalMode")&&M||w.delete(f.NEXT_CACHE_TAGS_HEADER),!x.cacheControl||t.getHeader("Cache-Control")||w.get("Cache-Control")||w.set("Cache-Control",(0,m.getCacheControlHeader)(x.cacheControl)),await (0,u.sendResponse)(H,z,new Response(x.value.body,{headers:w,status:x.value.status||200})),null};k?await o(k):await U.withPropagatedContext(e.headers,()=>U.trace(l.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},o))}catch(t){if(k||t instanceof x.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:q,isOnDemandRevalidate:N})}),M)throw t;return await (0,u.sendResponse)(H,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__50fa7ed3._.js.map