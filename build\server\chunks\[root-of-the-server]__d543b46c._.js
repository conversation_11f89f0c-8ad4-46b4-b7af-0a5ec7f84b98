module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},79543,(e,t,r)=>{},83932,e=>{"use strict";e.s(["handler",()=>b,"patchFetch",()=>A,"routeModule",()=>E,"serverHooks",()=>C,"workAsyncStorage",()=>w,"workUnitAsyncStorage",()=>y],83932);var t=e.i(47909),r=e.i(74017),a=e.i(96250),n=e.i(59756),o=e.i(61916),s=e.i(69741),i=e.i(16795),l=e.i(87718),u=e.i(95169),d=e.i(47587),p=e.i(66012),c=e.i(70101),x=e.i(26937),h=e.i(10372),R=e.i(93695);e.i(52474);var v=e.i(220);e.s(["POST",()=>g],3703);var m=e.i(89171);async function g(e){let t=m.NextResponse.json({success:!0});return t.cookies.set("auth-token","",{httpOnly:!0,path:"/",expires:new Date(0)}),t}var f=e.i(3703);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:""},distDir:"build",relativeProjectDir:"",resolvedPagePath:"[project]/src/app/api/auth/logout/route.ts",nextConfigOutput:"",userland:f}),{workAsyncStorage:w,workUnitAsyncStorage:y,serverHooks:C}=E;function A(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:y})}async function b(e,t,a){var m;let g="/api/auth/logout/route";g=g.replace(/\/index$/,"")||"/";let f=await E.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:C,isDraftMode:A,prerenderManifest:b,routerServerContext:N,isOnDemandRevalidate:k,revalidateOnlyGenerated:O,resolvedPathname:P}=f,T=(0,s.normalizeAppPath)(g),j=!!(b.dynamicRoutes[T]||b.routes[P]);if(j&&!A){let e=!!b.routes[P],t=b.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new R.NoFallbackError}let q=null;!j||E.isDev||A||(q="/index"===(q=P)?"/":q);let _=!0===E.isDev||!j,S=j&&!_,H=e.method||"GET",U=(0,o.getTracer)(),I=U.getActiveScopeSpan(),M={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(m=C.experimental)?void 0:m.cacheLife,isRevalidate:S,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,N)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let s=async r=>E.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==u.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${H} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),i=async o=>{var i,l;let u=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&k&&O&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await s(o);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let u=M.renderOpts.collectedTags;if(!j)return await (0,p.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(i.headers);u&&(t[h.NEXT_CACHE_TAGS_HEADER]=u),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:k})},N),t}},R=await E.handleResponse({req:e,nextConfig:C,cacheKey:q,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:O,responseGenerator:u,waitUntil:a.waitUntil});if(!j)return null;if((null==R||null==(i=R.value)?void 0:i.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==R||null==(l=R.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,c.fromNodeOutgoingHttpHeaders)(R.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&j||m.delete(h.NEXT_CACHE_TAGS_HEADER),!R.cacheControl||t.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,x.getCacheControlHeader)(R.cacheControl)),await (0,p.sendResponse)(D,$,new Response(R.value.body,{headers:m,status:R.value.status||200})),null};I?await i(I):await U.withPropagatedContext(e.headers,()=>U.trace(u.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:o.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},i))}catch(t){if(I||t instanceof R.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:k})}),j)throw t;return await (0,p.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__d543b46c._.js.map