"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = Mailchimp;
function Mailchimp(options) {
  return {
    id: "mailchimp",
    name: "<PERSON>chi<PERSON>",
    type: "oauth",
    authorization: "https://login.mailchimp.com/oauth2/authorize",
    token: "https://login.mailchimp.com/oauth2/token",
    userinfo: "https://login.mailchimp.com/oauth2/metadata",
    profile(profile) {
      return {
        id: profile.login.login_id,
        name: profile.accountname,
        email: profile.login.email,
        image: null
      };
    },
    style: {
      logo: "/mailchimp.svg",
      bg: "#000",
      text: "#fff"
    },
    options
  };
}