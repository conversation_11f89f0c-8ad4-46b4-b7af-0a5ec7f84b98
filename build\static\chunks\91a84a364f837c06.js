(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,33525,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},98183,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function l(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return a}})},95057,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return l}});let n=e.r(90809)._(e.r(98183)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",l=e.pathname||"",i=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),i&&"#"!==i[0]&&(i="#"+i),c&&"?"!==c[0]&&(c="?"+c),""+a+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+i}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},18581,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(71645);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18967,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return v},NormalizeError:function(){return g},PageNotFoundError:function(){return b},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return s},getLocationOrigin:function(){return i},getURL:function(){return u},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return y}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>a.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=i();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class b extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},73668,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=e.r(18967),o=e.r(52817);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},84508,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},22016,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return g},useLinkStatus:function(){return v}});let n=e.r(90809),o=e.r(43476),a=n._(e.r(71645)),l=e.r(95057),i=e.r(8372),u=e.r(18581),s=e.r(18967),c=e.r(5550);e.r(33525);let f=e.r(91949),d=e.r(73668),h=e.r(99781);e.r(84508);let p=e.r(65165);function m(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function g(e){var t;let r,n,l,[g,v]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),x=(0,a.useRef)(null),{href:y,as:w,children:j,prefetch:E=null,passHref:P,replace:k,shallow:O,scroll:M,onClick:N,onMouseEnter:_,onTouchStart:C,legacyBehavior:L=!1,onNavigate:S,ref:T,unstable_dynamicOnHover:R,...A}=e;r=j,L&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let U=a.default.useContext(i.AppRouterContext),I=!1!==E,B=!1!==E?null===(t=E)||"auto"===t?p.FetchStrategy.PPR:p.FetchStrategy.Full:p.FetchStrategy.PPR,{href:F,as:D}=a.default.useMemo(()=>{let e=m(y);return{href:e,as:w?m(w):e}},[y,w]);L&&(n=a.default.Children.only(r));let W=L?n&&"object"==typeof n&&n.ref:T,K=a.default.useCallback(e=>(null!==U&&(x.current=(0,f.mountLinkInstance)(e,F,U,B,I,v)),()=>{x.current&&((0,f.unmountLinkForCurrentNavigation)(x.current),x.current=null),(0,f.unmountPrefetchableInstance)(e)}),[I,F,U,B,v]),V={ref:(0,u.useMergedRef)(K,W),onClick(e){L||"function"!=typeof N||N(e),L&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),U&&(e.defaultPrevented||function(e,t,r,n,o,l,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,F,D,x,k,M,S))},onMouseEnter(e){L||"function"!=typeof _||_(e),L&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),U&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===R)},onTouchStart:function(e){L||"function"!=typeof C||C(e),L&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),U&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===R)}};return(0,s.isAbsoluteUrl)(D)?V.href=D:L&&!P&&("a"!==n.type||"href"in n.props)||(V.href=(0,c.addBasePath)(D)),l=L?a.default.cloneElement(n,V):(0,o.jsx)("a",{...A,...V,children:r}),(0,o.jsx)(b.Provider,{value:g,children:l})}let b=(0,a.createContext)(f.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(b);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},18566,(e,t,r)=>{t.exports=e.r(76562)},97722,e=>{"use strict";e.s(["default",()=>n]);var t=e.i(43476),r=e.i(18566);function n(e){let{className:n=""}=e,o=(0,r.useRouter)(),a=async()=>{try{await fetch("/api/auth/logout",{method:"POST"})}catch(e){}o.push("/auth/login"),o.refresh()};return(0,t.jsx)("button",{onClick:a,className:n,children:"Sign out"})}},8492,e=>{"use strict";e.s(["Sidebar",()=>d],8492);var t=e.i(43476),r=e.i(22016),n=e.i(18566),o=e.i(47163),a=e.i(71645);let l=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}),i=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"}))}),u=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}),s=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),c=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}),f=a.forwardRef(function(e,t){let{title:r,titleId:n,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},o),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});function d(e){let{user:a,profile:d}=e,h=(0,n.usePathname)(),p=((null==a?void 0:a.role)||(null==d?void 0:d.role)||"").toString().toLowerCase(),m=(null==a?void 0:a.fullName)||(null==d?void 0:d.full_name)||(null==a?void 0:a.email)||(null==d?void 0:d.email)||"User",g=[{name:"Dashboard",href:"/dashboard",icon:l},{name:"Tickets",href:"/dashboard/tickets",icon:i},{name:"New Ticket",href:"/dashboard/tickets/new",icon:f}];return("admin"===p||"agent"===p||"manager"===p)&&g.push({name:"Reports",href:"/dashboard/reports",icon:s}),"admin"===p&&g.push({name:"Users",href:"/dashboard/users",icon:u}),"admin"===p&&(g.push({name:"Departments",href:"/dashboard/departments",icon:c}),g.push({name:"Email configuration",href:"/dashboard/email-config",icon:c})),(0,t.jsx)("aside",{className:"hidden md:flex md:w-64 md:flex-col",children:(0,t.jsxs)("div",{className:"flex flex-col flex-grow pt-5 bg-slate-900 text-slate-200 border-r border-slate-800 overflow-y-auto",children:[(0,t.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,t.jsx)("h1",{className:"text-xl font-semibold text-white",children:"WeCare"})}),(0,t.jsx)("div",{className:"mt-6 flex-grow flex flex-col",children:(0,t.jsx)("nav",{className:"flex-1 px-2 space-y-1",children:g.map(e=>{let n=h===e.href;return(0,t.jsxs)(r.default,{href:e.href,className:(0,o.cn)(n?"bg-slate-800 border-blue-500 text-white":"border-transparent text-slate-300 hover:bg-slate-800 hover:text-white","group flex items-center px-2 py-2 text-sm font-medium border-l-4 rounded-r-md"),children:[(0,t.jsx)(e.icon,{className:(0,o.cn)(n?"text-blue-400":"text-slate-400 group-hover:text-slate-300","mr-3 h-5 w-5"),"aria-hidden":"true"}),e.name]},e.name)})})}),(0,t.jsx)("div",{className:"flex-shrink-0 p-4 border-t border-slate-800",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-medium text-white",children:(m||"").charAt(0)||"U"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-slate-100",children:m}),p&&(0,t.jsx)("p",{className:"text-xs text-slate-400 capitalize",children:p})]})]})})]})})}}]);