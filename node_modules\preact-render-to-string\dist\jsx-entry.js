var t;t=function(t,e){if("function"!=typeof Symbol){var n=0;Symbol=function(t){return"@@"+t+ ++n},Symbol.for=function(t){return"@@"+t}}var r=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,o=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,i=/[\s\n\\/='"\0<>]/,a=/^xlink:?./,u=/["&<]/;function l(t){if(!1===u.test(t+=""))return t;for(var e=0,n=0,r="",o="";n<t.length;n++){switch(t.charCodeAt(n)){case 34:o="&quot;";break;case 38:o="&amp;";break;case 60:o="&lt;";break;default:continue}n!==e&&(r+=t.slice(e,n)),r+=o,e=n+1}return n!==e&&(r+=t.slice(e,n)),r}var f=function(t,e){return String(t).replace(/(\n+)/g,"$1"+(e||"\t"))},s=function(t,e,n){return String(t).length>(e||40)||!n&&-1!==String(t).indexOf("\n")||-1!==String(t).indexOf("<")},c={},p=/([A-Z])/g;function d(t){var e="";for(var n in t){var o=t[n];null!=o&&""!==o&&(e&&(e+=" "),e+="-"==n[0]?n:c[n]||(c[n]=n.replace(p,"-$1").toLowerCase()),e="number"==typeof o&&!1===r.test(n)?e+": "+o+"px;":e+": "+o+";")}return e||void 0}function v(t,e){return Array.isArray(e)?e.reduce(v,t):null!=e&&!1!==e&&t.push(e),t}function _(){this.__d=!0}function y(t,e){return{__v:t,context:e,props:t.props,setState:_,forceUpdate:_,__d:!0,__h:[]}}function g(t,e){var n=t.contextType,r=n&&e[n.__c];return null!=n?r?r.props.value:n.__:e}var b=[];function m(t,n,r,u,c,p){if(null==t||"boolean"==typeof t)return"";if("object"!=typeof t)return"function"==typeof t?"":l(t);var _=r.pretty,h=_&&"string"==typeof _?_:"\t";if(Array.isArray(t)){for(var j="",x=0;x<t.length;x++)_&&x>0&&(j+="\n"),j+=m(t[x],n,r,u,c,p);return j}if(void 0!==t.constructor)return"";var S,k=t.type,A=t.props,O=!1;if("function"==typeof k){if(O=!0,!r.shallow||!u&&!1!==r.renderRootComponent){if(k===e.Fragment){var w=[];return v(w,t.props.children),m(w,n,r,!1!==r.shallowHighOrder,c,p)}var F,C=t.__c=y(t,n);e.options.__b&&e.options.__b(t);var E=e.options.__r;if(k.prototype&&"function"==typeof k.prototype.render){var M=g(k,n);(C=t.__c=new k(A,M)).__v=t,C._dirty=C.__d=!0,C.props=A,null==C.state&&(C.state={}),null==C._nextState&&null==C.__s&&(C._nextState=C.__s=C.state),C.context=M,k.getDerivedStateFromProps?C.state=Object.assign({},C.state,k.getDerivedStateFromProps(C.props,C.state)):C.componentWillMount&&(C.componentWillMount(),C.state=C._nextState!==C.state?C._nextState:C.__s!==C.state?C.__s:C.state),E&&E(t),F=C.render(C.props,C.state,C.context)}else for(var H=g(k,n),N=0;C.__d&&N++<25;)C.__d=!1,E&&E(t),F=k.call(t.__c,A,H);return C.getChildContext&&(n=Object.assign({},n,C.getChildContext())),e.options.diffed&&e.options.diffed(t),m(F,n,r,!1!==r.shallowHighOrder,c,p)}k=(S=k).displayName||S!==Function&&S.name||function(t){var e=(Function.prototype.toString.call(t).match(/^\s*function\s+([^( ]+)/)||"")[1];if(!e){for(var n=-1,r=b.length;r--;)if(b[r]===t){n=r;break}n<0&&(n=b.push(t)-1),e="UnnamedComponent"+n}return e}(S)}var D,I,L="<"+k;if(A){var W=Object.keys(A);r&&!0===r.sortAttributes&&W.sort();for(var $=0;$<W.length;$++){var P=W[$],T=A[P];if("children"!==P){if(!i.test(P)&&(r&&r.allAttributes||"key"!==P&&"ref"!==P&&"__self"!==P&&"__source"!==P)){if("defaultValue"===P)P="value";else if("defaultChecked"===P)P="checked";else if("defaultSelected"===P)P="selected";else if("className"===P){if(void 0!==A.class)continue;P="class"}else c&&a.test(P)&&(P=P.toLowerCase().replace(/^xlink:?/,"xlink:"));if("htmlFor"===P){if(A.for)continue;P="for"}"style"===P&&T&&"object"==typeof T&&(T=d(T)),"a"===P[0]&&"r"===P[1]&&"boolean"==typeof T&&(T=String(T));var U=r.attributeHook&&r.attributeHook(P,T,n,r,O);if(U||""===U)L+=U;else if("dangerouslySetInnerHTML"===P)I=T&&T.__html;else if("textarea"===k&&"value"===P)D=T;else if((T||0===T||""===T)&&"function"!=typeof T){if(!(!0!==T&&""!==T||(T=P,r&&r.xml))){L=L+" "+P;continue}if("value"===P){if("select"===k){p=T;continue}"option"===k&&p==T&&void 0===A.selected&&(L+=" selected")}L=L+" "+P+'="'+l(T)+'"'}}}else D=T}}if(_){var R=L.replace(/\n\s*/," ");R===L||~R.indexOf("\n")?_&&~L.indexOf("\n")&&(L+="\n"):L=R}if(L+=">",i.test(k))throw new Error(k+" is not a valid HTML tag name in "+L);var J,V=o.test(k)||r.voidElements&&r.voidElements.test(k),q=[];if(I)_&&s(I)&&(I="\n"+h+f(I,h)),L+=I;else if(null!=D&&v(J=[],D).length){for(var z=_&&~L.indexOf("\n"),B=!1,G=0;G<J.length;G++){var Z=J[G];if(null!=Z&&!1!==Z){var K=m(Z,n,r,!0,"svg"===k||"foreignObject"!==k&&c,p);if(_&&!z&&s(K)&&(z=!0),K)if(_){var Q=K.length>0&&"<"!=K[0];B&&Q?q[q.length-1]+=K:q.push(K),B=Q}else q.push(K)}}if(_&&z)for(var X=q.length;X--;)q[X]="\n"+h+f(q[X],h)}if(q.length||I)L+=q.join("");else if(r&&r.xml)return L.substring(0,L.length-1)+" />";return!V||J||I?(_&&~L.indexOf("\n")&&(L+="\n"),L=L+"</"+k+">"):L=L.replace(/>$/," />"),L}var h={shallow:!0};x.render=x;var j=[];function x(t,n,r){n=n||{};var o=e.options.__s;e.options.__s=!0;var i,a=e.h(e.Fragment,null);return a.__k=[t],i=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?m(t,n,r):F(t,n,!1,void 0,a),e.options.__c&&e.options.__c(t,j),e.options.__s=o,j.length=0,i}function S(t){return null==t||"boolean"==typeof t?null:"string"==typeof t||"number"==typeof t||"bigint"==typeof t?e.h(null,null,t):t}function k(t,e){return"className"===t?"class":"htmlFor"===t?"for":"defaultValue"===t?"value":"defaultChecked"===t?"checked":"defaultSelected"===t?"selected":e&&a.test(t)?t.toLowerCase().replace(/^xlink:?/,"xlink:"):t}function A(t,e){return"style"===t&&null!=e&&"object"==typeof e?d(e):"a"===t[0]&&"r"===t[1]&&"boolean"==typeof e?String(e):e}var O=Array.isArray,w=Object.assign;function F(t,n,r,a,u){if(null==t||!0===t||!1===t||""===t)return"";if("object"!=typeof t)return"function"==typeof t?"":l(t);if(O(t)){var f="";u.__k=t;for(var s=0;s<t.length;s++)f+=F(t[s],n,r,a,u),t[s]=S(t[s]);return f}if(void 0!==t.constructor)return"";t.__=u,e.options.__b&&e.options.__b(t);var c=t.type,p=t.props;if("function"==typeof c){var d;if(c===e.Fragment)d=p.children;else{d=c.prototype&&"function"==typeof c.prototype.render?function(t,n){var r=t.type,o=g(r,n),i=new r(t.props,o);t.__c=i,i.__v=t,i.__d=!0,i.props=t.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=w({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var a=e.options.__r;return a&&a(t),i.render(i.props,i.state,i.context)}(t,n):function(t,n){var r,o=y(t,n),i=g(t.type,n);t.__c=o;for(var a=e.options.__r,u=0;o.__d&&u++<25;)o.__d=!1,a&&a(t),r=t.type.call(o,t.props,i);return r}(t,n);var v=t.__c;v.getChildContext&&(n=w({},n,v.getChildContext()))}var _=F(d=null!=d&&d.type===e.Fragment&&null==d.key?d.props.children:d,n,r,a,t);return e.options.diffed&&e.options.diffed(t),t.__=void 0,e.options.unmount&&e.options.unmount(t),_}var b,m,h="<";if(h+=c,p)for(var j in b=p.children,p){var x=p[j];if(!("key"===j||"ref"===j||"__self"===j||"__source"===j||"children"===j||"className"===j&&"class"in p||"htmlFor"===j&&"for"in p||i.test(j)))if(x=A(j=k(j,r),x),"dangerouslySetInnerHTML"===j)m=x&&x.__html;else if("textarea"===c&&"value"===j)b=x;else if((x||0===x||""===x)&&"function"!=typeof x){if(!0===x||""===x){x=j,h=h+" "+j;continue}if("value"===j){if("select"===c){a=x;continue}"option"!==c||a!=x||"selected"in p||(h+=" selected")}h=h+" "+j+'="'+l(x)+'"'}}var C=h;if(h+=">",i.test(c))throw new Error(c+" is not a valid HTML tag name in "+h);var E="",M=!1;if(m)E+=m,M=!0;else if("string"==typeof b)E+=l(b),M=!0;else if(O(b)){t.__k=b;for(var H=0;H<b.length;H++){var N=b[H];if(b[H]=S(N),null!=N&&!1!==N){var D=F(N,n,"svg"===c||"foreignObject"!==c&&r,a,t);D&&(E+=D,M=!0)}}}else if(null!=b&&!1!==b&&!0!==b){t.__k=[S(b)];var I=F(b,n,"svg"===c||"foreignObject"!==c&&r,a,t);I&&(E+=I,M=!0)}if(e.options.diffed&&e.options.diffed(t),t.__=void 0,e.options.unmount&&e.options.unmount(t),M)h+=E;else if(o.test(c))return C+" />";return h+"</"+c+">"}x.shallowRender=function(t,e){return x(t,e,h)};var C=/(\\|\"|\')/g,E=Object.prototype.toString,M=Date.prototype.toISOString,H=Error.prototype.toString,N=RegExp.prototype.toString,D=Symbol.prototype.toString,I=/^Symbol\((.*)\)(.*)$/,L=/\n/gi,W=Object.getOwnPropertySymbols||function(t){return[]};function $(t){return"[object Array]"===t||"[object ArrayBuffer]"===t||"[object DataView]"===t||"[object Float32Array]"===t||"[object Float64Array]"===t||"[object Int8Array]"===t||"[object Int16Array]"===t||"[object Int32Array]"===t||"[object Uint8Array]"===t||"[object Uint8ClampedArray]"===t||"[object Uint16Array]"===t||"[object Uint32Array]"===t}function P(t){return""===t.name?"[Function anonymous]":"[Function "+t.name+"]"}function T(t){return D.call(t).replace(I,"Symbol($1)")}function U(t){return"["+H.call(t)+"]"}function R(t){if(!0===t||!1===t)return""+t;if(void 0===t)return"undefined";if(null===t)return"null";var e=typeof t;if("number"===e)return function(t){return t!=+t?"NaN":0===t&&1/t<0?"-0":""+t}(t);if("string"===e)return'"'+function(t){return t.replace(C,"\\$1")}(t)+'"';if("function"===e)return P(t);if("symbol"===e)return T(t);var n=E.call(t);return"[object WeakMap]"===n?"WeakMap {}":"[object WeakSet]"===n?"WeakSet {}":"[object Function]"===n||"[object GeneratorFunction]"===n?P(t,min):"[object Symbol]"===n?T(t):"[object Date]"===n?M.call(t):"[object Error]"===n?U(t):"[object RegExp]"===n?N.call(t):"[object Arguments]"===n&&0===t.length?"Arguments []":$(n)&&0===t.length?t.constructor.name+" []":t instanceof Error&&U(t)}function J(t,e,n,r,o,i,a,u,l,f){var s="";if(t.length){s+=o;for(var c=n+e,p=0;p<t.length;p++)s+=c+z(t[p],e,c,r,o,i,a,u,l,f),p<t.length-1&&(s+=","+r);s+=o+n}return"["+s+"]"}function V(t,e,n,r,o,i,a,u,l,f){if((i=i.slice()).indexOf(t)>-1)return"[Circular]";i.push(t);var s=++u>a;if(!s&&t.toJSON&&"function"==typeof t.toJSON)return z(t.toJSON(),e,n,r,o,i,a,u,l,f);var c=E.call(t);return"[object Arguments]"===c?s?"[Arguments]":function(t,e,n,r,o,i,a,u,l,f){return(f?"":"Arguments ")+J(t,e,n,r,o,i,a,u,l,f)}(t,e,n,r,o,i,a,u,l,f):$(c)?s?"[Array]":function(t,e,n,r,o,i,a,u,l,f){return(f?"":t.constructor.name+" ")+J(t,e,n,r,o,i,a,u,l,f)}(t,e,n,r,o,i,a,u,l,f):"[object Map]"===c?s?"[Map]":function(t,e,n,r,o,i,a,u,l,f){var s="Map {",c=t.entries(),p=c.next();if(!p.done){s+=o;for(var d=n+e;!p.done;)s+=d+z(p.value[0],e,d,r,o,i,a,u,l,f)+" => "+z(p.value[1],e,d,r,o,i,a,u,l,f),(p=c.next()).done||(s+=","+r);s+=o+n}return s+"}"}(t,e,n,r,o,i,a,u,l,f):"[object Set]"===c?s?"[Set]":function(t,e,n,r,o,i,a,u,l,f){var s="Set {",c=t.entries(),p=c.next();if(!p.done){s+=o;for(var d=n+e;!p.done;)s+=d+z(p.value[1],e,d,r,o,i,a,u,l,f),(p=c.next()).done||(s+=","+r);s+=o+n}return s+"}"}(t,e,n,r,o,i,a,u,l,f):"object"==typeof t?s?"[Object]":function(t,e,n,r,o,i,a,u,l,f){var s=(f?"":t.constructor?t.constructor.name+" ":"Object ")+"{",c=Object.keys(t).sort(),p=W(t);if(p.length&&(c=c.filter(function(t){return!("symbol"==typeof t||"[object Symbol]"===E.call(t))}).concat(p)),c.length){s+=o;for(var d=n+e,v=0;v<c.length;v++){var _=c[v];s+=d+z(_,e,d,r,o,i,a,u,l,f)+": "+z(t[_],e,d,r,o,i,a,u,l,f),v<c.length-1&&(s+=","+r)}s+=o+n}return s+"}"}(t,e,n,r,o,i,a,u,l,f):void 0}function q(t,e,n,r,o,i,a,u,l,f){for(var s,c=!1,p=0;p<l.length;p++)if((s=l[p]).test(t)){c=!0;break}return!!c&&s.print(t,function(t){return z(t,e,n,r,o,i,a,u,l,f)},function(t){var r=n+e;return r+t.replace(L,"\n"+r)},{edgeSpacing:o,spacing:r})}function z(t,e,n,r,o,i,a,u,l,f){return R(t)||q(t,e,n,r,o,i,a,u,l,f)||V(t,e,n,r,o,i,a,u,l,f)}var B={indent:2,min:!1,maxDepth:Infinity,plugins:[]};function G(t){return new Array(t+1).join(" ")}var Z={test:function(t){return t&&"object"==typeof t&&"type"in t&&"props"in t&&"key"in t},print:function(t,e,n){return x(t,Z.context,Z.opts)}},K={plugins:[Z]},Q={attributeHook:function(t,e,n,r,o){var i=typeof e;if("dangerouslySetInnerHTML"===t)return!1;if(null==e||"function"===i&&!r.functions)return"";if(r.skipFalseAttributes&&!o&&(!1===e||("class"===t||"style"===t)&&""===e))return"";var a="string"==typeof r.pretty?r.pretty:"\t";return"string"!==i?("function"!==i||r.functionNames?(Z.context=n,Z.opts=r,~(e=function(t,e){var n,r;e?(function(t){if(Object.keys(t).forEach(function(t){if(!B.hasOwnProperty(t))throw new Error("prettyFormat: Invalid option: "+t)}),t.min&&void 0!==t.indent&&0!==t.indent)throw new Error("prettyFormat: Cannot run with min option and indent")}(e),e=function(t){var e={};return Object.keys(B).forEach(function(n){return e[n]=t.hasOwnProperty(n)?t[n]:B[n]}),e.min&&(e.indent=0),e}(e)):e=B;var o=e.min?" ":"\n",i=e.min?"":"\n";if(e&&e.plugins.length){var a=q(t,n=G(e.indent),"",o,i,r=[],e.maxDepth,0,e.plugins,e.min);if(a)return a}return R(t)||(n||(n=G(e.indent)),r||(r=[]),V(t,n,"",o,i,r,e.maxDepth,0,e.plugins,e.min))}(e,K)).indexOf("\n")&&(e=f("\n"+e,a)+"\n")):e="Function",f("\n"+t+"={"+e+"}",a)):"\n"+a+t+'="'+l(e)+'"'},jsx:!0,xml:!1,functions:!0,functionNames:!0,skipFalseAttributes:!0,pretty:"  "};function X(t,e,n,r){return x(t,e,n=Object.assign({},Q,n||{}))}t.default=X,t.render=X},"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("preact")):"function"==typeof define&&define.amd?define(["exports","preact"],t):t(self.preactRenderToString={},(void 0).preact);
//# sourceMappingURL=jsx.js.map
