module.exports=[5522,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},40695,a=>{"use strict";a.s(["Button",()=>e]);var b=a.i(87924),c=a.i(72131),d=a.i(97895);let e=c.forwardRef(({className:a,variant:c="default",size:e="default",...f},g)=>(0,b.jsx)("button",{className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-blue-600 text-white hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",ghost:"hover:bg-gray-100 hover:text-gray-900",link:"text-blue-600 underline-offset-4 hover:underline"}[c],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[e],a),ref:g,...f}));e.displayName="Button"},82664,a=>{"use strict";a.s(["default",()=>f]);var b=a.i(87924),c=a.i(72131),d=a.i(5522),e=a.i(40695);function f(a){let[f,g]=(0,c.useState)([]),[h,i]=(0,c.useState)([]),[j,k]=(0,c.useState)(""),[l,m]=(0,c.useState)(""),[n,o]=(0,c.useState)(""),[p,q]=(0,c.useState)("CUSTOMER"),[r,s]=(0,c.useState)([]),[t,u]=(0,c.useState)([]),[v,w]=(0,c.useState)(!1),[x,y]=(0,c.useState)(""),[z,A]=(0,c.useState)("");(0,c.useEffect)(()=>{(async()=>{try{let[a,b]=await Promise.all([fetch("/api/projects?all=1"),fetch("/api/departments")]),c=await a.json(),d=await b.json();a.ok?g(c.projects||[]):y(c.error||"Failed to load projects"),b.ok?i(d.departments||[]):x||y(d.error||"Failed to load departments")}catch{y("Failed to load projects/departments")}})()},[]);let B=async a=>{a.preventDefault(),w(!0),y(""),A("");try{let a=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:j,fullName:l,password:n,role:p,projectIds:r,departmentIds:t})}),b=await a.json();if(!a.ok)return void y(b.error||"Failed to create user");A("User created"),k(""),m(""),o(""),q("CUSTOMER"),s([]),u([])}catch{y("Unexpected error")}finally{w(!1)}};return(0,b.jsxs)("form",{onSubmit:B,className:"space-y-4",children:[x&&(0,b.jsx)("div",{className:"bg-red-50 text-red-700 border border-red-200 p-3 rounded",children:x}),z&&(0,b.jsx)("div",{className:"bg-green-50 text-green-700 border border-green-200 p-3 rounded",children:z}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,b.jsx)(d.Input,{type:"email",value:j,onChange:a=>k(a.target.value),required:!0,className:"mt-1"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,b.jsx)(d.Input,{value:l,onChange:a=>m(a.target.value),className:"mt-1"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,b.jsx)(d.Input,{type:"password",value:n,onChange:a=>o(a.target.value),required:!0,className:"mt-1"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,b.jsxs)("select",{value:p,onChange:a=>q(a.target.value),className:"mt-1 w-full border rounded p-2",children:[(0,b.jsx)("option",{value:"CUSTOMER",children:"Customer"}),(0,b.jsx)("option",{value:"AGENT",children:"Agent"}),(0,b.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,b.jsx)("option",{value:"ADMIN",children:"Admin"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign Projects"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded",children:[f.map(a=>(0,b.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,b.jsx)("input",{type:"checkbox",checked:r.includes(a.id),onChange:()=>{var b;return b=a.id,void s(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])}}),(0,b.jsx)("span",{children:a.name})]},a.id)),0===f.length&&(0,b.jsx)("div",{className:"text-xs text-gray-500",children:"No projects found"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assign Departments"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 p-3 border rounded",children:[h.map(a=>(0,b.jsxs)("label",{className:"flex items-center gap-2 text-sm",children:[(0,b.jsx)("input",{type:"checkbox",checked:t.includes(a.id),onChange:()=>{var b;return b=a.id,void u(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])}}),(0,b.jsx)("span",{children:a.name})]},a.id)),0===h.length&&(0,b.jsx)("div",{className:"text-xs text-gray-500",children:"No departments found"})]})]}),(0,b.jsx)("div",{className:"flex gap-2",children:(0,b.jsx)(e.Button,{type:"submit",disabled:v,children:v?"Creating...":"Create User"})})]})}},75362,a=>{"use strict";a.s(["default",()=>f]);var b=a.i(87924),c=a.i(72131),d=a.i(40695);let e=[{key:"DASHBOARD_VIEW",label:"Dashboard View"},{key:"TICKETS_VIEW",label:"Tickets: View"},{key:"TICKETS_UPDATE",label:"Tickets: Update"},{key:"TICKETS_ASSIGN",label:"Tickets: Assign"},{key:"REPORTS_VIEW",label:"Reports: View"},{key:"DEPARTMENTS_MANAGE",label:"Departments: Manage"},{key:"PROJECTS_MANAGE",label:"Projects: Manage"},{key:"AUDIT_VIEW",label:"Audit: View"}];function f(){let[a,f]=(0,c.useState)([]),[g,h]=(0,c.useState)([]),[i,j]=(0,c.useState)([]),[k,l]=(0,c.useState)(!0),[m,n]=(0,c.useState)(""),[o,p]=(0,c.useState)(null),q=async()=>{l(!0),n("");try{let[a,b,c]=await Promise.all([fetch("/api/users"),fetch("/api/projects?all=1"),fetch("/api/departments")]),[d,e,g]=await Promise.all([a.json(),b.json(),c.json()]);if(!a.ok)throw Error(d.error||"Failed to load users");if(!b.ok)throw Error(e.error||"Failed to load projects");if(!c.ok)throw Error(g.error||"Failed to load departments");f(d.users||[]),h(e.projects||[]),j(g.departments||[])}catch(a){n(a?.message||"Failed to load")}finally{l(!1)}};(0,c.useEffect)(()=>{q()},[]);let r=async(a,b)=>{p(a.id),n("");try{let c=await fetch(`/api/users/${a.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),d=await c.json();if(!c.ok)throw Error(d.error||"Failed to update user");await q()}catch(a){n(a?.message||"Failed to update user")}finally{p(null)}},s=(0,c.useMemo)(()=>new Set(g.map(a=>a.id)),[g]),t=(0,c.useMemo)(()=>new Set(i.map(a=>a.id)),[i]);return k?(0,b.jsx)("div",{className:"p-4",children:"Loading users..."}):m?(0,b.jsx)("div",{className:"p-4 text-red-600",children:m}):(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,b.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,b.jsx)("h2",{className:"font-semibold",children:"Manage Users"}),(0,b.jsx)(d.Button,{variant:"outline",onClick:q,children:"Refresh"})]}),(0,b.jsx)("div",{className:"p-4 overflow-x-auto",children:(0,b.jsxs)("table",{className:"min-w-full text-sm",children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{className:"text-left text-gray-500",children:[(0,b.jsx)("th",{className:"p-2",children:"Name"}),(0,b.jsx)("th",{className:"p-2",children:"Email"}),(0,b.jsx)("th",{className:"p-2",children:"Role"}),(0,b.jsx)("th",{className:"p-2",children:"Projects"}),(0,b.jsx)("th",{className:"p-2",children:"Departments"}),(0,b.jsx)("th",{className:"p-2",children:"Permissions"}),(0,b.jsx)("th",{className:"p-2",children:"Active"}),(0,b.jsx)("th",{className:"p-2 text-right",children:"Actions"})]})}),(0,b.jsx)("tbody",{children:a.map(a=>{let c=new Set(a.userProjects.map(a=>a.project.id).filter(a=>s.has(a))),f=new Set(a.userDepartments.map(a=>a.department.id).filter(a=>t.has(a)));return(0,b.jsxs)("tr",{className:"border-t",children:[(0,b.jsx)("td",{className:"p-2",children:a.fullName||"—"}),(0,b.jsx)("td",{className:"p-2",children:a.email}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsxs)("select",{value:a.role,onChange:b=>r(a,{role:b.target.value}),className:"border rounded p-1",children:[(0,b.jsx)("option",{value:"CUSTOMER",children:"Customer"}),(0,b.jsx)("option",{value:"AGENT",children:"Agent"}),(0,b.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,b.jsx)("option",{value:"ADMIN",children:"Admin"})]})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:g.map(d=>(0,b.jsxs)("label",{className:"flex items-center gap-2",children:[(0,b.jsx)("input",{type:"checkbox",checked:c.has(d.id),onChange:()=>{let b=new Set(c);b.has(d.id)?b.delete(d.id):b.add(d.id),r(a,{projectIds:Array.from(b)})}}),(0,b.jsx)("span",{className:"truncate",children:d.name})]},d.id))})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:i.map(c=>(0,b.jsxs)("label",{className:"flex items-center gap-2",children:[(0,b.jsx)("input",{type:"checkbox",checked:f.has(c.id),onChange:()=>{let b=new Set(f);b.has(c.id)?b.delete(c.id):b.add(c.id),r(a,{departmentIds:Array.from(b)})}}),(0,b.jsx)("span",{className:"truncate",children:c.name})]},c.id))})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("div",{className:"max-w-xs grid grid-cols-1 gap-1",children:e.map(c=>{let d=new Set((a.userPermissions||[]).map(a=>a.permission)),e=d.has(c.key);return(0,b.jsxs)("label",{className:"flex items-center gap-2",children:[(0,b.jsx)("input",{type:"checkbox",checked:e,onChange:()=>{e?d.delete(c.key):d.add(c.key),r(a,{permissions:Array.from(d)})}}),(0,b.jsx)("span",{className:"truncate",children:c.label})]},c.key)})})}),(0,b.jsx)("td",{className:"p-2",children:(0,b.jsx)("input",{type:"checkbox",checked:a.isActive,onChange:()=>r(a,{isActive:!a.isActive})})}),(0,b.jsx)("td",{className:"p-2 text-right",children:(0,b.jsx)(d.Button,{size:"sm",disabled:o===a.id,variant:"outline",onClick:()=>r(a,{fullName:a.fullName||""}),children:o===a.id?"Saving...":"Save"})})]},a.id)})})]})})]})}}];

//# sourceMappingURL=_431a52b1._.js.map