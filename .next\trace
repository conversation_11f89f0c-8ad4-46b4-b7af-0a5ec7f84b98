[{"name": "generate-buildid", "duration": 194, "timestamp": 465100900727, "id": 4, "parentId": 1, "tags": {}, "startTime": 1758729144294, "traceId": "9e8864a9819b514a"}, {"name": "load-custom-routes", "duration": 409, "timestamp": 465100901034, "id": 5, "parentId": 1, "tags": {}, "startTime": 1758729144295, "traceId": "9e8864a9819b514a"}, {"name": "create-dist-dir", "duration": 2349, "timestamp": 465101061255, "id": 6, "parentId": 1, "tags": {}, "startTime": 1758729144455, "traceId": "9e8864a9819b514a"}, {"name": "create-pages-mapping", "duration": 826, "timestamp": 465101211925, "id": 7, "parentId": 1, "tags": {}, "startTime": 1758729144606, "traceId": "9e8864a9819b514a"}, {"name": "collect-app-files", "duration": 138598, "timestamp": 465101212901, "id": 8, "parentId": 1, "tags": {}, "startTime": 1758729144606, "traceId": "9e8864a9819b514a"}, {"name": "create-app-mapping", "duration": 11451, "timestamp": 465101351566, "id": 9, "parentId": 1, "tags": {}, "startTime": 1758729144745, "traceId": "9e8864a9819b514a"}, {"name": "create-app-layouts", "duration": 308, "timestamp": 465101363076, "id": 10, "parentId": 1, "tags": {}, "startTime": 1758729144757, "traceId": "9e8864a9819b514a"}, {"name": "collect-default-files", "duration": 14183, "timestamp": 465101366920, "id": 12, "parentId": 1, "tags": {}, "startTime": 1758729144761, "traceId": "9e8864a9819b514a"}, {"name": "generate-route-types", "duration": 75435, "timestamp": 465101364254, "id": 11, "parentId": 1, "tags": {}, "startTime": 1758729144758, "traceId": "9e8864a9819b514a"}, {"name": "public-dir-conflict-check", "duration": 4381, "timestamp": 465101440041, "id": 13, "parentId": 1, "tags": {}, "startTime": 1758729144834, "traceId": "9e8864a9819b514a"}, {"name": "generate-routes-manifest", "duration": 4765, "timestamp": 465101444984, "id": 14, "parentId": 1, "tags": {}, "startTime": 1758729144839, "traceId": "9e8864a9819b514a"}, {"name": "run-turbopack-compiler", "duration": 10033957, "timestamp": 465101461422, "id": 16, "parentId": 1, "tags": {}, "startTime": 1758729144855, "traceId": "9e8864a9819b514a"}, {"name": "verify-and-lint", "duration": 9706104, "timestamp": 465111623838, "id": 19, "parentId": 1, "tags": {}, "startTime": 1758729155017, "traceId": "9e8864a9819b514a"}, {"name": "verify-typescript-setup", "duration": 14874106, "timestamp": 465111572147, "id": 18, "parentId": 1, "tags": {}, "startTime": 1758729154966, "traceId": "9e8864a9819b514a"}, {"name": "check-static-error-page", "duration": 8072, "timestamp": 465126501574, "id": 22, "parentId": 21, "tags": {}, "startTime": 1758729169895, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 9664, "timestamp": 465126584133, "id": 23, "parentId": 21, "tags": {"page": "/_app"}, "startTime": 1758729169978, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 7947, "timestamp": 465126585883, "id": 25, "parentId": 21, "tags": {"page": "/_document"}, "startTime": 1758729169979, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 8424, "timestamp": 465126585693, "id": 24, "parentId": 21, "tags": {"page": "/_error"}, "startTime": 1758729169979, "traceId": "9e8864a9819b514a"}, {"name": "is-page-static", "duration": 1167472, "timestamp": 465126608735, "id": 56, "parentId": 29, "tags": {}, "startTime": 1758729170002, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 1188620, "timestamp": 465126587688, "id": 29, "parentId": 21, "tags": {"page": "/api/auth/logout"}, "startTime": 1758729169981, "traceId": "9e8864a9819b514a"}, {"name": "is-page-static", "duration": 1191405, "timestamp": 465126608259, "id": 55, "parentId": 28, "tags": {}, "startTime": 1758729170002, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 1212174, "timestamp": 465126587529, "id": 28, "parentId": 21, "tags": {"page": "/api/auth/login"}, "startTime": 1758729169981, "traceId": "9e8864a9819b514a"}, {"name": "is-page-static", "duration": 1251976, "timestamp": 465126609756, "id": 59, "parentId": 39, "tags": {}, "startTime": 1758729170003, "traceId": "9e8864a9819b514a"}, {"name": "check-page", "duration": 1273270, "timestamp": 465126588520, "id": 39, "parentId": 21, "tags": {"page": "/api/tickets/[id]"}, "startTime": 1758729169982, "traceId": "9e8864a9819b514a"}, {"name": "static-check", "duration": 1361018, "timestamp": 465126500781, "id": 21, "parentId": 1, "tags": {}, "startTime": 1758729169894, "traceId": "9e8864a9819b514a"}, {"name": "next-build", "duration": 27528420, "timestamp": 465100333390, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "true", "version": "15.5.2", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1758729143727, "traceId": "9e8864a9819b514a"}]